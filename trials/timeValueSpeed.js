
const { bm } = require('../dist'),
	TIMES = 1000000

function test1(time) {
	return Number(time.replace(':', ''))
}

function test2(time) {
	return Number(`${time.slice(0,2)}${time.slice(3)}`)
}

// ------ 78ms
const start1 = bm.mark()
for (let i=0; i < TIMES; i++) {
	test1('12:34')
}
console.log('replace(): ', bm.diff(start1))

// ------- 9.5ms
const start2 = bm.mark()
for (let i = 0; i < TIMES; i++) {
	test2('12:34')
}
console.log('slice(): ', bm.diff(start2))

