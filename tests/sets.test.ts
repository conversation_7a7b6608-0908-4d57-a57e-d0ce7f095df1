import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Sets } from '../src/index'

const { intersection, union, difference, symmetricDifference } = Sets

describe('Sets', () => {
  describe('intersection', () => {
    it('should return the intersection of multiple sets', () => {
      const set1 = new Set([1, 2, 3, 4])
      const set2 = new Set([3, 4, 5, 6])
      const set3 = new Set([2, 3, 4, 7])

      const result = intersection([set1, set2, set3])

      assert.deepEqual([...result], [3, 4])
    })

    it('should return the first set if only one set is provided', () => {
      const set = new Set([1, 2, 3])

      const result = intersection([set])

      assert.deepEqual([...result], [1, 2, 3])
    })

    it('should return an empty set if there is no intersection', () => {
      const set1 = new Set([1, 2])
      const set2 = new Set([3, 4])

      const result = intersection([set1, set2])

      assert.deepEqual([...result], [])
    })

    it('should handle sets with different types of elements', () => {
      const set1 = new Set([1, 'a', true])
      const set2 = new Set([1, 'a', false])

      const result = intersection([set1, set2])

      assert.deepEqual([...result], [1, 'a'])
    })
  })

  describe('union', () => {
    it('should return the union of multiple sets', () => {
      const set1 = new Set([1, 2, 3])
      const set2 = new Set([3, 4, 5])

      const result = union([set1, set2])

      assert.deepEqual([...result].sort(), [1, 2, 3, 4, 5])
    })

    it('should return an empty set if all input sets are empty', () => {
      const set1 = new Set([])
      const set2 = new Set([])

      const result = union([set1, set2])

      assert.deepEqual([...result], [])
    })

    it('should handle sets with different types of elements', () => {
      const set1 = new Set([1, 'a'])
      const set2 = new Set([true, false])

      const result = union([set1, set2])

      assert.deepEqual([...result].sort(), [1, 'a', true, false].sort())
    })
  })

  describe('difference', () => {
    it('should return the difference between sets', () => {
      const set1 = new Set([1, 2, 3, 4, 5])
      const set2 = new Set([3, 4])
      const set3 = new Set([5])

      const result = difference(set1, [set2, set3])

      assert.deepEqual([...result].sort(), [1, 2])
    })

    it('should return the original set if difference sets are empty', () => {
      const set1 = new Set([1, 2, 3])
      const set2 = new Set([])

      const result = difference(set1, [set2])

      assert.deepEqual([...result].sort(), [1, 2, 3])
    })
  })

  describe('symmetricDifference', () => {
    it('should return the symmetric difference between two sets', () => {
      const set1 = new Set([1, 2, 3])
      const set2 = new Set([2, 3, 4])

      const result = symmetricDifference(set1, set2)

      assert.deepEqual([...result].sort(), [1, 4])
    })

    it('should return the union if sets have no common elements', () => {
      const set1 = new Set([1, 2])
      const set2 = new Set([3, 4])

      const result = symmetricDifference(set1, set2)

      assert.deepEqual([...result].sort(), [1, 2, 3, 4])
    })

    it('should return an empty set if both sets are identical', () => {
      const set1 = new Set([1, 2, 3])
      const set2 = new Set([1, 2, 3])

      const result = symmetricDifference(set1, set2)

      assert.deepEqual([...result], [])
    })
  })
})
