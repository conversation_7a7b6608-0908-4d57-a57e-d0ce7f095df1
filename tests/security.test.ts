import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Security } from '../src/index'

const { Jwt, safeCredentials, generateKey, generateCode, randomString } = Security

describe('Security', () => {
  describe('Jwt', () => {
    const secret = 'test-secret'
    const jwt = new Jwt(secret)
    const payload = { userId: '123', role: 'admin' }

    it('should encode and decode JWT tokens', () => {
      const token = jwt.encode(payload)
      assert.ok(token)
      assert.ok(typeof token === 'string')
      
      const decoded = jwt.decode(token)
      assert.ok(decoded)
      assert.deepEqual(decoded?.payload, payload)
    })

    it('should verify valid tokens', () => {
      const token = jwt.encode(payload)
      const isValid = jwt.verify(token)
      assert.equal(isValid, true)
    })

    it('should reject invalid tokens', () => {
      const invalidToken = 'invalid.token.string'
      const isValid = jwt.verify(invalidToken)
      assert.equal(isValid, false)
    })

    it('should use static decode method', () => {
      const token = jwt.encode(payload)
      const decoded = Jwt.decode(token)
      assert.ok(decoded)
      assert.deepEqual(decoded?.payload, payload)
    })
  })

  describe('safeCredentials', () => {
    it('should validate valid credentials', () => {
      const credentials = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        tenant: 'test-tenant'
      }
      const result = safeCredentials(credentials)
      assert.equal(result, true)
    })

    it('should validate partial credentials', () => {
      const credentials = {
        username: 'testuser',
        email: '<EMAIL>'
      }
      const result = safeCredentials(credentials)
      assert.equal(result, true)
    })

    it('should reject invalid email', () => {
      const credentials = {
        username: 'testuser',
        email: 'invalid-email',
        password: 'password123'
      }
      const result = safeCredentials(credentials)
      assert.equal(result, false)
    })

    it('should reject non-string username', () => {
      const credentials = {
        username: 123,
        email: '<EMAIL>'
      }
      const result = safeCredentials(credentials)
      assert.equal(result, false)
    })

    it('should reject non-string password', () => {
      const credentials = {
        username: 'testuser',
        password: 123
      }
      const result = safeCredentials(credentials)
      assert.equal(result, false)
    })

    it('should reject non-string tenant', () => {
      const credentials = {
        username: 'testuser',
        tenant: 123
      }
      const result = safeCredentials(credentials)
      assert.equal(result, false)
    })

    it('should handle empty credentials', () => {
      const result = safeCredentials()
      assert.equal(result, true)
    })
  })

  describe('generateKey', () => {
    it('should generate a key of specified length', () => {
      const name = 'test-name'
      const keyLength = 16
      const key = generateKey(name, keyLength)
      assert.equal(key.length, keyLength)
    })

    it('should remove vowels and non-word characters', () => {
      const name = 'Test Name With Vowels!'
      const key = generateKey(name, 20)
      assert.ok(!key.includes('a'))
      assert.ok(!key.includes('e'))
      assert.ok(!key.includes('i'))
      assert.ok(!key.includes('o'))
      assert.ok(!key.includes('u'))
      assert.ok(!key.includes(' '))
      assert.ok(!key.includes('!'))
    })

    it('should handle empty name', () => {
      const key = generateKey('', 16)
      assert.equal(key.length, 16)
    })

    it('should use default length if not specified', () => {
      const key = generateKey('test')
      assert.equal(key.length, 16)
    })
  })

  describe('generateCode', () => {
    it('should generate a numeric code of specified length', () => {
      const length = 6
      const code = generateCode(length)
      assert.equal(code.length, length)
      assert.ok(/^[1-9]+$/.test(code))
    })

    it('should not include 0 in generated code', () => {
      const code = generateCode(20)
      assert.ok(!code.includes('0'))
    })
  })

  describe('randomString', () => {
    it('should generate a random string of specified length', () => {
      const length = 10
      const str = randomString(length)
      assert.equal(str.length, length)
    })

    it('should use specified character set', () => {
      const length = 10
      const charSet = 'ABC'
      const str = randomString(length, charSet)
      assert.equal(str.length, length)
      for (let i = 0; i < str.length; i++) {
        assert.ok(charSet.includes(str[i]))
      }
    })

    it('should use default length if not specified', () => {
      const str = randomString()
      assert.equal(str.length, 6)
    })

    it('should use default character set if not specified', () => {
      const str = randomString(10)
      assert.equal(str.length, 10)
      // Default character set includes alphanumeric characters
      assert.ok(/^[A-Za-z0-9]+$/.test(str))
    })
  })
})
