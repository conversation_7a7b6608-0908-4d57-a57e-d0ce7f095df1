import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Currencies, zeroDecimal, nativeAmount, currencyFormat } from '../dist'

describe('Currencies', () => {

	describe('currency', () => {
		it('should SGD', () => {
			const { symbol, decimals, name, country } = Currencies.currency('SGD', true)
			assert.equal(country, 'SG')
			assert.equal(symbol, '$')
			assert.equal(decimals, 2)
		})
		it('should TWD', () => {
			const { symbol, decimals, name, country } = Currencies.currency('TWD')
			assert.equal(country, 'TW')
			assert.equal(symbol, 'NT$')
			assert.equal(decimals, 0)
		})
		it('should HKD', () => {
			const { symbol, decimals, name, country } = Currencies.currency('HKD')
			assert.equal(country, 'HK')
			assert.equal(symbol, '$')
			assert.equal(decimals, 2)
		})
		it('should MYR', () => {
			const { symbol, decimals, name, country } = Currencies.currency('MYR')
			assert.equal(country, 'MY')
			assert.equal(symbol, 'RM')
			assert.equal(decimals, 2)
		})
		it('should KRW', () => {
			const { symbol, decimals, name, country } = Currencies.currency('KRW')
			assert.equal(country, 'KR')
			assert.equal(symbol, '₩')
			assert.equal(decimals, 0)
		})
	})

	describe('zeroDecimal', () => {
		it('should SGD', () => {
			const amount = zeroDecimal(123.45, 'SGD')
			assert.equal(amount, 12345)
		})
		it('should TWD', () => {
			const amount = zeroDecimal(1234, 'TWD')
			assert.equal(amount, 1234)
		})
		it('should JPY', () => {
			const amount = zeroDecimal(123456, 'JPY')
			assert.equal(amount, 123456)
		})
	})

	describe('nativeAmount', () => {
		it('should SGD', () => {
			const amount = nativeAmount(12345, 'SGD')
			assert.equal(amount, 123.45)
		})
		it('should TWD', () => {
			const amount = nativeAmount(1234, 'TWD')
			assert.equal(amount, 1234)
		})
		it('should JPY', () => {
			const amount = nativeAmount(123456, 'JPY')
			assert.equal(amount, 123456)
		})
	})

	describe('currencyFormat', () => {
		it('should $', () => {
			const amount = currencyFormat(123.45, 'SGD')
			assert.equal(amount, '$123.45')
		})

		it('should NT$', () => {
			const amount = currencyFormat(1234, 'TWD')
			assert.equal(amount, 'NT$1,234')
		})

		it('should ₩', () => {
			const amount = currencyFormat(123456, 'KRW')
			assert.equal(amount, '₩123,456')
		})

		it('should RM', () => {
			const amount = currencyFormat(123456, 'MYR')
			assert.equal(amount, 'RM 123,456.00')
		})
		
	})
})
