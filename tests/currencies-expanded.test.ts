import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Currencies } from '../src/index'

const { 
  currency, 
  currencySymbol, 
  isValid, 
  zeroDecimal, 
  nativeAmount, 
  roundAmount, 
  currencyFormat 
} = Currencies

describe('Currencies Expanded', () => {
  describe('currencySymbol', () => {
    it('should return $ for USD', () => {
      const symbol = currencySymbol('USD')
      assert.equal(symbol, '$')
    })

    it('should return € for EUR', () => {
      const symbol = currencySymbol('EUR')
      assert.equal(symbol, '€')
    })

    it('should return ¥ for JPY', () => {
      const symbol = currencySymbol('JPY')
      assert.equal(symbol, '¥')
    })

    it('should return $ for unknown currency', () => {
      const symbol = currencySymbol('XYZ')
      assert.equal(symbol, '$')
    })

    it('should return $ for empty currency when short is true', () => {
      const symbol = currencySymbol('', true)
      assert.equal(symbol, '$')
    })

    it('should handle lowercase currency codes', () => {
      const symbol = currencySymbol('eur')
      assert.equal(symbol, '€')
    })

    it('should return $ for dollar currencies when short is true', () => {
      const symbol = currencySymbol('SGD', true)
      assert.equal(symbol, '$')
    })
  })

  describe('currency', () => {
    it('should handle lowercase currency codes', () => {
      const { symbol, decimals } = currency('usd')
      assert.equal(symbol, '$')
      assert.equal(decimals, 2)
    })

    it('should provide defaults for unknown currency', () => {
      const { decimals, symbol, name } = currency('XYZ')
      assert.equal(decimals, 2)
      assert.equal(symbol, '$')
      assert.equal(name, '')
    })

    it('should handle empty currency code', () => {
      const result = currency()
      assert.deepEqual(result, { decimals: 2, symbol: '$', name: '', country: undefined, locale: undefined })
    })
  })

  describe('isValid', () => {
    it('should return true for valid currency codes', () => {
      assert.equal(isValid('USD'), true)
      assert.equal(isValid('EUR'), true)
      assert.equal(isValid('JPY'), true)
    })

    it('should return false for invalid currency codes', () => {
      assert.equal(isValid('XYZ'), false)
    })

    it('should handle lowercase currency codes', () => {
      assert.equal(isValid('usd'), true)
    })

    it('should handle empty currency code', () => {
      assert.equal(isValid(), false)
    })
  })

  describe('zeroDecimal', () => {
    it('should handle fractional amounts correctly', () => {
      const amount = zeroDecimal(123.456, 'USD')
      assert.equal(amount, 12345)
    })

    it('should handle empty currency code', () => {
      const amount = zeroDecimal(123.45)
      assert.equal(amount, 12345)
    })
  })

  describe('nativeAmount', () => {
    it('should handle empty currency code', () => {
      const amount = nativeAmount(12345)
      assert.equal(amount, 123.45)
    })
  })

  describe('roundAmount', () => {
    it('should round SGD amount to 2 decimals', () => {
      const amount = roundAmount(123.456, 'SGD')
      assert.equal(amount, 123.46)
    })

    it('should round TWD amount to 0 decimals', () => {
      const amount = roundAmount(1234.5, 'TWD')
      assert.equal(amount, 1235)
    })

    it('should round JPY amount to 0 decimals', () => {
      const amount = roundAmount(123456.7, 'JPY')
      assert.equal(amount, 123457)
    })

    it('should handle empty currency code', () => {
      const amount = roundAmount(123.456)
      assert.equal(amount, 123.46)
    })
  })

  describe('currencyFormat', () => {
    it('should handle empty currency code', () => {
      const amount = currencyFormat(123.45)
      assert.equal(amount.includes('123.45'), true)
    })
  })
})
