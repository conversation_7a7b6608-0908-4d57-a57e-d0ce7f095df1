import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Phones } from '../src/index'

const { parsePhoneNumber, parseFullNumber } = Phones

const validNumber = {
	countryCode: '886',
	regionCode: 'TW',
	isMobile: true,
	number: '+886987957706',
	nationalNumber: '987957706',
	fullNumber: '886987957706',
	lineType: 'mobile',
	possible: true
}

const otherTests = {
	with0: '+8860987957706',
	duplicatedCountryCode: '+886886987957706',
	hkClass2Number: '+85258999999',
}

describe('Phones', () => {
	it('should parsePhoneNumber (valid TW)', async () => {
		const resultTW = parsePhoneNumber(validNumber.number)

		assert.equal(validNumber.regionCode, resultTW?.regionCode)
		assert.equal(validNumber.countryCode, resultTW?.countryCode)
		assert.equal(validNumber.nationalNumber, resultTW?.nationalNumber)
		assert.equal(validNumber.fullNumber, resultTW?.fullNumber)
		assert.equal(validNumber.lineType, resultTW?.lineType)
		assert.equal(validNumber.possible, resultTW?.possible)
	})
	it('should parsePhoneNumber (duplicated country code)', async () => {
		const { valid } = parsePhoneNumber(otherTests.duplicatedCountryCode)
		assert.equal(valid, true)
	})
	it('should parsePhoneNumber (with 0)', async () => {
		const { valid } = parsePhoneNumber(otherTests.with0)
		assert.equal(valid, true)
	})
	it('should parsePhoneNumber as invalid (no country)', async () => {
		const { valid } = parsePhoneNumber(validNumber.nationalNumber)
		assert.equal(valid, false)
	})
	it('should parseFullNumber', async () => {
		const { valid } = parseFullNumber('6563968131')
		assert.equal(valid, true)
	})
	it('should parsePhoneNumber as valid (country = "TW")', async () => {
		const { valid } = parsePhoneNumber(validNumber.nationalNumber, validNumber.regionCode)

		assert.equal(valid, true)
	})
	it('should parsePhoneNumber as valid (country = "886" [string])', async () => {
		const { valid } = parsePhoneNumber(validNumber.nationalNumber, validNumber.countryCode)

		assert.equal(valid, true)
	})
	it('should parsePhoneNumber as valid (country = 886 [number])', async () => {
		const { valid } = parsePhoneNumber(validNumber.nationalNumber,Number(validNumber.countryCode))

		assert.equal(valid, true)
	})
	it('should parsePhoneNumber as mobile (HK Class 2 number +85258xxxxxx)', async () => {
		const { lineType } = parsePhoneNumber(otherTests.hkClass2Number)

		assert.equal(validNumber.lineType, lineType)
	})
})