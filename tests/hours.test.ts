import assert from 'node:assert'
import { describe, it } from 'node:test'
import {
	isOpen,
	nextOpen,
	openFrom,
	openUntil,
	periodsUnion,
	hoursFor,
	periodsDifference,
	periodsMerge,
	startEndOf,
	mergeSpecificDate,
	aggregateHours
} from '../src/index'

describe('Hours', () => {
	const TIMEZONE = 'Asia/Singapore',
		periodsOnly = {
			periods: [
				{ open: { day: 1, time: "09:00" }, close: { day: 1, time: "23:00" } },
				{ open: { day: 2, time: "09:00" }, close: { day: 2, time: "15:00" } },
				{ open: { day: 2, time: "18:00" }, close: { day: 2, time: "23:00" } },
				{ open: { day: 3, time: "09:00" }, close: { day: 3, time: "00:00" } },
				{ open: { day: 4, time: "00:00" }, close: { day: 4, time: "23:00" } },
				{ open: { day: 5, time: "09:00" }, close: { day: 5, time: "02:00" } },
				{ open: { day: 6, time: "09:00" }, close: { day: 6, time: "02:00" } },
				{ open: { day: 7, time: "09:00" }, close: { day: 7, time: "02:00" } }
			]
		},
		periodsWithRest = {
			periods: [
				{ open: { day: 1, time: "09:00" }, close: { day: 1, time: "12:00" } },
				{ open: { day: 1, time: "13:00" }, close: { day: 1, time: "02:00" } },
			]
		},
		nextOpenPeriods = {
			periods: [
				{ open: { day: 1, time: "09:00" }, close: { day: 1, time: "11:00" } },
				{ open: { day: 1, time: "14:00" }, close: { day: 1, time: "16:00" } },
				{ open: { day: 1, time: "13:00" }, close: { day: 1, time: "14:00" } },
				{ open: { day: 1, time: "18:00" }, close: { day: 1, time: "23:00" } },
				{ open: { day: 2, time: "09:00" }, close: { day: 2, time: "11:00" } },
				{ open: { day: 7, time: "22:00" }, close: { day: 7, time: "02:00" } },
			]
		},
		nextOpenPeriods2 = {
			periods: [
				{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "02:00" } },
				{ open: { day: 2, time: "12:00" }, close: { day: 2, time: "02:00" } }
			]
		},
		fullDay = {
			periods: [
				{ open: { day: 1, time: "00:00" }, close: { day: 1, time: "24:00" } },
				{ open: { day: 2, time: "00:00" }, close: { day: 2, time: "24:00" } },
				{ open: { day: 3, time: "00:00" }, close: { day: 3, time: "24:00" } },
				{ open: { day: 4, time: "00:00" }, close: { day: 4, time: "24:00" } },
				{ open: { day: 5, time: "00:00" }, close: { day: 5, time: "24:00" } },
				{ open: { day: 6, time: "00:00" }, close: { day: 6, time: "24:00" } },
				{ open: { day: 7, time: "00:00" }, close: { day: 7, time: "24:00" } }
			]
		},
		tillMidnight = {
			periods: [
				{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "00:00" } },
				{ open: { day: 6, time: "12:00" }, close: { day: 6, time: "00:00" } },
				{ open: { day: 7, time: "12:00" }, close: { day: 7, time: "00:00" } }
			]
		},
		specificPeriods = {
			periods: [
				{ open: { day: 6, time: "09:00" }, close: { day: 6, time: "02:00" } },
				{ open: { day: 7, time: "09:00" }, close: { day: 7, time: "23:00" } }
			],
			specific: [
				{ date: { year: 2023, month: 1, day: 21 }, periods: [{ open: { time: "09:00" }, close: { time: "12:00" } }] },
				{ date: { year: 2023, month: 1, day: 22 }, periods: [{ open: { time: "00:00" }, close: { time: "00:00" } }] },
				{ date: { year: 0, month: 1, day: 1 }, periods: [{ open: { time: "00:00" }, close: { time: "00:00" } }] }
			]
		},
		specificOnly = {
			periods: [],
			specific: [
				{ date: { year: 2023, month: 1, day: 1 }, periods: [{ open: { time: "09:00" }, close: { time: "12:00" } }] },
				{ date: { year: 2023, month: 2, day: 2 }, periods: [{ open: { time: "12:00" }, close: { time: "00:00" } }] }
			]
		}

	describe('hoursFor', () => {
		it('should periods only', async () => {
			const periods = hoursFor(periodsOnly, 2022, 12, 31),
				[ first ] = periods

			assert.equal(first.open.day, 5)		// Friday
		})

		it('should get periods for single day', async () => {
			const periods = hoursFor(tillMidnight, 2023, 2, 27)

			assert.equal(periods.length, 1)		// Friday
		})
	})

	describe('openUntil', () => {
		it('should return 1:59am previous day (late night hours)', async () => {
			const from = new Date('2023-01-02T01:00'), 		// Monday
				closingTime = openUntil(periodsOnly, from, TIMEZONE)

			assert.equal(closingTime.getTime(), new Date('2023-01-02T01:59:00.000').getTime())
		})

		it('should return 23:00am today', async () => {
			const from = new Date('2023-01-02T03:00'), 		// Monday
				closingTime = openUntil(periodsOnly, from, TIMEZONE)

			assert.equal(closingTime.getTime(), new Date('2023-01-02T22:59:00.000').getTime())
		})

		it('should return 1:59am next day (late night hours)', async () => {
			const from = new Date('2023-01-06T10:00'), 		// Friday
				closingTime = openUntil(periodsOnly, from, TIMEZONE)

			assert.equal(closingTime.getTime(), new Date('2023-01-07T01:59:00.000').getTime())
		})

		it('should return 1:59am next day (has rest & late night hours)', async () => {
			const from = new Date('2023-01-02T10:00'), 		// Monday
				closingTime = openUntil(periodsWithRest, from, TIMEZONE)

			assert.equal(closingTime.getTime(), new Date('2023-01-03T01:59:00.000').getTime())
		})

		it('should return 1:59am next day (has rest & late night hours)', async () => {
			const from = new Date('2023-01-02T14:00'), 		// Monday
				closingTime = openUntil(tillMidnight, from, TIMEZONE)

			assert.equal(closingTime.getTime(), new Date('2023-01-02T23:59:00.000').getTime())
		})
	})

	describe('isOpen', () => {
		it('should openFrom previous opening start time', async () => {
			const from = new Date('2023-01-02T01:00'),
				open = openFrom(periodsOnly, from, TIMEZONE)

			assert.equal(open.getTime(), new Date('2023-01-01T09:00:00.000').getTime())
		})

		it('should be Open (same day)', async () => {
			const from = new Date('2023-01-02T10:00'), 	// Monday
				to = new Date('2023-01-02T15:00'),
				open = isOpen(periodsOnly, from, to, TIMEZONE)

			assert.equal(open, true)
		})

		it('should NOT be Open (same day)', async () => {
			const from = new Date('2023-01-02T22:00'), 	// Monday
				to = new Date('2023-01-02T23:59'),
				open = isOpen(periodsOnly, from, to, TIMEZONE)

			assert.equal(open, false)
		})

		it('should NOT be Open (same day across 2 periods)', async () => {
			const from = new Date('2023-01-03T14:00'), 	// Tuesday
				to = new Date('2023-01-03T19:00'),
				open = isOpen(periodsOnly, from, to, TIMEZONE)

			assert.equal(open, false)
		})

		it('should be Open (across 2 days)', async () => {
			const from = new Date('2023-01-06T21:00'), 	// Friday
				to = new Date('2023-01-07T01:59'),
				open = isOpen(periodsOnly, from, to, TIMEZONE)

			assert.equal(open, true)
		})

		it('should be Open (late night hours)', async () => {
			const from = new Date('2023-01-07T01:00'), 	// Friday night
				to = new Date('2023-01-07T01:30'),
				open = isOpen(periodsOnly, from, to, TIMEZONE)

			assert.equal(open, true)
		})

		it('should be Open (overnight)', async () => {
			const from = new Date('2023-01-04T22:00'), 	// Wednesday
				to = new Date('2023-01-05T08:00'),
				open = isOpen(periodsOnly, from, to, TIMEZONE)

			assert.equal(open, true)
		})

		it('should be Open (late night hours, Sunday)', async () => {
			const from = new Date('2023-01-09T01:00'), 	// Sunday night
				to = new Date('2023-01-09T01:30'),
				open = isOpen(periodsOnly, from, to, TIMEZONE)

			assert.equal(open, true)
		})

		it('should NOT be Open (late night hours)', async () => {
			const from = new Date('2023-01-06T21:00'), 	// Friday night
				to = new Date('2023-01-07T04:00'),
				open = isOpen(periodsOnly, from, to, TIMEZONE)

			assert.equal(open, false)
		})

		it('should NOT be Open (late night hours, Sunday)', async () => {
			const from = new Date('2023-01-08T21:00'), 	// Sunday night
				to = new Date('2023-01-09T04:00'),
				open = isOpen(periodsOnly, from, to, TIMEZONE)

			assert.equal(open, false)
		})

		it('should be Open (2 full days)', async () => {
			const from = new Date('2023-01-07T00:00'), 	// Saturday & Sunday
				to = new Date('2023-01-08T23:59'),
				open = isOpen(fullDay, from, to, TIMEZONE)

			assert.equal(open, true)
		})

		it('should be Open (2 full days, Sun - Mon)', async () => {
			const from = new Date('2023-01-08T00:00'), 	// Sunday & Monday
				to = new Date('2023-01-09T23:59'),
				open = isOpen(fullDay, from, to, TIMEZONE)

			assert.equal(open, true)
		})

		it('should be Open (specific day)', async () => {
			const from = new Date('2023-01-21T10:00'),
				to = new Date('2023-01-21T11:00'),
				open = isOpen(specificPeriods, from, to, TIMEZONE)

			assert.equal(open, true)
		})

		it('should NOT be Open (specific day)', async () => {
			const from = new Date('2023-01-21T13:00'), 	// Saturday
				to = new Date('2023-01-21T15:00'),
				open = isOpen(specificPeriods, from, to, TIMEZONE)

			assert.equal(open, false)
		})

		it('should NOT be Open (specific day, full day)', async () => {
			const from = new Date('2023-01-22T09:00'), 	// Sunday
				to = new Date('2023-01-22T23:00'),
				open = isOpen(specificPeriods, from, to, TIMEZONE)

			assert.equal(open, false)
		})

		it('should NOT be Open (specific day, every year)', async () => {
			const from = new Date('2022-01-01T09:00'),
				to = new Date('2022-01-01T23:00'),
				open = isOpen(specificPeriods, from, to, TIMEZONE)

			assert.equal(open, false)
		})
		it('should openFrom 24 hours', async () => {
			const from = new Date('2024-09-08T08:00:00.000Z'),
				open = openFrom(fullDay, from, TIMEZONE)

			assert.equal(open.getTime(), new Date('2024-09-07T16:00:00.000Z').getTime())
		})

		it('should be Open (all days 11:00-24:00)', async () => {
			const allDays11to24 = {
				specific: [],
				periods: [
					{ open: { day: 1, time: "11:00" }, close: { day: 1, time: "24:00" } },
					{ open: { day: 2, time: "11:00" }, close: { day: 2, time: "24:00" } },
					{ open: { day: 3, time: "11:00" }, close: { day: 3, time: "24:00" } },
					{ open: { day: 4, time: "11:00" }, close: { day: 4, time: "24:00" } },
					{ open: { day: 5, time: "11:00" }, close: { day: 5, time: "24:00" } },
					{ open: { day: 6, time: "11:00" }, close: { day: 6, time: "24:00" } },
					{ open: { day: 7, time: "11:00" }, close: { day: 7, time: "24:00" } }
				]
			};

			const from = new Date('2025-04-11T15:00:00.000Z'),
				to = new Date('2025-04-11T15:59:59.999Z'),
				open = isOpen(allDays11to24, from, to, TIMEZONE);

			assert.equal(open, true);
		});
	})

	describe('nextOpen', () => {
		it('should return current time', async () => {
			const now = new Date('2023-01-02T10:00'),	// Monday
				next = nextOpen(nextOpenPeriods, now, TIMEZONE)

			assert.equal(next.start.getTime(), now.getTime())
		})

		it('should return next slot', async () => {
			const now = new Date('2023-01-02T12:00'),	// Monday
				next = nextOpen(nextOpenPeriods, now, TIMEZONE)

			assert.equal(next.start.getTime(), new Date('2023-01-02T13:00').getTime())
		})

		it('should return next slot (time = start time of a slot)', async () => {
			const now = new Date('2023-01-02T09:00'),	// Monday
				next = nextOpen(nextOpenPeriods, now, TIMEZONE)

			assert.equal(next.start.getTime(), new Date('2023-01-02T09:00').getTime())
		})

		it('should return next slot (time = end time of a slot)', async () => {
			const now = new Date('2023-01-02T16:00'),	// Monday
				next = nextOpen(nextOpenPeriods, now, TIMEZONE)

			assert.equal(next.start.getTime(), new Date('2023-01-02T18:00').getTime())
		})

		it('should return next slot on Monday', async () => {
			const now = new Date('2023-01-01T23:00'),	// Sunday
				next = nextOpen(nextOpenPeriods, now, TIMEZONE)

			assert.equal(next.start.getTime(), new Date('2023-01-01T23:00').getTime())
		})

		it('should return next slot (late night)', async () => {
			const now = new Date('2023-01-02T23:00'),	// Monday
				next = nextOpen(nextOpenPeriods2, now, TIMEZONE)

			assert.equal(next.start.getTime(), new Date('2023-01-02T23:00').getTime())
		})

		it('should return next slot (weekend not open)', async () => {
			const now = new Date('2023-03-11T12:00'),	// Monday
				next = nextOpen(nextOpenPeriods, now, TIMEZONE)

			assert.equal(next, undefined)
		})

		it('should return next slot (till midnight)', async () => {
			const now = new Date('2023-03-20T21:00'),	// Monday
				next = nextOpen(tillMidnight, now, TIMEZONE)

			assert.equal(next.end.getTime(), new Date('2023-03-21T00:00').getTime())
		})
	})

	describe('periodsUnion', () => {
		it('should return union periods', async () => {
			const periodA = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "12:00" } },
					{ open: { day: 1, time: "13:00" }, close: { day: 1, time: "17:00" } },
					{ open: { day: 1, time: "19:00" }, close: { day: 1, time: "21:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "07:00" }, close: { day: 1, time: "10:00" } },
					{ open: { day: 1, time: "11:00" }, close: { day: 1, time: "14:00" } },
					{ open: { day: 1, time: "22:00" }, close: { day: 1, time: "23:59" } },
				],
				periodC = [
					{ open: { day: 1, time: "07:00" }, close: { day: 1, time: "17:00" } },
					{ open: { day: 1, time: "19:00" }, close: { day: 1, time: "21:00" } },
					{ open: { day: 1, time: "22:00" }, close: { day: 1, time: "23:59" } },
				],
				union = periodsUnion(periodA, periodB)

			assert.deepStrictEqual(union, periodC)
		})

		it('should return union periods with different days', async () => {
			const periodA = [
					{ open: { day: 1, time: "09:00" }, close: { day: 1, time: "17:00" } },
					{ open: { day: 2, time: "09:00" }, close: { day: 2, time: "17:00" } }
				],
				periodB = [
					{ open: { day: 3, time: "09:00" }, close: { day: 3, time: "17:00" } },
					{ open: { day: 4, time: "09:00" }, close: { day: 4, time: "17:00" } }
				],
				union = periodsUnion(periodA, periodB)

			console.log('Union of different days:', JSON.stringify(union));

			assert.equal(union.length, 4);
			assert.ok(union.some(p => p.open.day === 1));
			assert.ok(union.some(p => p.open.day === 2));
			assert.ok(union.some(p => p.open.day === 3));
			assert.ok(union.some(p => p.open.day === 4));
		})

		it('should return union periods (late night)', async () => {
			const periodA = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "02:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "22:00" }, close: { day: 1, time: "00:00" } },
				],
				periodC = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "02:00" } },
				],
				union = periodsUnion(periodA, periodB)

			assert.deepStrictEqual(union, periodC)
		})

		it('should return union periods (Sun - Mon late night)', async () => {
			const periodA = [
					{ open: { day: 7, time: "08:00" }, close: { day: 7, time: "02:00" } },
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "02:00" } },
				],
				periodB = [
					{ open: { day: 7, time: "22:00" }, close: { day: 7, time: "00:00" } },
					{ open: { day: 1, time: "22:00" }, close: { day: 1, time: "00:00" } },
				],
				union = periodsUnion(periodA, periodB)

			// Check that the union has the correct number of periods
			assert.equal(union.length, 2);

			// Check that the union contains periods for both days
			assert.ok(union.some(p => p.open.day === 7));
			assert.ok(union.some(p => p.open.day === 1));

			// Check that the periods have the correct time ranges
			const day7Period = union.find(p => p.open.day === 7);
			const day1Period = union.find(p => p.open.day === 1);

			assert.deepStrictEqual(day7Period.open.time, "08:00");
			assert.deepStrictEqual(day7Period.close.time, "02:00");
			assert.deepStrictEqual(day1Period.open.time, "08:00");
			assert.deepStrictEqual(day1Period.close.time, "02:00");
		})
	})

	describe('periodsDifference', () => {
		it('should return periodsDifference (A enclose B)', async () => {
			const periodA = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "22:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "14:00" }, close: { day: 1, time: "19:00" } },
				],
				periodC = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "14:00" } },
					{ open: { day: 1, time: "19:00" }, close: { day: 1, time: "22:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, periodC)
		})

		it('should return periodsDifference (B enclose A)', async () => {
			const periodA = [
					{ open: { day: 1, time: "10:00" }, close: { day: 1, time: "20:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "20:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, [])
		})

		it('should return periodsDifference (intersect)', async () => {
			const periodA = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "22:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "19:00" }, close: { day: 1, time: "23:00" } },
				],
				periodC = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "19:00" } }
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, periodC)
		})

		it('should return periodsDifference (consecutive periods in B)', async () => {
			const periodA = [
					{ open: { day: 1, time: "11:00" }, close: { day: 1, time: "00:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "11:00" }, close: { day: 1, time: "13:00" } },
					{ open: { day: 1, time: "13:00" }, close: { day: 1, time: "15:00" } },
				],
				periodC = [
					{ open: { day: 1, time: "15:00" }, close: { day: 1, time: "00:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, periodC)
		})

		it('should return periodsDifference (consecutive periods in B till 00:00)', async () => {
			const periodA = [
					{ open: { day: 1, time: "11:00" }, close: { day: 1, time: "00:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "22:00" }, close: { day: 1, time: "23:00" } },
					{ open: { day: 1, time: "23:00" }, close: { day: 1, time: "00:00" } },
				],
				periodC = [
					{ open: { day: 1, time: "11:00" }, close: { day: 1, time: "22:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, periodC)
		})

		it('should return periodsDifference (fully overlap)', async () => {
			const periodA = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "20:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "20:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, [])
		})

		it('should return periodsDifference (no overlap)', async () => {
			const periodA = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "20:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "20:00" }, close: { day: 1, time: "23:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, periodA)
		})

		it('should return periodsDifference (late night)', async () => {
			const periodA = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "02:00" } },
					{ open: { day: 2, time: "08:00" }, close: { day: 2, time: "02:00" } },
				],
				periodB = [
					{ open: { day: 2, time: "20:00" }, close: { day: 2, time: "23:00" } },
				],
				periodC = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "02:00" } },
					{ open: { day: 2, time: "08:00" }, close: { day: 2, time: "20:00" } },
					{ open: { day: 2, time: "23:00" }, close: { day: 2, time: "02:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, periodC)
		})

		it('should return periodsDifference (late night, cross days)', async () => {
			const periodA = [
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "01:00" } },
					{ open: { day: 2, time: "12:00" }, close: { day: 2, time: "01:00" } },
					{ open: { day: 3, time: "12:00" }, close: { day: 3, time: "01:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "13:00" } },
				],
				periodC = [
					{ open: { day: 1, time: "13:00" }, close: { day: 1, time: "01:00" } },
					{ open: { day: 2, time: "12:00" }, close: { day: 2, time: "01:00" } },
					{ open: { day: 3, time: "12:00" }, close: { day: 3, time: "01:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, periodC)
		})

		it('should return periodsDifference (late night, cross days, Sunday)', async () => {
			const periodA = [
					{ open: { day: 7, time: "12:00" }, close: { day: 7, time: "01:00" } },
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "01:00" } },
					{ open: { day: 2, time: "12:00" }, close: { day: 2, time: "01:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "13:00" } },
				],
				periodC = [
					{ open: { day: 7, time: "12:00" }, close: { day: 7, time: "01:00" } },
					{ open: { day: 1, time: "13:00" }, close: { day: 1, time: "01:00" } },
					{ open: { day: 2, time: "12:00" }, close: { day: 2, time: "01:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, periodC)
		})

		it('should return periodsDifference (24 hours)', async () => {
			const periodA = [
					{ open: { day: 1, time: "00:00" }, close: { day: 1, time: "24:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "20:00" }, close: { day: 1, time: "23:00" } },
				],
				periodC = [
					{ open: { day: 1, time: "00:00" }, close: { day: 1, time: "20:00" } },
					{ open: { day: 1, time: "23:00" }, close: { day: 1, time: "00:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, periodC)
		})

		it('should return periodsDifference (24 hours, 2 days)', async () => {
			const periodA = [
					{ open: { day: 1, time: "00:00" }, close: { day: 1, time: "24:00" } },
					{ open: { day: 2, time: "00:00" }, close: { day: 2, time: "24:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "20:00" }, close: { day: 1, time: "01:00" } },
				],
				periodC = [
					{ open: { day: 1, time: "00:00" }, close: { day: 1, time: "20:00" } },
					{ open: { day: 2, time: "01:00" }, close: { day: 2, time: "00:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, periodC)
		})

		it('should return periodsDifference (last 1 hour)', async () => {
			const periodA = [
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "23:00" } },
					{ open: { day: 2, time: "12:00" }, close: { day: 2, time: "23:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "22:00" }, close: { day: 1, time: "23:00" } },
				],
				periodC = [
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "22:00" } },
					{ open: { day: 2, time: "12:00" }, close: { day: 2, time: "23:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, periodC)
		})

		it('should return periodsDifference (last 1 hour, late night)', async () => {
			const periodA = [
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "01:00" } },
					{ open: { day: 2, time: "12:00" }, close: { day: 2, time: "01:00" } },
				],
				periodB = [
					{ open: { day: 2, time: "00:00" }, close: { day: 2, time: "01:00" } },
				],
				periodC = [
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "00:00" } },
					{ open: { day: 2, time: "12:00" }, close: { day: 2, time: "01:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, periodC)
		})

		it('should return periodsDifference (last 2 hour, late night)', async () => {
			const periodA = [
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "01:00" } },
					{ open: { day: 2, time: "12:00" }, close: { day: 2, time: "01:00" } },
				],
				periodB = [
					{ open: { day: 1, time: "23:00" }, close: { day: 1, time: "01:00" } },
				],
				periodC = [
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "23:00" } },
					{ open: { day: 2, time: "12:00" }, close: { day: 2, time: "01:00" } },
				],
				diff = periodsDifference(periodA, periodB)

			assert.deepStrictEqual(diff, periodC)
		})
	})

	describe('periodsMerge', () => {
		it('should return periodsMerge', async () => {
			const periodsList = [
					[ { open: { day: 1, time: "08:00" }, close: { day: 1, time: "16:00" } } ],
					[ { open: { day: 1, time: "12:00" }, close: { day: 1, time: "21:00" } } ],
					[ { open: { day: 1, time: "10:00" }, close: { day: 1, time: "23:00" } } ]
				],
				periodsM = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "16:00" } },
					{ open: { day: 1, time: "10:00" }, close: { day: 1, time: "23:00" } },
				],
				merged = periodsMerge(periodsList)

			assert.deepStrictEqual(merged, periodsM)
		})

		it('should return periodsMerge (no intersect)', async () => {
			const periodsList = [
					[ { open: { day: 1, time: "12:00" }, close: { day: 1, time: "20:00" } } ],
					[ { open: { day: 1, time: "08:00" }, close: { day: 1, time: "11:00" } } ],
					[ { open: { day: 1, time: "21:00" }, close: { day: 1, time: "23:00" } } ]
				],
				periodsM = [
					{ open: { day: 1, time: "08:00" }, close: { day: 1, time: "11:00" } },
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "20:00" } },
					{ open: { day: 1, time: "21:00" }, close: { day: 1, time: "23:00" } }
				],
				merged = periodsMerge(periodsList)

			assert.deepStrictEqual(merged, periodsM)
		})

		it('should return periodsMerge (fully enclose)', async () => {
			const periodsList = [
					[ { open: { day: 1, time: "08:00" }, close: { day: 1, time: "16:00" } } ],
					[ { open: { day: 1, time: "12:00" }, close: { day: 1, time: "21:00" } } ],
					[ { open: { day: 1, time: "07:00" }, close: { day: 1, time: "23:00" } } ]
				],
				periodsM = [
					{ open: { day: 1, time: "07:00" }, close: { day: 1, time: "23:00" } },
				],
				merged = periodsMerge(periodsList)

			assert.deepStrictEqual(merged, periodsM)
		})

		it('should return periodsMerge (same periods)', async () => {
			const periodsList = [
					[ { open: { day: 1, time: "12:00" }, close: { day: 1, time: "21:00" } } ],
					[ { open: { day: 1, time: "12:00" }, close: { day: 1, time: "21:00" } } ],
					[ { open: { day: 1, time: "12:00" }, close: { day: 1, time: "21:00" } } ]
				],
				periodsM = [
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "21:00" } },
				],
				merged = periodsMerge(periodsList)

			assert.deepStrictEqual(merged, periodsM)
		})

		it('should return periodsMerge (late night)', async () => {
			const periodsList = [
					[ { open: { day: 1, time: "12:00" }, close: { day: 1, time: "01:00" } } ],
					[ { open: { day: 1, time: "12:00" }, close: { day: 1, time: "01:00" } } ],
					[ { open: { day: 1, time: "20:00" }, close: { day: 1, time: "02:00" } } ]
				],
				periodsM = [
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "01:00" } },
					{ open: { day: 1, time: "20:00" }, close: { day: 1, time: "02:00" } }
				],
				merged = periodsMerge(periodsList)

			assert.deepStrictEqual(merged, periodsM)
		})

		it('should return periodsMerge (cross multiple days)', async () => {
			const periodsList = [
					[ { open: { day: 1, time: "12:00" }, close: { day: 1, time: "01:00" } } ],
					[ { open: { day: 1, time: "12:00" }, close: { day: 1, time: "14:00" } } ],
					[ { open: { day: 2, time: "13:00" }, close: { day: 2, time: "01:00" } } ]
				],
				periodsM = [
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "01:00" } },
					{ open: { day: 2, time: "13:00" }, close: { day: 2, time: "01:00" } }
				],
				merged = periodsMerge(periodsList)

			assert.deepStrictEqual(merged, periodsM)
		})

		it('should return periodsMerge (Sunday late night)', async () => {
			const periodsList = [
					[ { open: { day: 7, time: "12:00" }, close: { day: 7, time: "01:00" } } ],
					[ { open: { day: 7, time: "10:00" }, close: { day: 7, time: "02:00" } } ],
					[ { open: { day: 1, time: "13:00" }, close: { day: 1, time: "01:00" } } ],
				],
				periodsM = [
					{ open: { day: 7, time: "10:00" }, close: { day: 7, time: "02:00" } },
					{ open: { day: 1, time: "13:00" }, close: { day: 1, time: "01:00" } },
				],
				merged = periodsMerge(periodsList)

			assert.deepStrictEqual(merged, periodsM)
		})
	})
	describe('openFrom', () => {
		it('should return open from', async () => {
			const now = new Date('2023-01-02T09:00'),	// Monday
				open = openFrom(nextOpenPeriods, now, TIMEZONE)

			assert.equal(open.getTime(), now.getTime())
		})

		it('should return open from (till midnight)', async () => {
			const now = new Date('2023-01-02T12:00'),	// Monday
				open = openFrom(tillMidnight, now, TIMEZONE)

			assert.equal(open.getTime(), now.getTime())
		})

		it('should return open from (till latenight)', async () => {
			const now = new Date('2023-01-02T12:00'),	// Monday
				open = openFrom(nextOpenPeriods2, now, TIMEZONE)

			assert.equal(open.getTime(), now.getTime())
		})
	})
	describe('startEndOf', () => {
		it('should return startEndOf', async () => {
			const startEnd = startEndOf(specificOnly, TIMEZONE),
				expected = {
					startTime: new Date('2023-01-01T01:00:00.000Z'),
					endTime: new Date('2023-02-02T15:59:00.000Z')
				}

			assert.deepStrictEqual(startEnd, expected)
		})
		it('should return empty startEndOf - periods only', async () => {
			const startEnd = startEndOf(periodsOnly, TIMEZONE),
				expected = {}

			assert.deepStrictEqual(startEnd, expected)
		})
		it('should return empty startEndOf', async () => {
			const startEnd = startEndOf(specificPeriods, TIMEZONE),
				expected = {}

			assert.deepStrictEqual(startEnd, expected)
		})
	})

	describe('mergeSpecificDate', () => {
		it('should add a new specific date when it does not exist', async () => {
			const specificDates = [
				{
					date: { year: 2023, month: 1, day: 1 },
					periods: [{ open: { time: "09:00" }, close: { time: "12:00" } }]
				}
			];
			const newDate = {
				date: { year: 2023, month: 1, day: 2 },
				periods: [{ open: { time: "10:00" }, close: { time: "14:00" } }]
			};

			const result = mergeSpecificDate(specificDates, newDate);

			assert.equal(result.length, 2);
			assert.deepStrictEqual(result[0], specificDates[0]);
			assert.deepStrictEqual(result[1], newDate);
			// Ensure deep clone was used (not reference)
			assert.notEqual(result[1], newDate);
		})

		it('should merge periods when the specific date already exists', async () => {
			const specificDates = [
				{
					date: { year: 2023, month: 1, day: 1 },
					periods: [{ open: { time: "09:00" }, close: { time: "12:00" } }]
				}
			];
			const newDate = {
				date: { year: 2023, month: 1, day: 1 },
				periods: [{ open: { time: "14:00" }, close: { time: "18:00" } }]
			};

			const result = mergeSpecificDate(specificDates, newDate);

			assert.equal(result.length, 1);
			assert.equal(result[0].periods.length, 2);
			assert.deepStrictEqual(result[0].periods[0], { open: { time: "09:00" }, close: { time: "12:00" } });
			assert.deepStrictEqual(result[0].periods[1], { open: { time: "14:00" }, close: { time: "18:00" } });
		})

		it('should handle overlapping periods when merging', async () => {
			const specificDates = [
				{
					date: { year: 2023, month: 1, day: 1 },
					periods: [{ open: { time: "09:00" }, close: { time: "14:00" } }]
				}
			];
			const newDate = {
				date: { year: 2023, month: 1, day: 1 },
				periods: [{ open: { time: "12:00" }, close: { time: "18:00" } }]
			};

			const result = mergeSpecificDate(specificDates, newDate);

			assert.equal(result.length, 1);
			assert.equal(result[0].periods.length, 1);
			assert.deepStrictEqual(result[0].periods[0], { open: { time: "09:00" }, close: { time: "18:00" } });
		})

		it('should handle busy property when merging periods', async () => {
			const specificDates = [
				{
					date: { year: 2023, month: 1, day: 1 },
					periods: [{ open: { time: "09:00" }, close: { time: "14:00" }, busy: { time: "12:00" } }]
				}
			];
			const newDate = {
				date: { year: 2023, month: 1, day: 1 },
				periods: [{ open: { time: "12:00" }, close: { time: "18:00" }, busy: { time: "16:00" } }]
			};

			const result = mergeSpecificDate(specificDates, newDate);

			assert.equal(result.length, 1);
			// Note: busy property behavior in periodsUnion is to keep the first one encountered
			assert.equal(result[0].periods.length, 1);
			assert.deepStrictEqual(result[0].periods[0].open, { time: "09:00" });
			assert.deepStrictEqual(result[0].periods[0].close, { time: "18:00" });
			assert.deepStrictEqual(result[0].periods[0].busy, { time: "12:00" });
		})

		it('should handle wildcards in date matching (year=0)', async () => {
			const specificDates = [
				{
					date: { year: 0, month: 1, day: 1 }, // Every year on Jan 1
					periods: [{ open: { time: "09:00" }, close: { time: "12:00" } }]
				}
			];
			const newDate = {
				date: { year: 2023, month: 1, day: 1 }, // Specific year
				periods: [{ open: { time: "14:00" }, close: { time: "18:00" } }]
			};

			const result = mergeSpecificDate(specificDates, newDate);

			assert.equal(result.length, 1);
			assert.equal(result[0].periods.length, 2);
		})

		it('should handle empty or undefined specificDates', async () => {
			const newDate = {
				date: { year: 2023, month: 1, day: 1 },
				periods: [{ open: { time: "09:00" }, close: { time: "12:00" } }]
			};

			const result1 = mergeSpecificDate([], newDate);
			const result2 = mergeSpecificDate(undefined, newDate);

			assert.equal(result1.length, 1);
			assert.deepStrictEqual(result1[0], newDate);
			assert.notEqual(result1[0], newDate); // Check deep clone

			assert.equal(result2.length, 1);
			assert.deepStrictEqual(result2[0], newDate);
			assert.notEqual(result2[0], newDate); // Check deep clone
		})
	})

	describe('aggregateHours', () => {
		it('should aggregate multiple hours objects with periods', async () => {
			const hours1 = {
				periods: [
					{ open: { day: 1, time: "09:00" }, close: { day: 1, time: "17:00" } },
					{ open: { day: 2, time: "09:00" }, close: { day: 2, time: "17:00" } }
				]
			};
			const hours2 = {
				periods: [
					{ open: { day: 3, time: "09:00" }, close: { day: 3, time: "17:00" } },
					{ open: { day: 4, time: "09:00" }, close: { day: 4, time: "17:00" } }
				]
			};

			const result = aggregateHours([{ hours: hours1 }, { hours: hours2 }]);

			console.log('Result periods:', JSON.stringify(result.periods));

			// Check that all periods are present, but don't rely on specific order
			assert.ok(result.periods.some(p =>
				p.open.day === 1 && p.open.time === "09:00" && p.close.day === 1 && p.close.time === "17:00"
			), 'Day 1 period missing');
			assert.ok(result.periods.some(p =>
				p.open.day === 2 && p.open.time === "09:00" && p.close.day === 2 && p.close.time === "17:00"
			), 'Day 2 period missing');
			assert.ok(result.periods.some(p =>
				p.open.day === 3 && p.open.time === "09:00" && p.close.day === 3 && p.close.time === "17:00"
			), 'Day 3 period missing');
			assert.ok(result.periods.some(p =>
				p.open.day === 4 && p.open.time === "09:00" && p.close.day === 4 && p.close.time === "17:00"
			), 'Day 4 period missing');
		})

		it('should merge overlapping periods when aggregating', async () => {
			const hours1 = {
				periods: [
					{ open: { day: 1, time: "09:00" }, close: { day: 1, time: "17:00" } }
				]
			};
			const hours2 = {
				periods: [
					{ open: { day: 1, time: "12:00" }, close: { day: 1, time: "20:00" } }
				]
			};

			const result = aggregateHours([{ hours: hours1 }, { hours: hours2 }]);

			assert.equal(result.periods.length, 1);
			assert.deepStrictEqual(result.periods[0], { open: { day: 1, time: "09:00" }, close: { day: 1, time: "20:00" } });
		})

		it('should aggregate specific dates', async () => {
			const hours1 = {
				periods: [],
				specific: [
					{
						date: { year: 2023, month: 1, day: 1 },
						periods: [{ open: { time: "09:00" }, close: { time: "12:00" } }]
					}
				]
			};
			const hours2 = {
				periods: [],
				specific: [
					{
						date: { year: 2023, month: 1, day: 2 },
						periods: [{ open: { time: "10:00" }, close: { time: "14:00" } }]
					}
				]
			};

			const result = aggregateHours([{ hours: hours1 }, { hours: hours2 }]);

			assert.equal(result.specific.length, 2);
			assert.deepStrictEqual(result.specific[0], hours1.specific[0]);
			assert.deepStrictEqual(result.specific[1], hours2.specific[0]);
		})

		it('should merge specific dates that overlap', async () => {
			const hours1 = {
				periods: [],
				specific: [
					{
						date: { year: 2023, month: 1, day: 1 },
						periods: [{ open: { time: "09:00" }, close: { time: "12:00" } }]
					}
				]
			};
			const hours2 = {
				periods: [],
				specific: [
					{
						date: { year: 2023, month: 1, day: 1 },
						periods: [{ open: { time: "14:00" }, close: { time: "18:00" } }]
					}
				]
			};

			const result = aggregateHours([{ hours: hours1 }, { hours: hours2 }]);

			assert.equal(result.specific.length, 1);
			assert.equal(result.specific[0].periods.length, 2);
			assert.deepStrictEqual(result.specific[0].periods[0], { open: { time: "09:00" }, close: { time: "12:00" } });
			assert.deepStrictEqual(result.specific[0].periods[1], { open: { time: "14:00" }, close: { time: "18:00" } });
		})

		it('should handle both periods and specific dates', async () => {
			const hours1 = {
				periods: [
					{ open: { day: 1, time: "09:00" }, close: { day: 1, time: "17:00" } }
				],
				specific: [
					{
						date: { year: 2023, month: 1, day: 1 },
						periods: [{ open: { time: "09:00" }, close: { time: "12:00" } }]
					}
				]
			};
			const hours2 = {
				periods: [
					{ open: { day: 2, time: "09:00" }, close: { day: 2, time: "17:00" } }
				],
				specific: [
					{
						date: { year: 2023, month: 1, day: 2 },
						periods: [{ open: { time: "10:00" }, close: { time: "14:00" } }]
					}
				]
			};

			console.log('Input hours1:', JSON.stringify(hours1));
			console.log('Input hours2:', JSON.stringify(hours2));

			const result = aggregateHours([{ hours: hours1 }, { hours: hours2 }]);

			console.log('Result periods:', JSON.stringify(result.periods));
			console.log('Result specific:', JSON.stringify(result.specific));

			// Check that periods are correctly merged
			assert.ok(result.periods.length >= 2, `Expected at least 2 periods, got ${result.periods.length}`);
			assert.ok(result.periods.some(p =>
				p.open.day === 1 && p.open.time === "09:00" && p.close.day === 1 && p.close.time === "17:00"
			), 'Day 1 period missing');
			assert.ok(result.periods.some(p =>
				p.open.day === 2 && p.open.time === "09:00" && p.close.day === 2 && p.close.time === "17:00"
			), 'Day 2 period missing');

			// Check that specific dates are correctly merged
			assert.ok(result.specific.length >= 2, `Expected at least 2 specific dates, got ${result.specific.length}`);
			assert.ok(result.specific.some(s =>
				s.date.year === 2023 && s.date.month === 1 && s.date.day === 1
			), 'Specific date 2023-01-01 missing');
			assert.ok(result.specific.some(s =>
				s.date.year === 2023 && s.date.month === 1 && s.date.day === 2
			), 'Specific date 2023-01-02 missing');
		})

		it('should handle empty array', async () => {
			const result = aggregateHours([]);

			assert.deepStrictEqual(result, { periods: [], specific: [] });
		})

		it('should handle missing properties', async () => {
			const hours1 = {};
			const hours2 = { periods: [] };
			const hours3 = { specific: [] };

			const result = aggregateHours([{ hours: hours1 }, { hours: hours2 }, { hours: hours3 }]);

			assert.deepStrictEqual(result, { periods: [], specific: [] });
		})
	})
})
