import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Emails, isEmail, EmailType } from '../src/index'

const { isPersonalEmail, emailAddressType, parseEmailAddress } = Emails

describe('Emails', () => {
  describe('isEmail', () => {
    it('should validate correct email addresses', () => {
      assert.equal(isEmail('<EMAIL>'), true)
      assert.equal(isEmail('<EMAIL>'), true)
      assert.equal(isEmail('<EMAIL>'), true)
    })

    it('should reject invalid email addresses', () => {
      assert.equal(isEmail('not-an-email'), false)
      assert.equal(isEmail('missing@domain'), false)
      assert.equal(isEmail('@domain.com'), false)
      assert.equal(isEmail('user@.com'), false)
      assert.equal(isEmail(''), false)
    })
  })

  describe('emailAddressType', () => {
    it('should identify personal email addresses', () => {
      assert.equal(emailAddressType('<EMAIL>'), EmailType.HOME)
      assert.equal(emailAddressType('<EMAIL>'), EmailType.HOME)
      assert.equal(emailAddressType('<EMAIL>'), EmailType.HOME)
      assert.equal(emailAddressType('<EMAIL>'), EmailType.HOME)
    })

    it('should identify work email addresses', () => {
      assert.equal(emailAddressType('<EMAIL>'), EmailType.WORK)
      assert.equal(emailAddressType('<EMAIL>'), EmailType.WORK)
      assert.equal(emailAddressType('<EMAIL>'), EmailType.WORK)
    })

    it('should handle invalid email addresses', () => {
      assert.equal(emailAddressType('not-an-email'), EmailType.OTHERS)
      assert.equal(emailAddressType(''), EmailType.OTHERS)
    })
  })

  describe('isPersonalEmail', () => {
    it('should identify personal email domains', () => {
      assert.equal(isPersonalEmail('<EMAIL>'), true)
      assert.equal(isPersonalEmail('<EMAIL>'), true)
      assert.equal(isPersonalEmail('<EMAIL>'), true)
      assert.equal(isPersonalEmail('<EMAIL>'), true)
      assert.equal(isPersonalEmail('<EMAIL>'), true)
      assert.equal(isPersonalEmail('<EMAIL>'), true)
    })

    it('should identify non-personal email domains', () => {
      assert.equal(isPersonalEmail('<EMAIL>'), false)
      assert.equal(isPersonalEmail('<EMAIL>'), false)
      assert.equal(isPersonalEmail('<EMAIL>'), false)
    })

    it('should handle invalid email addresses', () => {
      assert.equal(isPersonalEmail('not-an-email'), false)
      assert.equal(isPersonalEmail(''), false)
    })
  })

  describe('parseEmailAddress', () => {
    it('should parse valid email addresses', () => {
      const result = parseEmailAddress('<EMAIL>')
      assert.equal(result.local, 'user')
      assert.equal(result.domain, 'example.com')
    })

    it('should parse email addresses with display names', () => {
      const result = parseEmailAddress('John Doe <<EMAIL>>')
      assert.equal(result.name, 'John Doe')
      assert.equal(result.local, 'john.doe')
      assert.equal(result.domain, 'example.com')
    })

    it('should handle invalid email addresses', () => {
      const result = parseEmailAddress('not-an-email')
      assert.equal(result, null)
    })
  })
})
