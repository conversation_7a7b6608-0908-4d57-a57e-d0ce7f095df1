import assert from 'node:assert'
import { describe, it } from 'node:test'
import { bm } from '../src/index'

describe('Benchmark', () => {
  describe('mark', () => {
    it('should mark a timestamp', () => {
      const result = bm.mark('test')
      assert.ok(typeof result === 'bigint')
    })

    it('should mark a timestamp without a marker', () => {
      const result = bm.mark()
      assert.ok(typeof result === 'bigint')
    })
  })

  describe('get', () => {
    it('should get a marked timestamp', () => {
      bm.mark('test-get')
      const result = bm.get('test-get')
      assert.ok(typeof result === 'bigint')
    })
  })

  describe('elapsedTime', () => {
    it('should calculate elapsed time between two markers', () => {
      bm.mark('start')
      // Simulate some work
      for (let i = 0; i < 1000000; i++) {}
      bm.mark('end')
      
      const result = bm.elapsedTime('start', 'end', false)
      assert.ok(typeof result === 'number')
      assert.ok(result >= 0)
    })

    it('should calculate elapsed time with default end marker', () => {
      bm.mark('test.start')
      // Simulate some work
      for (let i = 0; i < 1000000; i++) {}
      bm.mark('test.end')
      
      const result = bm.elapsedTime('test', false)
      assert.ok(typeof result === 'number')
      assert.ok(result >= 0)
    })

    it('should return -1 if markers do not exist', () => {
      const result = bm.elapsedTime('nonexistent-start', 'nonexistent-end', false)
      assert.equal(result, -1)
    })
  })

  describe('diff', () => {
    it('should calculate difference between two timestamps', () => {
      const start = bm.mark()
      // Simulate some work
      for (let i = 0; i < 1000000; i++) {}
      const end = bm.mark()
      
      const result = bm.diff(start, end)
      assert.ok(typeof result === 'number')
      assert.ok(result >= 0)
    })

    it('should calculate difference with current time if end not provided', () => {
      const start = bm.mark()
      // Simulate some work
      for (let i = 0; i < 1000000; i++) {}
      
      const result = bm.diff(start)
      assert.ok(typeof result === 'number')
      assert.ok(result >= 0)
    })
  })

  describe('time', () => {
    it('should format time from Date object', () => {
      const date = new Date('2023-01-01T12:34:56')
      const result = bm.time(date)
      assert.equal(result, '12:34:56')
    })

    it('should format current time if no date provided', () => {
      const result = bm.time()
      assert.ok(/^\d{2}:\d{2}:\d{2}$/.test(result))
    })
  })

  describe('now', () => {
    it('should return current timestamp', () => {
      const result = bm.now()
      const now = Date.now()
      assert.ok(result >= now - 10 && result <= now + 10)
    })
  })

  describe('max', () => {
    it('should return maximum elapsed time for a marker', () => {
      // Create some elapsed time measurements
      bm.mark('max-test.start')
      bm.mark('max-test.end')
      bm.elapsedTime('max-test', false)
      
      const result = bm.max('max-test.start')
      assert.ok(typeof result === 'number')
    })
  })

  describe('min', () => {
    it('should return minimum elapsed time for a marker', () => {
      // Create some elapsed time measurements
      bm.mark('min-test.start')
      bm.mark('min-test.end')
      bm.elapsedTime('min-test', false)
      
      const result = bm.min('min-test.start')
      assert.ok(typeof result === 'number')
    })
  })

  describe('reset', () => {
    it('should reset all markers and statistics', () => {
      bm.mark('reset-test')
      bm.reset()
      
      const result = bm.get('reset-test')
      assert.equal(result, undefined)
    })
  })
})
