import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Identities } from '../src/index'

const { Taiwan, Singapore } = Identities

describe('Identities', () => {
	describe('isBusinessRegNumber', () => {
		it('should validate Taiwan business registration number', () => {
			const result = Identities.isBusinessRegNumber('TW', '12345678')
			assert.equal(result, true)
		})

		it('should validate Singapore business registration number', () => {
			const result = Identities.isBusinessRegNumber('SG', '201912345A')
			assert.equal(result, true)
		})

		it('should return true for unsupported country', () => {
			const result = Identities.isBusinessRegNumber('US', 'ABC123')
			assert.equal(result, true)
		})
	})

	describe('isNationalIdentity', () => {
		it('should validate Taiwan national identity', () => {
			const result = Identities.isNationalIdentity('TW', 'TP03121205005595')
			assert.equal(result, true)
		})

		it('should validate Singapore NRIC', () => {
			// Using a known valid test NRIC
			const result = Identities.isNationalIdentity('SG', '*********')
			assert.equal(result, true)
		})

		it('should return true for unsupported country', () => {
			const result = Identities.isNationalIdentity('US', 'ABC123')
			assert.equal(result, true)
		})
	})

	describe('Taiwan', () => {
		it('should validate citizen digital certificate number', () => {
			const idCardNumber = 'TP03121205005595'
			assert.equal(Taiwan.isCitizenDigitalCertNumber(idCardNumber), true)
		})

		it('should reject invalid citizen digital certificate number', () => {
			const idCardNumber = 'TP031212050'
			assert.equal(Taiwan.isCitizenDigitalCertNumber(idCardNumber), false)
		})

		it('should validate business registration number', () => {
			const businessNumber = '12345678'
			assert.equal(Taiwan.isBusinessRegNumber(businessNumber), true)
		})

		it('should reject invalid business registration number', () => {
			const businessNumber = '123456'
			assert.equal(Taiwan.isBusinessRegNumber(businessNumber), false)
		})
	})

	describe('Singapore', () => {
		it('should validate NRIC with correct checksum (S series)', () => {
			// Using a known valid test NRIC
			const nric = '*********'
			assert.equal(Singapore.isNRIC(nric), true)
		})

		it('should validate NRIC with correct checksum (F series)', () => {
			// Using a known valid test NRIC
			const nric = '*********'
			assert.equal(Singapore.isNRIC(nric), true)
		})

		it('should reject NRIC with incorrect format', () => {
			const nric = 'S92345'
			assert.equal(Singapore.isNRIC(nric), false)
		})

		it('should reject NRIC with incorrect checksum', () => {
			const nric = '*********' // Incorrect checksum
			assert.equal(Singapore.isNRIC(nric), false)
		})

		it('should validate business registration number (UEN)', () => {
			// Test various UEN formats
			assert.equal(Singapore.isBusinessRegNumber('12345678A'), true)
			assert.equal(Singapore.isBusinessRegNumber('201912345A'), true)
			assert.equal(Singapore.isBusinessRegNumber('T12LL1234A'), true)
		})

		it('should reject invalid business registration number', () => {
			assert.equal(Singapore.isBusinessRegNumber('123456'), false)
		})
	})
})
