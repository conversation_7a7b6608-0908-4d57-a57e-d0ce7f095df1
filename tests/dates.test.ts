import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Dates } from '../src/index'

const { sameDay, startOf, endOf, ceil, floor } = Dates

describe('Dates', () => {

	describe('sameDay', () => {
		it('should same', async () => {
			const same = sameDay(new Date(), new Date())
			assert.equal(same, true)
		})

		it('should not', async () => {
			const d1 = new Date('01/01/2022'),
				d2 = new Date('12/31/2021'),
				same = sameDay(d1, d2)

			assert.equal(same, false)
		})
	})

	describe('startOf', () => {
		it('should startOf day (now)', async () => {
			const today = new Date(),
				start = startOf('day')

			today.setHours(0)
			today.setMinutes(0)
			today.setSeconds(0)
			today.setMilliseconds(0)
			assert.deepEqual(start, today)
		})
		it('should startOf day (2020-01-01 18:20:19)', async () => {
			const date = new Date('2020-01-01T18:20:19'),
				start = startOf('day', date)

			date.setHours(0)
			date.setMinutes(0)
			date.setSeconds(0)
			date.setMilliseconds(0)
			assert.deepEqual(start, date)
		})
		it('should startOf tomorrow (relative)', async () => {
			const tomorrow = new Date(),
				start = startOf('day', 1)

			tomorrow.setDate(tomorrow.getDate() + 1)
			tomorrow.setHours(0)
			tomorrow.setMinutes(0)
			tomorrow.setSeconds(0)
			tomorrow.setMilliseconds(0)
			assert.deepEqual(start, tomorrow)
		})
	})

	describe('endOf', () => {
		it('should endOf month (now)', async () => {
			const today = new Date(),
				daysInMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate(),		// 0 as day returns last day of previous month
				end = endOf('month')

			today.setDate(daysInMonth)
			today.setHours(23)
			today.setMinutes(59)
			today.setSeconds(59)
			today.setMilliseconds(999)
			assert.deepEqual(end, today)
		})

		it('should endOf day before (relative)', async () => {
			const dayBefore = new Date(),
				end = endOf('day', -2)

			dayBefore.setDate(dayBefore.getDate() - 2)
			dayBefore.setHours(23)
			dayBefore.setMinutes(59)
			dayBefore.setSeconds(59)
			dayBefore.setMilliseconds(999)
			assert.deepEqual(end, dayBefore)
		})
	})

	describe('ceil', () => {
		const time = new Date('2020-01-01T17:00:00'),
			from = '09:00'

		it('should ceil with 15 minutes', async () => {
			const ceilTime = ceil(time, from, 15)
			assert.equal(ceilTime.getTime(), new Date('2020-01-01T17:00:00').getTime())
		})

		it('should ceil with 45 minutes', async () => {
			const ceilTime = ceil(time, from, 45)
			assert.equal(ceilTime.getTime(), new Date('2020-01-01T17:15:00').getTime())
		})
	})

	describe('floor', () => {
		const time = new Date('2020-01-01T18:20:19'),
			from = '09:00'

		it('should floor with 15 minutes', async () => {
			const floorTime = floor(time, from, 15)
			assert.equal(floorTime.getTime(), new Date('2020-01-01T18:15:00').getTime())
		})

		it('should floor with 60 minutes', async () => {
			const floorTime = floor(time, from, 60)
			assert.equal(floorTime.getTime(), new Date('2020-01-01T18:00:00').getTime())
		})
	})
})
