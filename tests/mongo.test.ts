import assert from 'node:assert'
import { describe, it } from 'node:test'
import { ObjectId, getDatabaseUrl, shortId } from '../src'
import isMongoId from 'validator/lib/isMongoId'

describe('Mongo', () => {

	describe('ObjectId', () => {
		it('should Create', async () => {
			const id = ObjectId()

			assert.equal(!!id, true)
		})
		it('should create from Hex String', async () => {
			const id = ObjectId('54495ad94c934721ede76d90')

			assert.equal(!!id, true)
		})
		it('should create from Time', async () => {
			const id = ObjectId(1414093117)

			assert.equal(!!id, true)
		})
	})

	describe('getDatabaseUrl', () => {
		it('should create basic connection string with required fields', () => {
			const options = {
				name: 'testdb',
				host: 'localhost:27017',
				authDb: 'admin'
			}
			const url = getDatabaseUrl(options)

			assert.match(url, /^mongodb:\/\/localhost:27017\/testdb\?authSource=admin/)
			assert.match(url, /maxPoolSize=5/)
		})

		it('should include username and password when provided', () => {
			const options = {
				name: 'testdb',
				host: 'localhost:27017',
				username: 'testuser',
				password: 'testpass',
				authDb: 'admin'
			}
			const url = getDatabaseUrl(options)

			assert.match(url, /^mongodb:\/\/testuser:testpass@localhost:27017\/testdb\?authSource=admin/)
		})

		it('should include replica set configuration when dbSet is provided', () => {
			const options = {
				name: 'testdb',
				host: 'localhost:27017',
				authDb: 'admin',
				dbSet: 'rs0'
			}
			const url = getDatabaseUrl(options)

			assert.match(url, /replicaSet=rs0/)
			assert.match(url, /readPreference=primaryPreferred/)
			assert.match(url, /slaveOk=true/)
		})

		it('should use custom maxPoolSize when provided', () => {
			const options = {
				name: 'testdb',
				host: 'localhost:27017',
				authDb: 'admin',
				maxPoolSize: '10'
			}
			const url = getDatabaseUrl(options)

			assert.match(url, /maxPoolSize=10/)
		})
	})

	describe('shortId', () => {
		it('should generate id with default length', () => {
			const id = shortId()

			assert.equal(id.length, 10)
		})

		it('should generate id with custom length', () => {
			const id = shortId(15)

			assert.equal(id.length, 15)
		})
	})

	describe('isMongoId', () => {
		it('should validate correct MongoDB ObjectId', () => {
			const validId = '507f1f77bcf86cd799439011'

			assert.equal(isMongoId(validId), true)
		})

		it('should reject invalid MongoDB ObjectId', () => {
			const invalidId = 'not-a-mongo-id'

			assert.equal(isMongoId(invalidId), false)
		})

		it('should reject empty string', () => {
			assert.equal(isMongoId(''), false)
		})
	})
})
