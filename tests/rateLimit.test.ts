import assert from 'node:assert'
import { describe, it } from 'node:test'
import { delay, rateLimit } from '../src/index'

describe('RateLimit', () => {
  describe('delay', () => {
    it('should delay execution for specified milliseconds', async () => {
      const start = Date.now()
      const delayTime = 100

      await delay(delayTime)

      const elapsed = Date.now() - start
      assert.ok(elapsed >= delayTime - 10, `Delay should be at least ${delayTime - 10}ms`)
    })
  })

  describe('rateLimit', () => {
    it('should create a rate limiter with specified parameters', () => {
      const rate = {
        limit: 10,
        interval: 1000
      }

      const limiter = rateLimit(rate)

      assert.equal(typeof limiter.removeTokens, 'function')
      assert.equal(limiter.tokenBucket.bucketSize, 10)
    })

    it('should throw error if limit or interval is missing', () => {
      const rate = {
        limit: 0,
        interval: 1000
      }

      assert.throws(() => {
        rateLimit(rate)
      }, /Not rate limited, missing parameters/)
    })

    it('should apply rate limiting to specified methods', async () => {
      // Create a test object with methods to rate limit
      const testObj = {
        counter: 0,
        increment() {
          this.counter += 1
          return this.counter
        }
      }

      const rate = {
        limit: 2,
        interval: 1000,
        methods: ['increment']
      }

      // Apply rate limiting
      const limiter = rateLimit(rate, testObj)

      // First two calls should work immediately
      const result1 = await testObj.increment()
      const result2 = await testObj.increment()

      assert.equal(result1, 1)
      assert.equal(result2, 2)

      // Third call should be rate limited, but we'll allow it to complete
      // by waiting for tokens to be available
      await limiter.removeTokens(1)

      const result3 = await testObj.increment()
      assert.equal(result3, 3)
    })

    it('should throw error if method not found', () => {
      const testObj = {
        doSomething() {}
      }

      const rate = {
        limit: 10,
        interval: 1000,
        methods: ['nonExistentMethod']
      }

      assert.throws(() => {
        rateLimit(rate, testObj)
      }, /Method 'nonExistentMethod' to rate limit not found/)
    })

    it('should handle nested methods', async () => {
      const testObj = {
        nested: {
          doSomething() {
            return 'done'
          }
        }
      }

      const rate = {
        limit: 10,
        interval: 1000,
        methods: ['nested.doSomething']
      }

      rateLimit(rate, testObj)

      // Verify the method still works after rate limiting
      // Since the method is now wrapped with a rate limiter, it returns a Promise
      const result = await testObj.nested.doSomething()
      assert.equal(result, 'done')
    })
  })
})
