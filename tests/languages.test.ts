import assert from 'node:assert'
import { describe, it } from 'node:test'
import { bestLanguage, localizeContent, languageOf, mergeGlobalize } from '../src/index'

describe('Languages', () => {
  describe('bestLanguage', () => {
    it('should find exact match in supported languages', () => {
      const preferred = ['en', 'zh-TW']
      const supported = ['en', 'zh-Hant', 'fr']
      const result = bestLanguage(preferred, supported)
      assert.equal(result, 'en')
    })

    it('should map Chinese language codes correctly', () => {
      const preferred = ['zh-TW']
      const supported = ['zh-Hant', 'en']
      const result = bestLanguage(preferred, supported)
      assert.equal(result, 'zh-Hant')
    })

    it('should handle Hong Kong Chinese correctly', () => {
      const preferred = ['zh-HK']
      const supported = ['zh-Hant-HK', 'zh-Hant', 'en']
      const result = bestLanguage(preferred, supported)
      assert.equal(result, 'zh-Hant-HK')
    })

    it('should use fallback when no match found', () => {
      const preferred = ['fr', 'de']
      const supported = ['en', 'zh-Hant']
      const fallback = 'en'
      const result = bestLanguage(preferred, supported, fallback)
      assert.equal(result, 'en')
    })

    it('should handle empty preferred languages', () => {
      const preferred: string[] = []
      const supported = ['en', 'zh-Hant']
      const fallback = 'en'
      const result = bestLanguage(preferred, supported, fallback)
      assert.equal(result, 'en')
    })
  })

  describe('localizeContent', () => {
    it('should return content for best matching language', () => {
      const preferred = ['en', 'zh-TW']
      const t = {
        'en': { message: 'Hello' },
        'zh-Hant': { message: '你好' }
      }
      const fallback = 'en'
      const result = localizeContent(preferred, t, fallback)
      assert.deepEqual(result, { message: 'Hello' })
    })

    it('should use fallback when no match found', () => {
      const preferred = ['fr', 'de']
      const t = {
        'en': { message: 'Hello' },
        'zh-Hant': { message: '你好' }
      }
      const fallback = 'en'
      const result = localizeContent(preferred, t, fallback)
      assert.deepEqual(result, { message: 'Hello' })
    })

    it('should use first available language when fallback not found', () => {
      const preferred = ['fr', 'de']
      const t = {
        'en': { message: 'Hello' },
        'zh-Hant': { message: '你好' }
      }
      const fallback = 'fr' // Not available
      const result = localizeContent(preferred, t, fallback)
      assert.deepEqual(result, { message: 'Hello' })
    })
  })

  describe('languageOf', () => {
    it('should extract language from locale object', () => {
      const locale = { languages: ['en-US'] }
      const result = languageOf(locale)
      // The function returns the language code without the country code
      assert.equal(result, 'en-US')
    })

    it('should handle dialect correctly', () => {
      const locale = { languages: ['zh-Hant-TW'] }
      const result = languageOf(locale)
      assert.equal(result, 'zh-Hant')
    })

    it('should handle Hong Kong Chinese correctly', () => {
      const locale = { languages: ['zh-Hant-HK'] }
      const result = languageOf(locale)
      assert.equal(result, 'zh-Hant-HK')
    })

    it('should handle Macau Chinese correctly', () => {
      const locale = { languages: ['zh-Hant-MO'] }
      const result = languageOf(locale)
      assert.equal(result, 'zh-Hant-HK')
    })

    it('should handle empty locale', () => {
      const result = languageOf()
      assert.equal(result, '')
    })
  })

  describe('mergeGlobalize', () => {
    it('should merge globalize data with specified language', () => {
      const obj = {
        id: '123',
        globalize: {
          default: 'en',
          t: {
            'en': { name: 'English Name' },
            'zh-Hant': { name: '中文名稱' }
          }
        }
      }
      const result = mergeGlobalize(obj, 'zh-Hant')
      assert.deepEqual(result, {
        id: '123',
        name: '中文名稱'
      })
    })

    it('should use default language when specified language not found', () => {
      const obj = {
        id: '123',
        globalize: {
          default: 'en',
          t: {
            'en': { name: 'English Name' }
          }
        }
      }
      const result = mergeGlobalize(obj, 'zh-Hant')
      assert.deepEqual(result, {
        id: '123',
        name: 'English Name'
      })
    })

    it('should return original object if no globalize property', () => {
      const obj = { id: '123', name: 'Test' }
      const result = mergeGlobalize(obj, 'en')
      assert.deepEqual(result, obj)
    })
  })
})
