import assert from 'node:assert'
import { test } from 'node:test'
import { Multitenancy } from '../src/index'

const { generateTenantCode } = Multitenancy

test('Multitenancy', async (t) => {

	await t.test('generateTenantCode', () => {
		const code1 = generateTenantCode('glico', 'tw'),
			code2 = generateTenantCode('蒸餃阿姨', 'tw'),
			code3 = generateTenantCode('滷之鄉', 'tw'),
			code4 = generateTenantCode('瑞幸咖啡', 'cn'),
			code5 = generateTenantCode('Starbucks Coffee Singapore', 'sg')

		assert.match(code1, /^glico\d{3}-tw$/)
		assert.match(code2, /^zhengjiaoayi\d{3}-tw$/)
		assert.match(code3, /^luzhixiang\d{3}-tw$/)
		assert.match(code4, /^ruixingkafei\d{3}-cn$/)
		assert.match(code5, /^strbckscffsngpr\d{3}-sg$/)
	})
})
