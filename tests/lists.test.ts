import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Lists } from '../src/index'

const { splitIntoChunks } = Lists

describe('Lists', () => {
  describe('splitIntoChunks', () => {
    it('should split an array into chunks of the specified size', () => {
      const arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
      const result = splitIntoChunks(arr, 3)
      
      assert.equal(result.length, 4)
      assert.deepEqual(result[0], [1, 2, 3])
      assert.deepEqual(result[1], [4, 5, 6])
      assert.deepEqual(result[2], [7, 8, 9])
      assert.deepEqual(result[3], [10])
    })

    it('should handle empty arrays', () => {
      const arr: any[] = []
      const result = splitIntoChunks(arr, 3)
      
      assert.equal(result.length, 0)
      assert.deepEqual(result, [])
    })

    it('should handle arrays smaller than chunk size', () => {
      const arr = [1, 2, 3]
      const result = splitIntoChunks(arr, 5)
      
      assert.equal(result.length, 1)
      assert.deepEqual(result[0], [1, 2, 3])
    })

    it('should handle arrays equal to chunk size', () => {
      const arr = [1, 2, 3, 4, 5]
      const result = splitIntoChunks(arr, 5)
      
      assert.equal(result.length, 1)
      assert.deepEqual(result[0], [1, 2, 3, 4, 5])
    })

    it('should use default chunk size if not specified', () => {
      const arr = Array(10000).fill(1)
      const result = splitIntoChunks(arr)
      
      assert.equal(result.length, 2)
      assert.equal(result[0].length, 5000)
      assert.equal(result[1].length, 5000)
    })

    it('should handle arrays of objects', () => {
      const arr = [{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }, { id: 5 }]
      const result = splitIntoChunks(arr, 2)
      
      assert.equal(result.length, 3)
      assert.deepEqual(result[0], [{ id: 1 }, { id: 2 }])
      assert.deepEqual(result[1], [{ id: 3 }, { id: 4 }])
      assert.deepEqual(result[2], [{ id: 5 }])
    })

    it('should handle arrays of strings', () => {
      const arr = ['a', 'b', 'c', 'd', 'e']
      const result = splitIntoChunks(arr, 2)
      
      assert.equal(result.length, 3)
      assert.deepEqual(result[0], ['a', 'b'])
      assert.deepEqual(result[1], ['c', 'd'])
      assert.deepEqual(result[2], ['e'])
    })
  })
})
