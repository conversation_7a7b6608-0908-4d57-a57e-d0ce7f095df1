import assert from 'node:assert'
import { describe, it } from 'node:test'
import { merge, Objects, findPath } from '../src/index'
import { map } from 'traverse'

const { pickAndMap, pickDeepValue, substitute } = Objects

const person = {
	givenName: '<PERSON>',
	familyName: 'Tan',
	birth: { date: '12/12/12', place: { city: 'new york' } },
	emailOpt: [{ type: 'email', status: true }],
	phone: '98765432',
	email: '<EMAIL>'
}

describe('Objects', () => {
	it('should pickAndMap', () => {
		const mapping = {
				givenName: 'first_name',
				familyName: 'last_name',
				'birth.place.city': 'city',
				'birth.place': 'place',
				'emailOpt.0.status': 'optin',
			},
			data = pickAndMap(person, mapping),
			{ optin, city } = data

		assert.equal(optin, true)
		assert.equal(city, 'new york')
	})

	it('should pickDeepValue', () => {
		const prop1 = 'birth.place',
			prop2 = 'email',
			result1 = pickDeepValue(person, prop1),
			result2 = pickDeepValue(person, prop2)

		assert.equal(person.birth.place, result1)
		assert.equal(person.email, result2)
	})

	describe('substitute', () => {
		const string = 'test',
			number = 123,
			boolean = true,
			object = { string, number, boolean },
			data = {
				object: { toMerge: object },
				string: { toMerge: string },
				number: { toMerge: number },
				boolean: { toMerge: boolean }
			}

		it('should substitute string template', () => {
			const template = '#{string.toMerge} #{object.toMerge.boolean}',
				result = substitute(template, data)

			assert.equal(result, `${string} ${object.boolean}`)
		})
		
		it('should substitute object template', () => {
			const template = {
					object: '#{object.toMerge}',
					string: '#{string.toMerge}',
					number: '#{number.toMerge}',
					boolean: '#{boolean.toMerge}'
				},
				result = substitute(template, data)

			assert.deepStrictEqual(result, { object, string, number, boolean })
		})

		it('should not substitute if tag is different', () => {
			const template = {
					string: '#{string.toMerge}',
					number: '#{number.toMerge}',
					boolean: '#{boolean.toMerge}'
				},
				options = { prefix: '{{', suffix: '}}' },
				result = substitute(template, data, options)

			assert.deepStrictEqual(result, template)
		})

		it('should substitute missing value with null', () => {
			const template = {
					string: '#{string.toMerge}',
					missing: '#{missing.toMerge}'
				},
				result = substitute(template, data)

			assert.deepStrictEqual(result, { string, missing: null })
		})
	})

	describe('merge', () => {
		it('should merge arrays of objects', () => {
			const arr1 = [
				{ a: 1, b: 1, c: 1, d:[{e:1, f:1}] },
				{ a: 2, b: 2, c: 2, d:[{e:2, f:2}] }
			],
			 arr2 = [
				{ a: 3, b: 3, c: 3, d:[{e:3, f:3}] },
				{ a: 4, b: 4, c: 4, d:[{e:4, f:4}] }
			]
			const overwriteMerge = (destinationArray: any[], sourceArray: any[], options: any) => sourceArray,
				result = arr1.map((item, index) => merge(item, arr2[index], { arrayMerge: overwriteMerge }))

			assert.deepStrictEqual(result, [
				{ a: 3, b: 3, c: 3, d:[{e:3, f:3}] },
				{ a: 4, b: 4, c: 4, d:[{e:4, f:4}] }
			])
		})
	})

	describe('merge', () => {
		it('should merge arrays of objects', () => {
			const arr1 = [
				{ a: 1, b: 1, c: 1 },
				{ a: 2, b: 2, c: 2 },
				{ a: 3, b: 3, c: 3 },
			],
			 arr2 = [
				{ a: 4, b: 4, c: 4 },
				{ a: 5, b: 5, c: 5 }
			]

			const overwriteMerge = (destinationArray: any[], sourceArray: any[], options: any) => sourceArray,
				result = merge(arr1, arr2, { arrayMerge: overwriteMerge })
			
			assert.deepStrictEqual(result, [
				{ a: 4, b: 4, c: 4 },
				{ a: 5, b: 5, c: 5 }
			])
		})
	})

	describe('findPath', () => {
		it('should find path of an given value', () => {
			const obj = {
				"a": {
				  "b": {
					"c": "123",
					"d": "456"
				  }
				},
				"e": {
				  "f": {
					"g": "789",
					"h": "012"
				  }
				}
			  }
			const result = findPath(obj, '123')
			assert.deepStrictEqual(result, {
				"a": {
				  "b": {
					"c": "123"
				  }
				}
			  })
		})
	})
})
