import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Addresses } from '../src/index'

const { addressComponents2Address, formatAddress, completeAddress } = Addresses

// Taiwan address test data
const twGeocode = {
	"address_components": [
		{
			"long_name": "128",
			"short_name": "128",
			"types": [
				"street_number"
			]
		},
		{
			"long_name": "舊宗路一段",
			"short_name": "舊宗路一段",
			"types": [
				"route"
			]
		},
		{
			"long_name": "湖元里",
			"short_name": "湖元里",
			"types": [
				"administrative_area_level_4",
				"political"
			]
		},
		{
			"long_name": "內湖區",
			"short_name": "內湖區",
			"types": [
				"administrative_area_level_3",
				"political"
			]
		},
		{
			"long_name": "台北市",
			"short_name": "台北市",
			"types": [
				"administrative_area_level_1",
				"political"
			]
		},
		{
			"long_name": "台灣",
			"short_name": "TW",
			"types": [
				"country",
				"political"
			]
		},
		{
			"long_name": "114",
			"short_name": "114",
			"types": [
				"postal_code"
			]
		}
	],
	"formatted_address": "114台灣台北市內湖區舊宗路一段128號",
	"geometry": {
		"location": {
			"lat": 25.0609683,
			"lng": 121.5780389
		},
		"location_type": "ROOFTOP",
		"viewport": {
			"northeast": {
				"lat": 25.0622265802915,
				"lng": 121.5791633802915
			},
			"southwest": {
				"lat": 25.0595286197085,
				"lng": 121.5764654197085
			}
		}
	},
	"place_id": "ChIJPfJ8QYGrQjQRhdl_zz2iV9I",
	"plus_code": {
		"compound_code": "3H6H+96 台灣台北市內湖區",
		"global_code": "7QQ33H6H+96"
	},
	"types": [
		"street_address"
	]
},
twConverted = {
	type: 'store',
	level: undefined,
	house: '128',
	city: undefined,
	street: '湖元里 舊宗路一段',
	state: '台北市',
	country: 'TW',
	postCode: '114',
	formatted: '114台灣台北市內湖區舊宗路一段128號',
	short: '內湖區舊宗路一段128號',
	optIn: true
}

// Singapore address test data
const sgGeocode = {
	"address_components": [
		{
			"long_name": "10",
			"short_name": "10",
			"types": [
				"street_number"
			]
		},
		{
			"long_name": "Bayfront Avenue",
			"short_name": "Bayfront Ave",
			"types": [
				"route"
			]
		},
		{
			"long_name": "Downtown Core",
			"short_name": "Downtown Core",
			"types": [
				"neighborhood",
				"political"
			]
		},
		{
			"long_name": "Singapore",
			"short_name": "Singapore",
			"types": [
				"locality",
				"political"
			]
		},
		{
			"long_name": "Singapore",
			"short_name": "SG",
			"types": [
				"country",
				"political"
			]
		},
		{
			"long_name": "018956",
			"short_name": "018956",
			"types": [
				"postal_code"
			]
		}
	],
	"formatted_address": "10 Bayfront Avenue, Singapore 018956",
	"geometry": {
		"location": {
			"lat": 1.2839,
			"lng": 103.8607
		},
		"location_type": "ROOFTOP",
		"viewport": {
			"northeast": {
				"lat": 1.2852,
				"lng": 103.8620
			},
			"southwest": {
				"lat": 1.2826,
				"lng": 103.8594
			}
		}
	},
	"place_id": "ChIJAVHa7hgZ2jERxzG0J9bNcb0",
	"plus_code": {
		"compound_code": "7V8Q+H7 Singapore",
		"global_code": "6PH57V8Q+H7"
	},
	"types": [
		"street_address"
	]
}

// US address test data
const usGeocode = {
	"address_components": [
		{
			"long_name": "1600",
			"short_name": "1600",
			"types": [
				"street_number"
			]
		},
		{
			"long_name": "Amphitheatre Parkway",
			"short_name": "Amphitheatre Pkwy",
			"types": [
				"route"
			]
		},
		{
			"long_name": "Mountain View",
			"short_name": "Mountain View",
			"types": [
				"locality",
				"political"
			]
		},
		{
			"long_name": "Santa Clara County",
			"short_name": "Santa Clara County",
			"types": [
				"administrative_area_level_2",
				"political"
			]
		},
		{
			"long_name": "California",
			"short_name": "CA",
			"types": [
				"administrative_area_level_1",
				"political"
			]
		},
		{
			"long_name": "United States",
			"short_name": "US",
			"types": [
				"country",
				"political"
			]
		},
		{
			"long_name": "94043",
			"short_name": "94043",
			"types": [
				"postal_code"
			]
		}
	],
	"formatted_address": "1600 Amphitheatre Pkwy, Mountain View, CA 94043, USA",
	"geometry": {
		"location": {
			"lat": 37.4224,
			"lng": -122.0841
		},
		"location_type": "ROOFTOP",
		"viewport": {
			"northeast": {
				"lat": 37.4237,
				"lng": -122.0828
			},
			"southwest": {
				"lat": 37.4211,
				"lng": -122.0854
			}
		}
	},
	"place_id": "ChIJ2eUgeAK6j4ARbn5u_wAGqWA",
	"plus_code": {
		"compound_code": "CWC8+W9 Mountain View, CA, USA",
		"global_code": "849VCWC8+W9"
	},
	"types": [
		"street_address"
	]
}

describe('Addresses', () => {
	describe('addressComponents2Address', () => {
		it('should convert Taiwan geocode to address', () => {
			const address = addressComponents2Address(twGeocode.address_components, twGeocode.formatted_address)
			assert.deepStrictEqual(address, twConverted)
		})

		it('should convert Singapore geocode to address', () => {
			const address = addressComponents2Address(sgGeocode.address_components, sgGeocode.formatted_address)
			assert.equal(address.country, 'SG')
			assert.equal(address.postCode, '018956')
			assert.equal(address.house, '10')
			// The function is using long_name instead of short_name for the street
			assert.equal(address.street, 'Bayfront Avenue')
			assert.equal(address.state, 'Downtown Core')
			assert.equal(address.city, 'Singapore')
		})

		it('should convert US geocode to address', () => {
			const address = addressComponents2Address(usGeocode.address_components, usGeocode.formatted_address)
			assert.equal(address.country, 'US')
			assert.equal(address.postCode, '94043')
			assert.equal(address.house, '1600')
			// The function is using long_name instead of short_name for the street
			assert.equal(address.street, 'Amphitheatre Parkway')
			assert.equal(address.state, 'CA')
			assert.equal(address.city, 'Mountain View')
		})

		it('should handle empty address components', () => {
			const address = addressComponents2Address([], 'Empty Address')
			assert.equal(address.formatted, 'Empty Address')
			assert.equal(address.type, 'store')
			assert.equal(address.optIn, true)
		})
	})

	describe('formatAddress', () => {
		it('should format a Taiwan address', () => {
			const address = {
				house: '128',
				street: '舊宗路一段',
				state: '台北市',
				country: 'TW',
				postCode: '114'
			}
			const formatted = formatAddress(address)
			assert.ok(formatted.formatted)
			assert.ok(formatted.short)
		})

		it('should format a Singapore address', () => {
			const address = {
				house: '10',
				street: 'Bayfront Avenue',
				city: 'Singapore',
				country: 'SG',
				postCode: '018956'
			}
			const formatted = formatAddress(address)
			assert.ok(formatted.formatted)
			assert.ok(formatted.short)
		})

		it('should format an address with unit and floor', () => {
			const address = {
				house: '10',
				street: 'Bayfront Avenue',
				city: 'Singapore',
				country: 'SG',
				postCode: '018956',
				unit: '42',
				floor: '3'
			}
			const formatted = formatAddress(address)
			assert.ok(formatted.formatted)
			assert.ok(formatted.short)

			// The unit and floor might be formatted in different ways depending on the country format
			// Just check that the formatted address exists
			assert.ok(formatted.formatted.length > 0)
			assert.ok(formatted.short.length > 0)
		})

		it('should handle minimal address information', () => {
			const address = {
				street: 'Main Street'
			}
			const formatted = formatAddress(address)
			assert.equal(formatted.street, 'Main Street')
			// Just check that formatted and short properties exist
			assert.ok('formatted' in formatted)
			assert.ok('short' in formatted)
		})
	})

	describe('completeAddress', () => {
		it('should complete an address with geocoding information', () => {
			const partialAddress = {
				house: '128'
			}
			const completed = completeAddress(partialAddress, twGeocode)
			assert.equal(completed.house, '128') // Should keep existing data
			assert.equal(completed.street, '湖元里 舊宗路一段') // Should add missing data
			assert.equal(completed.state, '台北市')
			assert.equal(completed.country, 'TW')
			assert.equal(completed.postCode, '114')
			assert.ok(completed.geo) // Should add geo data
			assert.equal(completed.geo.type, 'Point')
			assert.deepStrictEqual(completed.geo.coordinates, [121.5780389, 25.0609683])
			assert.equal(completed.valid, true)
		})

		it('should not overwrite existing address data', () => {
			const existingAddress = {
				house: '100', // Different from geocode
				street: 'Custom Street',
				state: 'Custom State',
				country: 'US',
				postCode: '12345',
				valid: false
			}
			const completed = completeAddress(existingAddress, twGeocode)
			assert.equal(completed.house, '100') // Should keep existing data
			assert.equal(completed.street, 'Custom Street')
			assert.equal(completed.state, 'Custom State')
			assert.equal(completed.country, 'US')
			assert.equal(completed.postCode, '12345')
			assert.equal(completed.valid, false)
			assert.ok(completed.geo) // Should add geo data
		})

		it('should handle objects with toJSON method', () => {
			const addressWithToJSON = {
				house: '128',
				toJSON: function() {
					return {
						house: this.house,
						custom: 'value'
					}
				}
			}
			const completed = completeAddress(addressWithToJSON, twGeocode)
			assert.equal(completed.house, '128')
			assert.equal(completed.custom, 'value')
			assert.equal(completed.street, '湖元里 舊宗路一段')
		})
	})
})