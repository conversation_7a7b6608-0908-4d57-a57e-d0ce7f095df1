import { formatDateTime as dayjs } from '@perkd/format-datetime'
import { daysToAnniversary, daysSinceAnniversary } from '../src'

const end = dayjs({ hour: 23, minute: 59 }),
	start = end.add(1, 'm'),
	testTimezone = dayjs().tz('Asia/Singapore')

console.log(end.format('HH:mm'))
console.log(start.format('HH:mm'))
console.log(testTimezone.format('HH:mm'))

console.log(dayjs().diff(new Date('2000-01-01'), 'year'))
console.log(dayjs().diff(new Date('2023-04-10'), 'day'))

const date = new Date('2024-04-15'),
	join = dayjs(date),
	yearsSince = dayjs().diff(join, 'year'),
	anniversary = join.add(yearsSince + 1, 'year')

console.log(`yearsSince ${yearsSince}`)
console.log('daySince ', anniversary.diff(dayjs(), 'day') + 1)
console.log(daysToAnniversary(date))

console.log('daysSinceAnniversary', daysSinceAnniversary(date))
