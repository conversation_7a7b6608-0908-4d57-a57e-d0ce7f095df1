import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Flows } from '../src/index'

const { nextStepOf, preStepOf, stepOf, hasStep, atStep, isLastStep } = Flows

describe('Flows', () => {
  // Sample flow for testing
  const testFlow = {
    at: 1,
    steps: ['start', 'middle', 'end']
  }

  describe('nextStepOf', () => {
    it('should return the index of the next step', () => {
      const result = nextStepOf('start', testFlow)
      assert.equal(result, 1)
    })

    it('should return the index of the current step if it is the last step', () => {
      const result = nextStepOf('end', testFlow)
      assert.equal(result, 2)
    })

    it('should handle non-existent steps', () => {
      const result = nextStepOf('nonexistent', testFlow)
      assert.equal(result, -1)
    })
  })

  describe('preStepOf', () => {
    it('should return the index of the previous step', () => {
      const result = preStepOf('middle', testFlow)
      assert.equal(result, 0)
    })

    it('should return the index of the current step if it is the first step', () => {
      const result = preStepOf('start', testFlow)
      assert.equal(result, 0)
    })

    it('should handle non-existent steps', () => {
      const result = preStepOf('nonexistent', testFlow)
      assert.equal(result, -1)
    })
  })

  describe('stepOf', () => {
    it('should return the index of the given step', () => {
      const result = stepOf('middle', testFlow)
      assert.equal(result, 1)
    })

    it('should return -1 for non-existent steps', () => {
      const result = stepOf('nonexistent', testFlow)
      assert.equal(result, -1)
    })
  })

  describe('hasStep', () => {
    it('should return true if the step exists in the flow', () => {
      const result = hasStep('middle', testFlow)
      assert.equal(result, true)
    })

    it('should return false if the step does not exist in the flow', () => {
      const result = hasStep('nonexistent', testFlow)
      assert.equal(result, false)
    })

    it('should handle flow with undefined steps', () => {
      const invalidFlow = { at: 0 } as any
      const result = hasStep('start', invalidFlow)
      assert.equal(result, false)
    })
  })

  describe('atStep', () => {
    it('should return true if the current step matches the given step', () => {
      const result = atStep('middle', testFlow)
      assert.equal(result, true)
    })

    it('should return false if the current step does not match the given step', () => {
      const result = atStep('start', testFlow)
      assert.equal(result, false)
    })

    it('should return false for non-existent steps', () => {
      const result = atStep('nonexistent', testFlow)
      assert.equal(result, false)
    })
  })

  describe('isLastStep', () => {
    it('should return true if the given step is the last step', () => {
      const result = isLastStep(testFlow, 'end')
      assert.equal(result, true)
    })

    it('should return false if the given step is not the last step', () => {
      const result = isLastStep(testFlow, 'middle')
      assert.equal(result, false)
    })

    it('should return true if the current step is the last step', () => {
      const lastStepFlow = { ...testFlow, at: 2 }
      const result = isLastStep(lastStepFlow)
      assert.equal(result, true)
    })

    it('should return false if the current step is not the last step', () => {
      const result = isLastStep(testFlow)
      assert.equal(result, false)
    })
  })
})
