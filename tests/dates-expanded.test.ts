import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Dates } from '../src/index'

const {
  sameDay,
  isSameDay,
  diffDays,
  startOf,
  endOf,
  dayOfWeek,
  timestamp,
  parseTime,
  ceil,
  floor,
  yearsSince,
  daysSince,
  daysToAnniversary,
  daysSinceAnniversary,
  getDate,
  isValidDate,
  isDateObject,
  isDateDetail,
  toDate,
  toDateDetail,
  getDay,
  getNextDateByWeekday,
  getISODate,
  lastDayOfMonth,
  timeIsBetween
} = Dates

describe('Dates Expanded', () => {
  describe('isSameDay', () => {
    it('should return true for same day', () => {
      const d1 = new Date('2023-01-01T10:00:00Z')
      const d2 = new Date('2023-01-01T18:00:00Z')
      assert.equal(isSameDay(d1, d2), true)
    })

    it('should return false for different days', () => {
      const d1 = new Date('2023-01-01T10:00:00Z')
      const d2 = new Date('2023-01-02T10:00:00Z')
      assert.equal(isSameDay(d1, d2), false)
    })
  })

  describe('diffDays', () => {
    it('should calculate difference in days', () => {
      const d1 = new Date('2023-01-01')
      const d2 = new Date('2023-01-05')
      assert.equal(diffDays(d1, d2), 4)
    })

    it('should handle null inputs', () => {
      assert.equal(diffDays(null, new Date()), null)
      assert.equal(diffDays(new Date(), null), null)
    })
  })

  describe('dayOfWeek', () => {
    it('should return correct day of week for Date object', () => {
      // Monday
      const monday = new Date('2023-01-02')
      assert.equal(dayOfWeek(monday), 1)
      
      // Sunday
      const sunday = new Date('2023-01-01')
      assert.equal(dayOfWeek(sunday), 7)
    })

    it('should return correct day of week for date detail object', () => {
      // Monday
      const monday = { year: 2023, month: 1, day: 2 }
      assert.equal(dayOfWeek(monday), 1)
    })
  })

  describe('timestamp', () => {
    it('should convert date to unix timestamp', () => {
      const date = new Date('2023-01-01T00:00:00Z')
      const ts = timestamp(date.toISOString())
      assert.equal(ts, Math.floor(date.getTime() / 1000))
    })

    it('should return current timestamp when no date provided', () => {
      const ts = timestamp()
      const now = Math.floor(Date.now() / 1000)
      assert.ok(ts >= now - 1 && ts <= now + 1)
    })
  })

  describe('parseTime', () => {
    it('should parse date string', () => {
      const dateStr = '2023-01-01T00:00:00Z'
      const result = parseTime(dateStr)
      assert.ok(result instanceof Date)
      assert.equal(result.toISOString(), new Date(dateStr).toISOString())
    })

    it('should return Date object as is', () => {
      const date = new Date('2023-01-01T00:00:00Z')
      const result = parseTime(date)
      assert.equal(result, date)
    })

    it('should parse time string', () => {
      const result = parseTime('10:00')
      assert.ok(result instanceof Date)
    })
  })

  describe('ceil', () => {
    it('should ceil time to next interval', () => {
      const time = new Date('2023-01-01T10:31:00')
      const result = ceil(time, '10:00', 30)
      assert.equal(result.getHours(), 11)
      assert.equal(result.getMinutes(), 0)
    })

    it('should not change time if already at interval', () => {
      const time = new Date('2023-01-01T10:30:00')
      const result = ceil(time, '10:00', 30)
      assert.equal(result.getHours(), 10)
      assert.equal(result.getMinutes(), 30)
    })
  })

  describe('floor', () => {
    it('should floor time to previous interval', () => {
      const time = new Date('2023-01-01T10:31:00')
      const result = floor(time, '10:00', 30)
      assert.equal(result.getHours(), 10)
      assert.equal(result.getMinutes(), 30)
    })

    it('should not change time if already at interval', () => {
      const time = new Date('2023-01-01T10:30:00')
      const result = floor(time, '10:00', 30)
      assert.equal(result.getHours(), 10)
      assert.equal(result.getMinutes(), 30)
    })
  })

  describe('yearsSince', () => {
    it('should calculate years since date', () => {
      const now = new Date()
      const pastDate = new Date(now.getFullYear() - 5, now.getMonth(), now.getDate())
      assert.equal(yearsSince(pastDate), 5)
    })
  })

  describe('daysSince', () => {
    it('should calculate days since date (inclusive)', () => {
      const now = new Date()
      const yesterday = new Date(now)
      yesterday.setDate(now.getDate() - 1)
      assert.equal(daysSince(yesterday), 2) // inclusive, so 2 days
    })
  })

  describe('daysToAnniversary', () => {
    it('should calculate days to next anniversary', () => {
      const now = new Date()
      const nextYear = new Date(now)
      nextYear.setFullYear(now.getFullYear() + 1)
      
      // If the anniversary is exactly one year from now
      assert.equal(daysToAnniversary(now), 366) // 365 days + inclusive counting
    })
  })

  describe('daysSinceAnniversary', () => {
    it('should calculate days since last anniversary', () => {
      const now = new Date()
      
      // If today is the anniversary
      assert.equal(daysSinceAnniversary(now), 1) // inclusive counting
    })
  })

  describe('isValidDate', () => {
    it('should return true for valid dates', () => {
      assert.equal(isValidDate(new Date()), true)
      assert.equal(isValidDate('2023-01-01'), true)
    })

    it('should return false for invalid dates', () => {
      assert.equal(isValidDate(null), false)
      assert.equal(isValidDate(undefined), false)
      assert.equal(isValidDate('not a date'), false)
    })
  })

  describe('isDateObject', () => {
    it('should return true for valid Date objects', () => {
      assert.equal(isDateObject(new Date()), true)
    })

    it('should return false for invalid Date objects', () => {
      assert.equal(isDateObject(new Date('invalid')), false)
      assert.equal(isDateObject('2023-01-01'), false)
      assert.equal(isDateObject(null), false)
    })
  })

  describe('isDateDetail', () => {
    it('should return true for valid DateDetail objects', () => {
      assert.equal(isDateDetail({ year: 2023, month: 1, day: 1 }), true)
    })

    it('should return false for non-DateDetail objects', () => {
      assert.equal(isDateDetail(new Date()), false)
      assert.equal(isDateDetail('2023-01-01'), false)
      assert.equal(isDateDetail(null), false)
    })
  })

  describe('toDate', () => {
    it('should convert DateDetail to Date', () => {
      const dateDetail = { year: 2023, month: 1, day: 1 }
      const result = toDate(dateDetail)
      assert.equal(result.getFullYear(), 2023)
      assert.equal(result.getMonth(), 0) // 0-based month
      assert.equal(result.getDate(), 1)
    })

    it('should convert string to Date', () => {
      const dateStr = '2023-01-01'
      const result = toDate(dateStr)
      assert.equal(result.getFullYear(), 2023)
      assert.equal(result.getMonth(), 0)
      assert.equal(result.getDate(), 1)
    })

    it('should return Date object as is', () => {
      const date = new Date('2023-01-01')
      const result = toDate(date)
      assert.equal(result, date)
    })

    it('should use server date as fallback', () => {
      const serverDate = new Date('2023-01-01')
      const result = toDate(undefined, serverDate)
      assert.equal(result.toISOString(), serverDate.toISOString())
    })
  })

  describe('toDateDetail', () => {
    it('should convert Date to DateDetail', () => {
      const date = new Date('2023-01-01')
      const result = toDateDetail(date)
      assert.deepEqual(result, { year: 2023, month: 1, day: 1 })
    })

    it('should convert string to DateDetail', () => {
      const dateStr = '2023-01-01'
      const result = toDateDetail(dateStr)
      assert.deepEqual(result, { year: 2023, month: 1, day: 1 })
    })

    it('should return undefined for invalid date', () => {
      assert.equal(toDateDetail('invalid'), undefined)
      assert.equal(toDateDetail(null), undefined)
    })
  })

  describe('getDay', () => {
    it('should return day of week (1-7)', () => {
      // Monday
      const monday = new Date('2023-01-02')
      assert.equal(getDay(monday), 1)
      
      // Sunday
      const sunday = new Date('2023-01-01')
      assert.equal(getDay(sunday), 7)
    })

    it('should handle DateDetail input', () => {
      const dateDetail = { year: 2023, month: 1, day: 2 } // Monday
      assert.equal(getDay(dateDetail), 1)
    })
  })

  describe('getNextDateByWeekday', () => {
    it('should get next occurrence of weekday', () => {
      // If today is Monday (1) and we want next Thursday (4)
      const monday = new Date('2023-01-02')
      const result = getNextDateByWeekday(4, monday)
      assert.equal(result.getDate(), 5) // Thursday is 3 days later
    })

    it('should get next week if same weekday', () => {
      // If today is Monday (1) and we want next Monday (1)
      const monday = new Date('2023-01-02')
      const result = getNextDateByWeekday(1, monday)
      assert.equal(result.getDate(), 9) // Next Monday is 7 days later
    })
  })

  describe('getISODate', () => {
    it('should format date as ISO 8601 date', () => {
      const date = new Date('2023-01-01')
      assert.equal(getISODate(date), '2023-01-01')
    })

    it('should handle DateDetail input', () => {
      const dateDetail = { year: 2023, month: 1, day: 1 }
      assert.equal(getISODate(dateDetail), '2023-01-01')
    })

    it('should pad month and day with leading zeros', () => {
      const date = new Date('2023-01-01')
      assert.equal(getISODate(date), '2023-01-01')
    })
  })

  describe('lastDayOfMonth', () => {
    it('should return last day of month', () => {
      // January has 31 days
      const january = new Date('2023-01-15')
      assert.equal(lastDayOfMonth(january), 31)
      
      // February 2023 has 28 days
      const february = new Date('2023-02-15')
      assert.equal(lastDayOfMonth(february), 28)
      
      // February 2024 (leap year) has 29 days
      const februaryLeap = new Date('2024-02-15')
      assert.equal(lastDayOfMonth(februaryLeap), 29)
    })
  })

  describe('timeIsBetween', () => {
    it('should return true for time between start and end', () => {
      assert.equal(timeIsBetween('12:30', '10:00', '14:00'), true)
    })

    it('should return false for time outside range', () => {
      assert.equal(timeIsBetween('09:30', '10:00', '14:00'), false)
      assert.equal(timeIsBetween('14:30', '10:00', '14:00'), false)
    })

    it('should handle overnight ranges', () => {
      assert.equal(timeIsBetween('23:30', '22:00', '06:00'), true)
      assert.equal(timeIsBetween('01:30', '22:00', '06:00'), true)
      assert.equal(timeIsBetween('10:30', '22:00', '06:00'), false)
    })
  })
})
