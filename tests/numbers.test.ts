import assert from 'node:assert'
import { describe, it } from 'node:test'
import { round, growthRate, Numbers } from '../src/index'

describe('Numbers', () => {
	describe('round', () => {
		it('should round numbers to specified decimal places', async () => {
			const down1 = round(1.244, 2),
				down2 = round(1.2449, 2),
				same1 = round(9.99, 2),
				same2 = round(0.01, 2),
				up = round(1.245, 2)

			assert.equal(down1, 1.24)
			assert.equal(down2, 1.24)
			assert.equal(same1, 9.99)
			assert.equal(same2, 0.01)
			assert.equal(up, 1.25)
		})

		it('should round to integer by default', () => {
			assert.equal(round(1.4), 1)
			assert.equal(round(1.5), 2)
			assert.equal(round(1.9), 2)
			assert.equal(round(-1.5), -2)
		})

		it('should handle zero decimal places', () => {
			assert.equal(round(1234.5678, 0), 1235)
			// The implementation doesn't support negative decimal places
			// as Number.toFixed() only accepts values between 0 and 100
		})

		it('should handle very small and very large numbers', () => {
			assert.equal(round(0.0000001, 5), 0)
			assert.equal(round(0.000001, 5), 0)
			assert.equal(round(0.00001, 5), 0.00001)
			assert.equal(round(1000000.1, 0), 1000000)
		})
	})

	describe('growthRate', () => {
		it('should calculate positive growth rate', () => {
			assert.equal(growthRate(110, 100), 0.1)
			assert.equal(growthRate(150, 100), 0.5)
			assert.equal(growthRate(200, 100), 1)
		})

		it('should calculate negative growth rate', () => {
			assert.equal(growthRate(90, 100), -0.1)
			assert.equal(growthRate(50, 100), -0.5)
			assert.equal(growthRate(0, 100), -1)
		})

		it('should handle zero growth', () => {
			assert.equal(growthRate(100, 100), 0)
		})

		it('should return undefined when previous value is zero', () => {
			assert.equal(growthRate(100, 0), undefined)
		})

		it('should round to 3 decimal places', () => {
			assert.equal(growthRate(123.456, 100), 0.235)
			assert.equal(growthRate(100, 123.456), -0.19)
		})
	})

	describe('Numbers module', () => {
		it('should export round and growthRate functions', () => {
			assert.equal(typeof Numbers.round, 'function')
			assert.equal(typeof Numbers.growthRate, 'function')
		})
	})
})
