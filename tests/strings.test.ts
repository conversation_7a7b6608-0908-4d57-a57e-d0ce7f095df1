import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Strings, bm } from '../src/index'

const { mustache, hasUnicode, isChinese, similarity, parseUrl, isUrl, isIP } = Strings

const qualifierStr = '{"$and":[{"$or":[{"memberships.current":{"$not":{"$elemMatch":{"key":"{key}","tierLevel":1,"state":"active"}}}},{"memberships.current":{"$size":0}}]}]}'

describe('Strings', () => {
	describe('mustache', () => {
		it('should replace placeholders in simple text', async () => {
			const str = mustache('replace {name} here', { name: 'john' })
			assert.equal(str, 'replace john here')
		})

		it('should handle multiple placeholders', async () => {
			const str = mustache('Hello {firstName} {lastName}!', { firstName: 'John', lastName: 'Doe' })
			assert.equal(str, 'Hello John <PERSON>!')
		})

		it('should replace empty string for missing data', async () => {
			const str = mustache('Hello {name}!', {})
			assert.equal(str, 'Hello !')
		})

		it('should handle complex JSON strings', async () => {
			const key = 'abc123',
				str = mustache(qualifierStr, { key }),
				qualifier = JSON.parse(str)

			assert.equal(qualifier.$and[0].$or[0]['memberships.current'].$not.$elemMatch.key, key)
		})
	})

	describe('hasUnicode', () => {
		it('should detect Unicode characters', async () => {
			const unicode = hasUnicode('abc中文xxxx')
			assert.equal(unicode, true)
		})

		it('should return false for ASCII-only strings', async () => {
			const unicode = hasUnicode('abcxxxx')
			assert.equal(unicode, false)
		})

		it('should handle empty strings', async () => {
			const unicode = hasUnicode('')
			assert.equal(unicode, false)
		})
	})

	describe('isChinese', () => {
		it('should detect Chinese characters', async () => {
			const result = isChinese('你好世界')
			assert.equal(result, true)
		})

		it('should detect mixed Chinese and non-Chinese text', async () => {
			const result = isChinese('Hello 世界')
			assert.equal(result, true)
		})

		it('should return false for non-Chinese text', async () => {
			const result = isChinese('Hello world')
			assert.equal(result, false)
		})
	})

	describe('similarity', () => {
		it('should calculate similarity between strings', async () => {
			const result = similarity('hello', 'hallo')
			assert.ok(result > 0)
		})

		it('should return 1 for identical strings', async () => {
			const result = similarity('hello', 'hello')
			assert.equal(result, 1)
		})

		it('should handle different types', async () => {
			const result = similarity('123', '123')
			assert.ok(result > 0.9)
		})

		it('should handle boolean values', async () => {
			const result1 = similarity(true, '1')
			const result2 = similarity(false, '0')
			assert.ok(result1 > 0.9)
			assert.ok(result2 > 0.9)
		})

		it('should handle date objects', async () => {
			const date = new Date('2023-01-01')
			const dateStr = date.toISOString()
			const result = similarity(date, dateStr)
			assert.ok(result > 0.9)
		})
	})

	describe('parseUrl', () => {
		it('should parse a standard URL', async () => {
			const url = 'https://example.com/api/v1/users'
			const result = parseUrl(url)

			assert.equal(result?.protocol, 'https')
			assert.equal(result?.hostname, 'example.com')
			assert.equal(result?.apiRoot, '/api')
			assert.equal(result?.apiVersion, 1)
			assert.equal(result?.apiPath, '/users')
		})

		it('should parse URL with port', async () => {
			const url = 'http://localhost:3000/api/users'
			const result = parseUrl(url)

			assert.equal(result?.protocol, 'http')
			assert.equal(result?.hostname, 'localhost')
			assert.equal(result?.port, '3000')
			assert.equal(result?.apiRoot, '/api')
		})

		it('should return undefined for invalid URLs', async () => {
			const url = 'not-a-valid-url'
			const result = parseUrl(url)

			assert.equal(result, undefined)
		})
	})

	describe('isUrl', () => {
		it('should identify valid URLs', async () => {
			const result = isUrl('https://example.com')
			assert.equal(result, true)
		})

		it('should identify URLs with different protocols', async () => {
			const result = isUrl('ftp://example.com')
			assert.equal(result, true)
		})

		it('should return false for non-URL strings', async () => {
			const result = isUrl('example.com')
			assert.equal(result, false)
		})

		it('should handle empty strings', async () => {
			const result = isUrl('')
			assert.equal(result, false)
		})
	})

	describe('isIP', () => {
		it('should identify valid IPv4 addresses', async () => {
			const result = isIP('***********')
			assert.equal(result, true)
		})

		it('should identify valid IPv6 addresses', async () => {
			const result = isIP('2001:0db8:85a3:0000:0000:8a2e:0370:7334')
			assert.equal(result, true)
		})

		it('should identify unusual IPv6 notations', async () => {
			const result = isIP('::2001:e68:5470:3c2d:e0af:7fe3')
			assert.equal(result, true)
		})

		it('should reject invalid IP addresses', async () => {
			assert.equal(isIP('256.0.0.1'), false)
			assert.equal(isIP('192.168.1'), false)
			assert.equal(isIP('not-an-ip'), false)
			assert.equal(isIP(''), false)
		})
	})
})

describe('Dev', () => {
	it('should bm()', async () => {
		const start = bm.mark(),
			NOW = bm.now()

		console.log(`bm: now = ${NOW}, lapsed: ${bm.diff(start)}ms`)
	})
})
