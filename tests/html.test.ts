import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Html } from '../src/index'

const { imageReferences, substituteUrls } = Html

describe('Html', () => {
  describe('imageReferences', () => {
    it('should extract image references from HTML', () => {
      const html = '<div><img src="image.jpg" alt="Local Image"><img src="http://example.com/image.png" alt="External Image"></div>'
      const result = imageReferences(html)

      assert.equal(result.unique.length, 2)
      assert.equal(result.local.length, 1)
      assert.equal(result.external.length, 1)
      assert.equal(result.local[0].original, 'image.jpg')
      assert.equal(result.external[0].original, 'http://example.com/image.png')
    })

    it('should handle HTML with no images', () => {
      const html = '<div><p>No images here</p></div>'
      const result = imageReferences(html)

      assert.equal(result.unique.length, 0)
      assert.equal(result.local.length, 0)
      assert.equal(result.external.length, 0)
    })

    it('should handle duplicate image references', () => {
      const html = '<div><img src="image.jpg"><img src="image.jpg"></div>'
      const result = imageReferences(html)

      assert.equal(result.unique.length, 1)
      assert.equal(result.local.length, 1)
      assert.equal(result.local[0].original, 'image.jpg')
    })

    it('should handle case sensitivity in URLs', () => {
      const html = '<div><img src="Image.jpg"><img src="image.jpg"></div>'
      const result = imageReferences(html)

      // The function converts URLs to lowercase for comparison
      assert.equal(result.unique.length, 1)
      assert.equal(result.local.length, 1)
      // But preserves the original case in the result
      assert.equal(result.local[0].original, 'Image.jpg')
    })

    it('should handle empty HTML', () => {
      const html = ''
      const result = imageReferences(html)

      assert.equal(result.unique.length, 0)
      assert.equal(result.local.length, 0)
      assert.equal(result.external.length, 0)
    })
  })

  describe('substituteUrls', () => {
    it('should substitute URLs in HTML', () => {
      const html = '<div><img src="image.jpg"><img src="http://example.com/image.png"></div>'
      const substitutions = [
        { original: 'image.jpg', url: 'new-image.jpg' },
        { original: 'http://example.com/image.png', url: 'https://cdn.example.com/image.png' }
      ]

      const result = substituteUrls(html, substitutions)

      assert.equal(result.includes('new-image.jpg'), true)
      assert.equal(result.includes('https://cdn.example.com/image.png'), true)
      // The function uses regex with 'gi' flag, which means it will replace all occurrences
      // but it doesn't escape special regex characters, so we need to be careful with the assertions
      assert.ok(!result.includes('src="image.jpg"'))
      assert.ok(!result.includes('src="http://example.com/image.png"'))
    })

    it('should handle case-insensitive substitutions', () => {
      const html = '<div><img src="Image.jpg"></div>'
      const substitutions = [
        { original: 'image.jpg', url: 'new-image.jpg' }
      ]

      const result = substituteUrls(html, substitutions)

      assert.equal(result.includes('new-image.jpg'), true)
      assert.equal(result.includes('Image.jpg'), false)
    })

    it('should handle multiple occurrences of the same URL', () => {
      const html = '<div><img src="image.jpg"><img src="image.jpg"></div>'
      const substitutions = [
        { original: 'image.jpg', url: 'new-image.jpg' }
      ]

      const result = substituteUrls(html, substitutions)

      assert.equal(result, '<div><img src="new-image.jpg"><img src="new-image.jpg"></div>')
    })

    it('should handle empty substitution list', () => {
      const html = '<div><img src="image.jpg"></div>'
      const substitutions = []

      const result = substituteUrls(html, substitutions)

      assert.equal(result, html)
    })

    it('should handle empty HTML', () => {
      const html = ''
      const substitutions = [
        { original: 'image.jpg', url: 'new-image.jpg' }
      ]

      const result = substituteUrls(html, substitutions)

      assert.equal(result, '')
    })
  })
})
