import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Events } from '../src/index'

const { domainOf, match } = Events

describe('Events', () => {
  describe('domainOf', () => {
    it('should extract domain from event name', () => {
      const result = domainOf('user.created')
      assert.equal(result, 'user')
    })

    it('should extract domain from complex event name', () => {
      const result = domainOf('user.profile.updated')
      assert.equal(result, 'user')
    })

    it('should handle empty event name', () => {
      const result = domainOf()
      assert.equal(result, '')
    })

    it('should handle event name without domain', () => {
      const result = domainOf('created')
      assert.equal(result, 'created')
    })
  })

  describe('match', () => {
    it('should match exact strings', () => {
      const result = match('user.created', 'user.created')
      assert.equal(result, true)
    })

    it('should not match different strings', () => {
      const result = match('user.created', 'user.updated')
      assert.equal(result, false)
    })

    it('should match with wildcard at end', () => {
      const result = match('user.created', 'user.*')
      assert.equal(result, true)
    })

    it('should match with wildcard in middle', () => {
      const result = match('user.profile.updated', 'user.*.updated')
      assert.equal(result, true)
    })

    it('should match with wildcard at beginning of action', () => {
      const result = match('user.profile.updated', 'user.*updated')
      assert.equal(result, true)
    })

    it('should match with wildcard at end of action', () => {
      const result = match('user.profile.created', 'user.profile.*')
      assert.equal(result, true)
    })

    it('should not match if domain is different', () => {
      const result = match('user.created', 'account.*')
      assert.equal(result, false)
    })

    it('should not match if pattern is just a wildcard', () => {
      const result = match('user.created', '*')
      assert.equal(result, false)
    })

    it('should not match if pattern is just a domain', () => {
      const result = match('user.created', 'user')
      assert.equal(result, false)
    })

    it('should not match if pattern starts with wildcard', () => {
      const result = match('user.created', '*.created')
      assert.equal(result, false)
    })

    it('should not match if before part does not match', () => {
      const result = match('user.created', 'account.*')
      assert.equal(result, false)
    })

    it('should not match if after part does not match', () => {
      const result = match('user.created', 'user.*updated')
      assert.equal(result, false)
    })
  })
})
