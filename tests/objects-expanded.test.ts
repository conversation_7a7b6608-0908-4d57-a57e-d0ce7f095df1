import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Objects } from '../src/index'

const { 
  isEmptyObj, 
  cloneDeep, 
  removeEmptyValues, 
  pick, 
  pickObj, 
  pickDeep, 
  parseJSON, 
  getDescendantProp,
  flatten,
  unflatten
} = Objects

describe('Objects Expanded', () => {
  describe('isEmptyObj', () => {
    it('should return true for empty objects', () => {
      assert.equal(isEmptyObj({}), true)
    })

    it('should return true for null or undefined', () => {
      assert.equal(isEmptyObj(null), true)
      assert.equal(isEmptyObj(undefined), true)
    })

    it('should return false for non-empty objects', () => {
      assert.equal(isEmptyObj({ key: 'value' }), false)
    })

    it('should handle objects with toJSON method', () => {
      const obj = {
        toJSON: () => ({ key: 'value' })
      }
      assert.equal(isEmptyObj(obj), false)
    })

    it('should consider objects with undefined values as empty', () => {
      assert.equal(isEmptyObj({ key: undefined }), true)
    })
  })

  describe('cloneDeep', () => {
    it('should create a deep copy of an object', () => {
      const original = { a: 1, b: { c: 2 } }
      const clone = cloneDeep(original)
      
      assert.deepEqual(clone, original)
      assert.notEqual(clone, original)
      assert.notEqual(clone.b, original.b)
    })

    it('should handle arrays', () => {
      const original = [1, 2, [3, 4]]
      const clone = cloneDeep(original)
      
      assert.deepEqual(clone, original)
      assert.notEqual(clone, original)
      assert.notEqual(clone[2], original[2])
    })

    it('should handle nested objects and arrays', () => {
      const original = { a: [1, { b: 2 }], c: { d: [3] } }
      const clone = cloneDeep(original)
      
      assert.deepEqual(clone, original)
      assert.notEqual(clone, original)
      assert.notEqual(clone.a, original.a)
      assert.notEqual(clone.a[1], original.a[1])
      assert.notEqual(clone.c, original.c)
      assert.notEqual(clone.c.d, original.c.d)
    })
  })

  describe('removeEmptyValues', () => {
    it('should remove null and undefined values from an object', () => {
      const obj = { a: 1, b: null, c: undefined, d: 'value' }
      const result = removeEmptyValues(obj)
      
      assert.deepEqual(result, { a: 1, d: 'value' })
    })

    it('should keep falsy values that are not null or undefined', () => {
      const obj = { a: 0, b: '', c: false, d: null }
      const result = removeEmptyValues(obj)
      
      assert.deepEqual(result, { a: 0, b: '', c: false })
    })

    it('should handle empty objects', () => {
      const obj = {}
      const result = removeEmptyValues(obj)
      
      assert.deepEqual(result, {})
    })
  })

  describe('pick', () => {
    it('should pick properties from an object using a mapping', () => {
      const obj = { a: { b: { c: 'value' } }, d: 'another' }
      const mapping = { 'a.b.c': 'key' }
      const result = pick(mapping, obj)
      
      assert.deepEqual(result, { key: 'value' })
    })

    it('should handle multiple mappings', () => {
      const obj = { a: { b: { c: 'value1' } }, d: 'value2' }
      const mapping = { 'a.b.c': 'key1', 'd': 'key2' }
      const result = pick(mapping, obj)
      
      assert.deepEqual(result, { key1: 'value1', key2: 'value2' })
    })

    it('should handle reverse mapping', () => {
      const obj = { key1: 'value1', key2: 'value2' }
      const mapping = { 'a.b.c': 'key1', 'd': 'key2' }
      const result = pick(mapping, obj, true)
      
      assert.deepEqual(result, { a: { b: { c: 'value1' } }, d: 'value2' })
    })

    it('should handle const values', () => {
      const obj = { a: 1 }
      const mapping = { 'const.value': 'key' }
      const result = pick(mapping, obj)
      
      assert.deepEqual(result, { key: 'value' })
    })
  })

  describe('pickObj', () => {
    it('should pick shallow properties from an object', () => {
      const obj = { a: 1, b: 2, c: 3, d: 4 }
      const result = pickObj(obj, ['a', 'c'])
      
      assert.deepEqual(result, { a: 1, c: 3 })
    })

    it('should handle objects with toJSON method', () => {
      const obj = {
        toJSON: () => ({ a: 1, b: 2, c: 3 })
      }
      const result = pickObj(obj, ['a', 'c'])
      
      assert.deepEqual(result, { a: 1, c: 3 })
    })

    it('should handle empty properties array', () => {
      const obj = { a: 1, b: 2 }
      const result = pickObj(obj, [])
      
      assert.deepEqual(result, {})
    })

    it('should handle null or undefined objects', () => {
      assert.equal(pickObj(null, ['a']), null)
      assert.equal(pickObj(undefined, ['a']), undefined)
    })

    it('should ignore non-existent properties', () => {
      const obj = { a: 1, b: 2 }
      const result = pickObj(obj, ['a', 'c'])
      
      assert.deepEqual(result, { a: 1 })
    })
  })

  describe('pickDeep', () => {
    it('should pick deep properties from an object', () => {
      const obj = { a: { b: { c: 'value' } }, d: 'another' }
      const pickProps = { x: 'a.b.c', y: 'd' }
      const result = pickDeep(obj, pickProps)
      
      assert.deepEqual(result, { x: 'value', y: 'another' })
    })

    it('should handle string property', () => {
      const obj = { a: { b: { c: 'value' } } }
      const result = pickDeep(obj, 'a.b.c')
      
      assert.equal(result, 'value')
    })

    it('should handle flat mode', () => {
      const obj = { a: { b: { c: 'value' } }, d: 'another' }
      const pickProps = { 'x.y': 'a.b.c', 'z': 'd' }
      const result = pickDeep(obj, pickProps, true)
      
      assert.deepEqual(result, { 'x.y': 'value', 'z': 'another' })
    })
  })

  describe('parseJSON', () => {
    it('should parse valid JSON strings', () => {
      const json = '{"a":1,"b":"value"}'
      const result = parseJSON(json)
      
      assert.deepEqual(result, { a: 1, b: 'value' })
    })

    it('should return default value for invalid JSON', () => {
      const json = 'invalid json'
      const result = parseJSON(json, { default: true })
      
      assert.deepEqual(result, { default: true })
    })

    it('should return false as default value if not specified', () => {
      const json = 'invalid json'
      const result = parseJSON(json)
      
      assert.equal(result, false)
    })

    it('should handle name parameter for error logging', () => {
      const json = 'invalid json'
      const result = parseJSON(json, null, 'test')
      
      assert.equal(result, false)
    })
  })

  describe('getDescendantProp', () => {
    it('should get a nested property from an object', () => {
      const obj = { a: { b: { c: 'value' } } }
      const result = getDescendantProp(obj, 'a.b.c')
      
      assert.equal(result, 'value')
    })

    it('should handle non-existent properties', () => {
      const obj = { a: { b: {} } }
      const result = getDescendantProp(obj, 'a.b.c')
      
      assert.equal(result, undefined)
    })

    it('should handle empty path', () => {
      const obj = { a: 1 }
      const result = getDescendantProp(obj, '')
      
      assert.deepEqual(result, obj)
    })
  })

  describe('flatten/unflatten', () => {
    it('should flatten nested objects', () => {
      const obj = { a: { b: { c: 'value' } }, d: 'another' }
      const result = flatten(obj)
      
      assert.deepEqual(result, { 'a.b.c': 'value', 'd': 'another' })
    })

    it('should unflatten flattened objects', () => {
      const flattened = { 'a.b.c': 'value', 'd': 'another' }
      const result = unflatten(flattened)
      
      assert.deepEqual(result, { a: { b: { c: 'value' } }, d: 'another' })
    })

    it('should handle arrays in objects', () => {
      const obj = { a: [1, 2, { b: 'value' }] }
      const flattened = flatten(obj)
      const result = unflatten(flattened)
      
      assert.deepEqual(result, obj)
    })
  })
})
