import assert from 'node:assert'
import { describe, it } from 'node:test'
import { Scripts } from '../src/index'
import path from 'node:path'

const { loadPackage, loadModule, scriptPath } = Scripts

describe('Scripts', () => {
  describe('loadPackage', () => {
    it('should load an existing package', () => {
      const result = loadPackage('path')
      assert.equal(typeof result, 'object')
      assert.equal(typeof result.join, 'function')
    })

    it('should return an error for non-existent package', () => {
      const result = loadPackage('non-existent-package-name')
      assert.ok(result instanceof Error)
    })
  })

  describe('scriptPath', () => {
    it('should generate correct path with filename', () => {
      const result = scriptPath('test.js')
      const expectedPath = path.resolve(__dirname, '../src') + '/lib/providers/test.js'
      assert.equal(result, expectedPath)
    })

    it('should generate correct path without filename', () => {
      const result = scriptPath()
      const expectedPath = path.resolve(__dirname, '../src') + '/lib/providers'
      assert.equal(result, expectedPath)
    })

    it('should use custom path if provided', () => {
      const result = scriptPath('test.js', '/custom/path')
      const expectedPath = path.resolve(__dirname, '../src') + '/custom/path/test.js'
      assert.equal(result, expectedPath)
    })
  })

  describe('loadModule', () => {
    it('should return an error for non-existent module', () => {
      const result = loadModule('non-existent-module.js', '/custom/path')
      assert.ok(result instanceof Error)
    })
  })
})
