import assert from 'node:assert'
import { describe, it } from 'node:test'
import { CardNumbers } from '../src/index'

const { cardNumberFromBarcode, isValidCardNumber, cleanseCardNumber } = CardNumbers

describe('CardNumbers', () => {
  describe('cardNumberFromBarcode', () => {
    it('should extract card number from barcode using pattern', () => {
      const barcode = 'CARD|21|1234567890'
      const patterns = ['/(?!CARD\\|2[0-1]\\|)\\d{8,10}/g']
      const result = cardNumberFromBarcode(barcode, patterns)
      assert.equal(result, '1234567890')
    })

    it('should return original barcode if no match found', () => {
      const barcode = 'CARD|21|ABC123'
      const patterns = ['/(?!CARD\\|2[0-1]\\|)\\d{8,10}/g']
      const result = cardNumberFromBarcode(barcode, patterns)
      assert.equal(result, barcode)
    })

    it('should handle multiple patterns and use first match', () => {
      const barcode = 'PREFIX-12345-SUFFIX'
      const patterns = ['/\\d{3}/g', '/\\d{5}/g']
      const result = cardNumberFromBarcode(barcode, patterns)
      // The first pattern will match '123', not '12345'
      assert.equal(result, '123')
    })
  })

  describe('isValidCardNumber', () => {
    it('should validate card number format using pattern', () => {
      const cardNumber = '1234567890'
      const patterns = ['/\\d{10}/g']
      const result = isValidCardNumber(cardNumber, patterns)
      assert.equal(result, true)
    })

    it('should return false for invalid card number format', () => {
      const cardNumber = 'ABC123'
      const patterns = ['/\\d{10}/g']
      const result = isValidCardNumber(cardNumber, patterns)
      assert.equal(result, false)
    })

    it('should handle multiple patterns and validate if any match', () => {
      const cardNumber = '12345'
      const patterns = ['/\\d{10}/g', '/\\d{5}/g']
      const result = isValidCardNumber(cardNumber, patterns)
      assert.equal(result, true)
    })
  })

  describe('cleanseCardNumber', () => {
    it('should remove spaces and symbols from card number', () => {
      const cardNumber = '1234-5678-90'
      const result = cleanseCardNumber(cardNumber)
      assert.equal(result, '1234567890')
    })

    it('should handle card number with spaces', () => {
      const cardNumber = '1234 5678 90'
      const result = cleanseCardNumber(cardNumber)
      assert.equal(result, '1234567890')
    })

    it('should handle card number with mixed symbols', () => {
      const cardNumber = '1234.5678-90 (ABC)'
      const result = cleanseCardNumber(cardNumber)
      // The function only keeps alphanumeric characters
      assert.equal(result, '1234567890ABC')
    })

    it('should return non-string input as is', () => {
      const cardNumber = null as any
      const result = cleanseCardNumber(cardNumber)
      assert.equal(result, null)
    })
  })
})
