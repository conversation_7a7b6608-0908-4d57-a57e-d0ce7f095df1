# Package Development Guidelines for Perkd Ecosystem

This document provides a comprehensive guideline for creating new packages compatible with the Perkd ecosystem, referencing other repositories (e.g., via GitHub MCP) and ensuring seamless integration with existing services and packages.

---

## 1. Directory Structure

```
package-root/
  README.md
  package.json
  tsconfig.json
  .c8rc.json
  src/
    index.ts
    ... (feature modules)
  tests/
    ... (test files)
  scripts/
    ... (utility scripts)
  doc/
    ... (documentation, references)
```

---

## 2. Configuration Files

### `package.json`
- Use a scoped name (e.g., `@perkd/package-name`).
- Reference internal packages using GitHub URLs with `semver` tags for compatibility:
  ```json
  "dependencies": {
    "@perkd/format-datetime": "github:perkd/format-datetime#semver:^1.3.3"
  }
  ```
- Specify `engines.node` (e.g., `>=20`).
- Define scripts for build, test, coverage, and release:
  ```json
  "scripts": {
    "build": "npx tsc",
    "test": "tsc && node --test --import tsx tests/**/*.test.ts",
    "coverage": "c8 yarn test",
    ...
  }
  ```
- Use `yarn` as the package manager (v4+ recommended).

### `tsconfig.json`
- Set `rootDir` to `src` and `outDir` to `dist`.
- Enable `strict` mode and `esModuleInterop`.
- Target modern Node.js (e.g., `es2022`).
- Include source files and exclude `node_modules`, `tests`, and type definitions.

### `.c8rc.json`
- Configure code coverage to include `src/**/*.ts` and exclude build, test, and type files.
- Use `text`, `lcov`, and `html` reporters.

---

## 3. Source Code Organization
- Place all implementation files in `src/`.
- Export all public APIs from `src/index.ts`.
- Organize features by domain (e.g., `src/time.ts`, `src/objects.ts`).

---

## 4. Testing
- Place all test files in `tests/`.
- Use `.test.ts` suffix for test files.
- Ensure high coverage (target >90%).
- Use `c8` for coverage reporting.

---

## 5. Documentation
- Provide a clear `README.md` with installation, usage, and development instructions.
- Document all public APIs with examples.
- Maintain additional docs in `doc/` as needed.

---

## 6. Coding Standards
- Use TypeScript with strict type checking.
- Follow a shared ESLint config (e.g., `@perkd/eslint-config`).
- Use Prettier for formatting if desired.

---

## 7. Dependency Management
- Reference other Perkd packages via GitHub with semver tags for compatibility.
- Keep dependencies up to date using tools like `ncu` (npm-check-updates).
- Separate `dependencies` and `devDependencies` clearly.

---

## 8. Build & Release
- Build output goes to `dist/`.
- Do not commit build artifacts.
- Use scripts for clean builds and releases.
- Optionally, use GitHub Actions for CI/CD.

---

## 9. Compatibility & Integration
- Ensure all packages use compatible Node.js and TypeScript versions.
- Export types for all public APIs.
- Avoid breaking changes; use semver for versioning.
- Test integration with other packages before release.

---

## 10. Referencing Other Repositories (MCP/GitHub)
- Use the following format in `package.json` for cross-repo dependencies:
  ```json
  "@perkd/other-package": "github:perkd/other-package#semver:^x.y.z"
  ```
- Ensure referenced packages follow these same guidelines.

---

## 11. Example: Minimal `package.json`
```json
{
  "name": "@perkd/example",
  "version": "1.0.0",
  "private": true,
  "engines": { "node": ">=20" },
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "npx tsc",
    "test": "tsc && node --test --import tsx tests/**/*.test.ts",
    "coverage": "c8 yarn test"
  },
  "dependencies": {
    "@perkd/utils": "github:perkd/utils#semver:^2.0.0"
  },
  "devDependencies": {
    "@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.3",
    "c8": "^10.1.3",
    "tsx": "^4.19.4",
    "typescript": "^5.0.0"
  },
  "packageManager": "yarn@4.9.1"
}
```

---

## 12. Checklist for New Packages
- [ ] Follows directory structure
- [ ] Has all required config files
- [ ] Uses correct dependency references
- [ ] Passes all tests with high coverage
- [ ] Documents all public APIs
- [ ] Compatible with Node.js >=20 and latest TypeScript
- [ ] Integrates with other Perkd packages

---

For questions or updates, refer to the main [utils](https://github.com/perkd/utils) repository as a template.
