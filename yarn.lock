# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10

"@bcoe/v8-coverage@npm:^1.0.1":
  version: 1.0.2
  resolution: "@bcoe/v8-coverage@npm:1.0.2"
  checksum: 10/46600b2dde460269b07a8e4f12b72e418eae1337b85c979f43af3336c9a1c65b04e42508ab6b245f1e0e3c64328e1c38d8cd733e4a7cebc4fbf9cf65c6e59937
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/aix-ppc64@npm:0.25.4"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/android-arm64@npm:0.25.4"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/android-arm@npm:0.25.4"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/android-x64@npm:0.25.4"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/darwin-arm64@npm:0.25.4"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/darwin-x64@npm:0.25.4"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/freebsd-arm64@npm:0.25.4"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/freebsd-x64@npm:0.25.4"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/linux-arm64@npm:0.25.4"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/linux-arm@npm:0.25.4"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/linux-ia32@npm:0.25.4"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/linux-loong64@npm:0.25.4"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/linux-mips64el@npm:0.25.4"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/linux-ppc64@npm:0.25.4"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/linux-riscv64@npm:0.25.4"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/linux-s390x@npm:0.25.4"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/linux-x64@npm:0.25.4"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/netbsd-arm64@npm:0.25.4"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/netbsd-x64@npm:0.25.4"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/openbsd-arm64@npm:0.25.4"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/openbsd-x64@npm:0.25.4"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/sunos-x64@npm:0.25.4"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/win32-arm64@npm:0.25.4"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/win32-ia32@npm:0.25.4"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.25.4":
  version: 0.25.4
  resolution: "@esbuild/win32-x64@npm:0.25.4"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.5.1, @eslint-community/eslint-utils@npm:^4.7.0":
  version: 4.7.0
  resolution: "@eslint-community/eslint-utils@npm:4.7.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10/43ed5d391526d9f5bbe452aef336389a473026fca92057cf97c576db11401ce9bcf8ef0bf72625bbaf6207ed8ba6bf0dcf4d7e809c24f08faa68a28533c491a7
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10/c08f1dd7dd18fbb60bdd0d85820656d1374dd898af9be7f82cb00451313402a22d5e30569c150315b4385907cdbca78c22389b2a72ab78883b3173be317620cc
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.20.0":
  version: 0.20.0
  resolution: "@eslint/config-array@npm:0.20.0"
  dependencies:
    "@eslint/object-schema": "npm:^2.1.6"
    debug: "npm:^4.3.1"
    minimatch: "npm:^3.1.2"
  checksum: 10/9db7f6cbb5363f2f98ee4805ce09d1a95c4349e86f3f456f2c23a0849b7a6aa8d2be4c25e376ee182af062762e15a101844881c89b566eea0856c481ffcb2090
  languageName: node
  linkType: hard

"@eslint/config-helpers@npm:^0.2.1":
  version: 0.2.2
  resolution: "@eslint/config-helpers@npm:0.2.2"
  checksum: 10/55dbb0b8d63c4cb28fa2a5fd5f16c785f6bd87eb0f50d2f42ec3f7d06b5c6201e2e170846a4360ca00105578b034fba132ed54e4ee3215be240c4a43e7839189
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.14.0":
  version: 0.14.0
  resolution: "@eslint/core@npm:0.14.0"
  dependencies:
    "@types/json-schema": "npm:^7.0.15"
  checksum: 10/d9b060cf97468150675ddf4fb3db55edaa32467e0adf9f80919a5bfd15d0835ad7765456f4397ec2d16b0a1bb702af63f6d4712f94194d34fea118231ae1e2db
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.3.1":
  version: 3.3.1
  resolution: "@eslint/eslintrc@npm:3.3.1"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^10.0.1"
    globals: "npm:^14.0.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10/cc240addbab3c5fceaa65b2c8d5d4fd77ddbbf472c2f74f0270b9d33263dc9116840b6099c46b64c9680301146250439b044ed79278a1bcc557da412a4e3c1bb
  languageName: node
  linkType: hard

"@eslint/js@npm:9.27.0, @eslint/js@npm:^9.27.0":
  version: 9.27.0
  resolution: "@eslint/js@npm:9.27.0"
  checksum: 10/cdbe380fd31bb325b9ec65ae310fb92a57efb796d3cc5d8bc9dafef447d634c64e497bedade6a49e0cf44a5b14560ab6ac9ed9da5a38e2415b31ae01ae5b758e
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.6":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: 10/266085c8d3fa6cd99457fb6350dffb8ee39db9c6baf28dc2b86576657373c92a568aec4bae7d142978e798b74c271696672e103202d47a0c148da39154351ed6
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.3.1":
  version: 0.3.1
  resolution: "@eslint/plugin-kit@npm:0.3.1"
  dependencies:
    "@eslint/core": "npm:^0.14.0"
    levn: "npm:^0.4.1"
  checksum: 10/ab0c4cecadc6c38c7ae5f71b9831d3521d08237444d8f327751d1133a4369ccd42093a1c06b26fd6c311015807a27d95a0184a761d1cdd264b090896dcf0addb
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 10/270d936be483ab5921702623bc74ce394bf12abbf57d9145a69e8a0d1c87eb1c768bd2d93af16c5705041e257e6d9cc7529311f63a1349f3678abc776fc28523
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": "npm:^0.19.1"
    "@humanwhocodes/retry": "npm:^0.3.0"
  checksum: 10/6d43c6727463772d05610aa05c83dab2bfbe78291022ee7a92cb50999910b8c720c76cc312822e2dea2b497aa1b3fef5fe9f68803fc45c9d4ed105874a65e339
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10/e993950e346331e5a32eefb27948ecdee2a2c4ab3f072b8f566cd213ef485dd50a3ca497050608db91006f5479e43f91a439aef68d2a313bd3ded06909c7c5b3
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 10/eb457f699529de7f07649679ec9e0353055eebe443c2efe71c6dd950258892475a038e13c6a8c5e13ed1fb538cdd0a8794faa96b24b6ffc4c87fb1fc9f70ad7f
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.2":
  version: 0.4.3
  resolution: "@humanwhocodes/retry@npm:0.4.3"
  checksum: 10/0b32cfd362bea7a30fbf80bb38dcaf77fee9c2cae477ee80b460871d03590110ac9c77d654f04ec5beaf71b6f6a89851bdf6c1e34ccdf2f686bd86fcd97d9e61
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10/e9ed5fd27c3aec1095e3a16e0c0cf148d1fee55a38665c35f7b3f86a9b5d00d042ddaabc98e8a1cb7463b9378c15f22a94eb35e99469c201453eb8375191f243
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10/4412e9e6713c89c1e66d80bb0bb5a2a93192f10477623a27d08f228ba0316bb880affabc5bfe7f838f58a34d26c2c190da726e576cdfc18c49a72e89adabdcf5
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2, @istanbuljs/schema@npm:^0.1.3":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 10/a9b1e49acdf5efc2f5b2359f2df7f90c5c725f2656f16099e8b2cd3a000619ecca9fc48cf693ba789cf0fd989f6e0df6a22bc05574be4223ecdbb7997d04384b
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10/97106439d750a409c22c8bff822d648f6a71f3aa9bc8e5129efdc36343cd3096ddc4eeb1c62d2fe48e9bdd4db37b05d4646a17114ecebd3bbcacfa2de51c3c1d
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10/4ed6123217569a1484419ac53f6ea0d9f3b57e5b57ab30d7c267bdb27792a27eb0e4b08e84a2680aa55cc2f2b411ffd6ec3db01c44fdc6dc43aca4b55f8374fd
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.12":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10/dced32160a44b49d531b80a4a2159dceab6b3ddf0c8e95a0deae4b0e894b172defa63d5ac52a19c2068e1fe7d31ea4ba931fbeec103233ecb4208953967120fc
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10/6ab2a9b8a1d67b067922c36f259e3b3dfd6b97b219c540877a4944549a4d49ea5ceba5663905ab5289682f1f3c15ff441d02f0447f620a42e1cb5e1937174d4b
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10/012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10/40033e33e96e97d77fba5a238e4bba4487b8284678906a9f616b5579ddaf868a18874c0054a75402c9fbaaa033a25ceae093af58c9c30278e35c23c9479e79b0
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10/775c9a7eb1f88c195dfb3bce70c31d0fe2a12b28b754e25c08a3edb4bc4816bfedb7ac64ef1e730579d078ca19dacf11630e99f8f3c3e0fd7b23caa5fd6d30a6
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10/405c4490e1ff11cf299775449a3c254a366a4b1ffc79d87159b0ee7d5558ac9f6a2f8c0735fd6ff3873cef014cb1a44a5f9127cb6a1b2dbc408718cca9365b5a
  languageName: node
  linkType: hard

"@perkd/eslint-config@github:Perkd-X/eslint-config#semver:^3.1.3":
  version: 3.1.3
  resolution: "@perkd/eslint-config@https://github.com/Perkd-X/eslint-config.git#commit=864553f9b5517707787a1e0e5813ee1dc76060f0"
  checksum: 10/0ce64ec413bb493ec9482012369c7dc9e9509cb1360aba08d21fb2cca2c2ea1b86f56882bf5c0f21e18a74563a4001e5f2ef50a31226ac629f867a0928f7d7d5
  languageName: node
  linkType: hard

"@perkd/format-datetime@github:perkd/format-datetime#semver:^1.3.3":
  version: 1.3.3
  resolution: "@perkd/format-datetime@https://github.com/perkd/format-datetime.git#commit=09fc7564a4ed19f213d7e9cc92c4b78ae40699fd"
  dependencies:
    dayjs: "npm:^1.11.13"
  checksum: 10/d74bf673d52436b9d5c8145f5089e5356dbb1b7ac8f1d862672376aa27d1f3399d1f5080005971f1e655ccb2180748c38c89cba7d40a52471e643bf464574b97
  languageName: node
  linkType: hard

"@perkd/utils@workspace:.":
  version: 0.0.0-use.local
  resolution: "@perkd/utils@workspace:."
  dependencies:
    "@eslint/js": "npm:^9.27.0"
    "@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.3"
    "@perkd/format-datetime": "github:perkd/format-datetime#semver:^1.3.3"
    "@stylistic/eslint-plugin-ts": "npm:^4.4.0"
    "@types/busboy": "npm:^1"
    "@types/country-data": "npm:^0.0.5"
    "@types/eslint": "npm:^9.6.1"
    "@types/flat": "npm:5.0.5"
    "@types/franc": "npm:5.0.3"
    "@types/get-value": "npm:^3.0.5"
    "@types/jws": "npm:^3.2.10"
    "@types/locale": "npm:^0.1.4"
    "@types/node": "npm:^22.15.21"
    "@types/set-value": "npm:^4"
    "@types/similarity": "npm:^1.2.3"
    "@types/traverse": "npm:^0.6.37"
    "@types/validator": "npm:^13.15.1"
    bson-objectid: "npm:^2.0.4"
    c8: "npm:^10.1.3"
    camelcase: "npm:6.3.0"
    country-data: "npm:^0.0.31"
    currency-symbol-map: "npm:^5.1.0"
    deepmerge: "npm:^4.3.1"
    email-addresses: "npm:^5.0.0"
    eslint: "npm:^9.27.0"
    eslint-plugin-jsonc: "npm:^2.20.1"
    flat: "npm:5.0.2"
    franc: "npm:5.0.0"
    get-value: "npm:^4.0.1"
    htmlparser2: "npm:^10.0.0"
    jws: "npm:^4.0.0"
    libphonenumber-js: "npm:^1.12.8"
    limiter: "npm:^3.0.0"
    locale: "npm:^0.1.0"
    nanoid: "npm:3.3.8"
    pinyin-pro: "npm:^3.26.0"
    referer-parser: "npm:^0.0.3"
    rfdc: "npm:^1.4.1"
    set-value: "npm:^4.1.0"
    sift: "npm:^17.1.3"
    similarity: "npm:^1.2.1"
    traverse: "npm:^0.6.11"
    tslib: "npm:^2.8.1"
    tsx: "npm:^4.19.4"
    typescript-eslint: "npm:^8.32.1"
    validator: "npm:^13.15.0"
  languageName: unknown
  linkType: soft

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10/115e8ceeec6bc69dff2048b35c0ab4f8bbee12d8bb6c1f4af758604586d802b6e669dcb02dda61d078de42c2b4ddce41b3d9e726d7daa6b4b850f4adbf7333ff
  languageName: node
  linkType: hard

"@pkgr/core@npm:^0.2.4":
  version: 0.2.4
  resolution: "@pkgr/core@npm:0.2.4"
  checksum: 10/8544f0346c3f7035b9e2fdf60179c68b12d3c76b3fba9533844099af67cf5c0ce5257538f5faa05953d48cc1536d046f003231f321b2f75b3fb449db8410a2b7
  languageName: node
  linkType: hard

"@stylistic/eslint-plugin-ts@npm:^4.4.0":
  version: 4.4.0
  resolution: "@stylistic/eslint-plugin-ts@npm:4.4.0"
  dependencies:
    "@typescript-eslint/utils": "npm:^8.32.1"
    eslint-visitor-keys: "npm:^4.2.0"
    espree: "npm:^10.3.0"
  peerDependencies:
    eslint: ">=9.0.0"
  checksum: 10/2b330aa2c076f3c56bf6211e6a7db7030151c8fb2fcc95993db1443e89453a616ffdfe00b67786883538579f4c8565c73c779518189ef8fff40b233eb86f1b22
  languageName: node
  linkType: hard

"@types/busboy@npm:^1":
  version: 1.5.4
  resolution: "@types/busboy@npm:1.5.4"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/43cdd26754603fbee81f538ac52769f2cc8445d5f238666845d99a9fee22e0b608a075d0c346f78c43ade4ce4ec04433a51a1ffa21524ca29ead9d2375f4ec9c
  languageName: node
  linkType: hard

"@types/country-data@npm:^0.0.5":
  version: 0.0.5
  resolution: "@types/country-data@npm:0.0.5"
  checksum: 10/6fb31dffc21882e81823e9fadb1e45e34d7ddbea132dce20999417612f22eef3dcfb27c70fbe0135e76f8d4e98272d258e8b75a3aa5b1c849ecfe1ccc5715680
  languageName: node
  linkType: hard

"@types/eslint@npm:^9.6.1":
  version: 9.6.1
  resolution: "@types/eslint@npm:9.6.1"
  dependencies:
    "@types/estree": "npm:*"
    "@types/json-schema": "npm:*"
  checksum: 10/719fcd255760168a43d0e306ef87548e1e15bffe361d5f4022b0f266575637acc0ecb85604ac97879ee8ae83c6a6d0613b0ed31d0209ddf22a0fe6d608fc56fe
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^1.0.6":
  version: 1.0.7
  resolution: "@types/estree@npm:1.0.7"
  checksum: 10/419c845ece767ad4b21171e6e5b63dabb2eb46b9c0d97361edcd9cabbf6a95fcadb91d89b5fa098d1336fa0b8fceaea82fca97a2ef3971f5c86e53031e157b21
  languageName: node
  linkType: hard

"@types/flat@npm:5.0.5":
  version: 5.0.5
  resolution: "@types/flat@npm:5.0.5"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"@types/franc@npm:5.0.3":
  version: 5.0.3
  resolution: "@types/franc@npm:5.0.3"
  checksum: 10/aa1d9e76d8e5b9a49766f1074f722cd19495015ad312bad91fded052020df5dc5efe5738005e457a7b0b3281ae532c647719728363231eab077c66ffb3e9fcca
  languageName: node
  linkType: hard

"@types/get-value@npm:^3.0.5":
  version: 3.0.5
  resolution: "@types/get-value@npm:3.0.5"
  checksum: 10/dd77252d3b31662cb76af745ababe07c113e8fc34d32fa9f4701fae802f5cecc8236760a59c3c5c6405ca14d11695cd3fb7f62227163e5d52a67e7ab46bb699a
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:^2.0.1":
  version: 2.0.6
  resolution: "@types/istanbul-lib-coverage@npm:2.0.6"
  checksum: 10/3feac423fd3e5449485afac999dcfcb3d44a37c830af898b689fadc65d26526460bedb889db278e0d4d815a670331796494d073a10ee6e3a6526301fe7415778
  languageName: node
  linkType: hard

"@types/json-schema@npm:*, @types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10/1a3c3e06236e4c4aab89499c428d585527ce50c24fe8259e8b3926d3df4cfbbbcf306cfc73ddfb66cbafc973116efd15967020b0f738f63e09e64c7d260519e7
  languageName: node
  linkType: hard

"@types/jws@npm:^3.2.10":
  version: 3.2.10
  resolution: "@types/jws@npm:3.2.10"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10/144a6f990ecc53fe6c4bae52a45fdea259d25ae425fd2bec6c82c321c8762b470819c08649e615c4ef2943b4de2fd4c5c1e7f57e8b07ced862f91a376e7d1949
  languageName: node
  linkType: hard

"@types/locale@npm:^0.1.4":
  version: 0.1.4
  resolution: "@types/locale@npm:0.1.4"
  checksum: 10/d7812564067d2d8626dcdab256a078fa79d9b02d8a86713d05bec1578fe8e0601f0b70065d17be5463475fbf84b12c407be4fd0bb622b4455320b0add26746ba
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:^22.15.21":
  version: 22.15.21
  resolution: "@types/node@npm:22.15.21"
  dependencies:
    undici-types: "npm:~6.21.0"
  checksum: 10/cb4189587cca445bfb8166c0ed39f9344d743f37f3da892f2999a99bbabda45dc773237e61ecb7d1dc83dd95718cb1b5715b0be5dd7953565b19019e36a7cf39
  languageName: node
  linkType: hard

"@types/set-value@npm:^4":
  version: 4.0.3
  resolution: "@types/set-value@npm:4.0.3"
  checksum: 10/fe0dc8633ffcb16815683b4239a101234665c29d675164af3b4ce9887af1ed748d40cf12fd159475b27077f024301ba9cd642445780a5105b7526814a1c1fd6c
  languageName: node
  linkType: hard

"@types/similarity@npm:^1.2.3":
  version: 1.2.3
  resolution: "@types/similarity@npm:1.2.3"
  checksum: 10/1f3c9ad6e803e3c1d161c6701da09686a86a8772703765b259d3614ab1b0a84294b0b9f9044d596f7310cf583cc8614e1b1699f3e22175ba4af01fd3b295804e
  languageName: node
  linkType: hard

"@types/traverse@npm:^0.6.37":
  version: 0.6.37
  resolution: "@types/traverse@npm:0.6.37"
  checksum: 10/f66e812a22d4ee2e10d936400cefba581d90a509bc5278699ed9bbdc18bd9ba7c926883d3a55d4c97f4b5a021b55bce5bd1d5eec8f2b5203419f1584ce60b126
  languageName: node
  linkType: hard

"@types/validator@npm:^13.15.1":
  version: 13.15.1
  resolution: "@types/validator@npm:13.15.1"
  checksum: 10/fcd6180ee18e8323e5b57cca391dace7f524a4136065e65bb9694a0b3f12a52271cbcdb282d697f092a53f834f1ed2879628443fb53b0a531a271b44da8d056a
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:8.32.1":
  version: 8.32.1
  resolution: "@typescript-eslint/eslint-plugin@npm:8.32.1"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.10.0"
    "@typescript-eslint/scope-manager": "npm:8.32.1"
    "@typescript-eslint/type-utils": "npm:8.32.1"
    "@typescript-eslint/utils": "npm:8.32.1"
    "@typescript-eslint/visitor-keys": "npm:8.32.1"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^7.0.0"
    natural-compare: "npm:^1.4.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    "@typescript-eslint/parser": ^8.0.0 || ^8.0.0-alpha.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10/442205dd4e9fe016cf4f3edf292f5dba696c9e7d6c32c785a8bff0833974149513feab6c30d21e2f3c509bd2b2833edfb3175c0ee220661a02da59fd79100bb4
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:8.32.1":
  version: 8.32.1
  resolution: "@typescript-eslint/parser@npm:8.32.1"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:8.32.1"
    "@typescript-eslint/types": "npm:8.32.1"
    "@typescript-eslint/typescript-estree": "npm:8.32.1"
    "@typescript-eslint/visitor-keys": "npm:8.32.1"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10/3c2ab90fec0aaaa57f883bf3963030e74402de82a73b8e47a19109accf18ee1441878bcba73bb9584890eedb56215000d6652196d9bfde05272c043d1c9c529d
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.32.1":
  version: 8.32.1
  resolution: "@typescript-eslint/scope-manager@npm:8.32.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.32.1"
    "@typescript-eslint/visitor-keys": "npm:8.32.1"
  checksum: 10/f81f71bd88e6bed90c9a42ed3cd26a360f8f7ca53186cea33b872194bf724a4635dd3aead5002b18de6cc5d1df9840445af1f7fc4d117f452705ccebdb6c0b0f
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.32.1":
  version: 8.32.1
  resolution: "@typescript-eslint/type-utils@npm:8.32.1"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:8.32.1"
    "@typescript-eslint/utils": "npm:8.32.1"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10/e50a6f2a16ccd916ed32a4d0b6c1011b8c177d1f789dfa629942efbd6cd3b1f5b9dc42c67b64e8c377338b34906e0191b9165caa4302d92979f13cd06d1b4234
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.32.1":
  version: 8.32.1
  resolution: "@typescript-eslint/types@npm:8.32.1"
  checksum: 10/3a310e4bafa8dd6ddc83cd8627048ebe54660982348531bbbeee3d0fb06ffdacce6ac14200159166774bd8797664686f5d0bf19d3dd33216daccbc0e48cd3e51
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.32.1":
  version: 8.32.1
  resolution: "@typescript-eslint/typescript-estree@npm:8.32.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.32.1"
    "@typescript-eslint/visitor-keys": "npm:8.32.1"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.3.2"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10/8b956ce05bf64d412e33b7d564db9d5620a7c2600ab04f2c6bb7561bcf46593f14b77ca9c895a4480869a323565a140985a4f4760f9df58f71114c4f502b3c78
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.32.1, @typescript-eslint/utils@npm:^8.32.1":
  version: 8.32.1
  resolution: "@typescript-eslint/utils@npm:8.32.1"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.7.0"
    "@typescript-eslint/scope-manager": "npm:8.32.1"
    "@typescript-eslint/types": "npm:8.32.1"
    "@typescript-eslint/typescript-estree": "npm:8.32.1"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10/9383cea185f3b54124a639f806d627f686637460e2eea431ccb9eeb6452dcd5a893856e051eb5925510f8aeb9317f8b7d23d9f39ca8fa80c46f2b797dae77d9c
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.32.1":
  version: 8.32.1
  resolution: "@typescript-eslint/visitor-keys@npm:8.32.1"
  dependencies:
    "@typescript-eslint/types": "npm:8.32.1"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10/a1cbfbdac89d443dfc2718673e2cc1e884fc942678b3d1c3149cdab8123c71685473e362d794fcee1e975ceb45d16f44025a3f0bebe9b09a6bf8679f060d6817
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10/ebd2c149dda6f543b66ce3779ea612151bb3aa9d0824f169773ee9876f1ca5a4e0adbcccc7eed048c04da7998e1825e2aa76fcca92d9e67dea50ac2b0a58dc2e
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10/d4371eaef7995530b5b5ca4183ff6f062ca17901a6d3f673c9ac011b01ede37e7a1f7f61f8f5cfe709e88054757bb8f3277dc4061087cdf4f2a1f90ccbcdb977
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0, acorn@npm:^8.5.0, acorn@npm:^8.9.0":
  version: 8.14.1
  resolution: "acorn@npm:8.14.1"
  bin:
    acorn: bin/acorn
  checksum: 10/d1379bbee224e8d44c3c3946e6ba6973e999fbdd4e22e41c3455d7f9b6f72f7ce18d3dc218002e1e48eea789539cf1cb6d1430c81838c6744799c712fb557d92
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10/3db6d8d4651f2aa1a9e4af35b96ab11a7607af57a24f3bc721a387eaa3b5f674e901f0a648b0caefd48f3fd117c7761b79a3b55854e2aebaa96c3f32cf76af84
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10/48d6ad21138d12eb4d16d878d630079a2bda25a04e745c07846a4ad768319533031e28872a9b3c5790fa1ec41aabdf2abed30a56e5a03ebc2cf92184b8ee306c
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10/2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10/495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10/b4494dfbfc7e4591b4711a396bd27e540f8153914123dccb4cdbbcb514015ada63a3809f362b9d8d4f6b17a706f1d7bea3c6f974b15fa5ae76b5b502070889ff
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10/70fdf883b704d17a5dfc9cde206e698c16bcd74e7f196ab821511651aee4f9f76c9514bdfa6ca3a27b5e49138b89cb222a28caf3afe4567570139577f991df32
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10/18640244e641a417ec75a9bd38b0b2b6b95af5199aa241b131d4b2fb206f334d7ecc600bd194861610a5579084978bfcbb02baa399dbe442d56d0ae5e60dbaef
  languageName: node
  linkType: hard

"argparse@npm:~ 0.1.11":
  version: 0.1.16
  resolution: "argparse@npm:0.1.16"
  dependencies:
    underscore: "npm:~1.7.0"
    underscore.string: "npm:~2.4.0"
  checksum: 10/4984dd143a5aa465a469005c1ca62a3658067ac6cf77dc374258a1d167e1bc35cfa2d84dbc1a1e864af3c54fdada91bcb9bb4dc4e4fad1054dfb0bb98a78daa1
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    is-array-buffer: "npm:^3.0.5"
  checksum: 10/0ae3786195c3211b423e5be8dd93357870e6fb66357d81da968c2c39ef43583ef6eece1f9cb1caccdae4806739c65dea832b44b8593414313cd76a89795fca63
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.1"
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    is-array-buffer: "npm:^3.0.4"
  checksum: 10/4821ebdfe7d699f910c7f09bc9fa996f09b96b80bccb4f5dd4b59deae582f6ad6e505ecef6376f8beac1eda06df2dbc89b70e82835d104d6fcabd33c1aed1ae9
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 10/1a09379937d846f0ce7614e75071c12826945d4e417db634156bf0e4673c495989302f52186dfa9767a1d9181794554717badd193ca2bbab046ef1da741d8efd
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: "npm:^1.0.0"
  checksum: 10/6c9da3a66caddd83c875010a1ca8ef11eac02ba15fb592dc9418b2b5e7b77b645fa7729380a92d9835c2f05f2ca1b6251f39b993e0feb3f1517c74fa1af02cab
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10/9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10/faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10/a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.3":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10/fad11a0d4697a27162840b02b1fad249c1683cbc510cd5bf1a471f2f8085c046d41094308c577a50a03a579dd99d5a6b3724c4b5e8b14df2c4443844cfcda2c6
  languageName: node
  linkType: hard

"bson-objectid@npm:^2.0.4":
  version: 2.0.4
  resolution: "bson-objectid@npm:2.0.4"
  checksum: 10/dfc34b9bbebddc11d322b6fe41a0d8576d9538a37e3f8d4249e666e5580bb0801db741e2f5f0d5d5fbd6dd7969ce32890673223b83c4ce023d095a43d69a7231
  languageName: node
  linkType: hard

"buffer-equal-constant-time@npm:^1.0.1":
  version: 1.0.1
  resolution: "buffer-equal-constant-time@npm:1.0.1"
  checksum: 10/80bb945f5d782a56f374b292770901065bad21420e34936ecbe949e57724b4a13874f735850dd1cc61f078773c4fb5493a41391e7bda40d1fa388d6bd80daaab
  languageName: node
  linkType: hard

"c8@npm:^10.1.3":
  version: 10.1.3
  resolution: "c8@npm:10.1.3"
  dependencies:
    "@bcoe/v8-coverage": "npm:^1.0.1"
    "@istanbuljs/schema": "npm:^0.1.3"
    find-up: "npm:^5.0.0"
    foreground-child: "npm:^3.1.1"
    istanbul-lib-coverage: "npm:^3.2.0"
    istanbul-lib-report: "npm:^3.0.1"
    istanbul-reports: "npm:^3.1.6"
    test-exclude: "npm:^7.0.1"
    v8-to-istanbul: "npm:^9.0.0"
    yargs: "npm:^17.7.2"
    yargs-parser: "npm:^21.1.1"
  peerDependencies:
    monocart-coverage-reports: ^2
  peerDependenciesMeta:
    monocart-coverage-reports:
      optional: true
  bin:
    c8: bin/c8.js
  checksum: 10/6fdc2097fec28a14089fb6e450a5d7532b39391de5c1fd04803430c7a27be4fff6311388ab9ca8cc570c42062bf0c0fa1d7f23558957f7ab35b781f33a5f0906
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10/ea026b27b13656330c2bbaa462a88181dcaa0435c1c2e705db89b31d9bdf7126049d6d0445ba746dca21454a0cfdf1d6f47fd39d34c8c8435296b30bc5738a13
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10/00482c1f6aa7cfb30fb1dbeb13873edf81cfac7c29ed67a5957d60635a56b2a4a480f1016ddbdb3395cc37900d46037fb965043a51c5c789ffeab4fc535d18b5
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: 10/659b03c79bbfccf0cde3a79e7d52570724d7290209823e1ca5088f94b52192dc1836b82a324d0144612f816abb2f1734447438e38d9dafe0b3f82c2a1b9e3bce
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10/ef2b96e126ec0e58a7ff694db43f4d0d44f80e641370c21549ed911fecbdbc2df3ebc9bddad918d6bbdefeafb60bb3337902006d5176d72bcd2da74820991af7
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10/072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase@npm:6.3.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 10/8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10/cb3f3e594913d63b1814d7ca7c9bafbf895f75fbf93b92991980610dfd7b48500af4e3a5d4e3a8f337990a96b168d7eb84ee55efdce965e2ee8efc20f8c8f139
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10/b63cb1f73d171d140a2ed8154ee6566c8ab775d3196b0e03a2a94b5f6a0ce7777ee5685ca56849403c8d17bd457a6540672f9a60696a6137c7a409097495b82c
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.1"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10/eaa5561aeb3135c2cddf7a3b3f562fc4238ff3b3fc666869ef2adf264be0f372136702f16add9299087fb1907c2e4ec5dbfe83bd24bce815c70a80c6c1a2e950
  languageName: node
  linkType: hard

"collapse-white-space@npm:^1.0.0":
  version: 1.0.6
  resolution: "collapse-white-space@npm:1.0.6"
  checksum: 10/9673fb797952c5c888341435596c69388b22cd5560c8cd3f40edb72734a9c820f56a7c9525166bcb7068b5d5805372e6fd0c4b9f2869782ad070cb5d3faf26e7
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10/fa00c91b4332b294de06b443923246bccebe9fab1b253f7fe1772d37b06a2269b4039a85e309abe1fe11b267b11c08d1d0473fda3badd6167f57313af2887a64
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10/b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10/9680699c8e2b3af0ae22592cb764acaf973f292a7b71b8a06720233011853a58e256c89216a10cbe889727532fd77f8bcd49a760cedfde271b8e006c20e079f2
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10/c987be3ec061348cdb3c2bfb924bec86dea1eacad10550a85ca23edb0fe3556c3a61c7399114f3331ccb3499d7fd0285ab24566e5745929412983494c3926e15
  languageName: node
  linkType: hard

"country-data@npm:^0.0.31":
  version: 0.0.31
  resolution: "country-data@npm:0.0.31"
  dependencies:
    currency-symbol-map: "npm:~2"
    underscore: "npm:>1.4.4"
  checksum: 10/d15918acaf1207b0ab7a337f7d1f217ddc94bc6e5b1a23b4f03e234a43caf22576a58442ccc0a40ab170a08be4884033829d89df6fbdbe1df1aabfc8ce5b3253
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10/0d52657d7ae36eb130999dffff1168ec348687b48dd38e2ff59992ed916c88d328cf1d07ff4a4a10bc78de5e1c23f04b306d569e42f7a2293915c081e4dfee86
  languageName: node
  linkType: hard

"currency-symbol-map@npm:^5.1.0":
  version: 5.1.0
  resolution: "currency-symbol-map@npm:5.1.0"
  checksum: 10/7f6a4c58af8dc0150568a32bc5baa5dd8481abe6ae28918c2d7ccd86e8bd7e9dfa08e3bdeeb256da5dffb9974a91c11ce430023bf47fd9a19ea6f126668a4a20
  languageName: node
  linkType: hard

"currency-symbol-map@npm:~2":
  version: 2.2.0
  resolution: "currency-symbol-map@npm:2.2.0"
  checksum: 10/aa5e058962d40a779818ef68864784323f7b1f59806f727dbf0d0925bda239e5b1277cbb5073607e95090a9946c999a8b90dc1a997e4bad412ff201163512020
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10/c10b155a4e93999d3a215d08c23eea95f865e1f510b2e7748fcae1882b776df1afe8c99f483ace7fc0e5a3193ab08da138abebc9829d12003746c5a338c4d644
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10/2a47055fcf1ab3ec41b00b6f738c6461a841391a643c9ed9befec1117c1765b4d492661d97fb7cc899200c328949dca6ff189d2c6537d96d60e8a02dfe3c95f7
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.1"
  checksum: 10/fa3bdfa0968bea6711ee50375094b39f561bce3f15f9e558df59de9c25f0bdd4cddc002d9c1d70ac7772ebd36854a7e22d1761e7302a934e6f1c2263bcf44aa2
  languageName: node
  linkType: hard

"dayjs@npm:^1.11.13":
  version: 1.11.13
  resolution: "dayjs@npm:1.11.13"
  checksum: 10/7374d63ab179b8d909a95e74790def25c8986e329ae989840bacb8b1888be116d20e1c4eee75a69ea0dfbae13172efc50ef85619d304ee7ca3c01d5878b704f5
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10/8e2709b2144f03c7950f8804d01ccb3786373df01e406a0f66928e47001cf2d336cbed9ee137261d4f90d68d8679468c755e3548ed83ddacdc82b194d2468afe
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10/ec12d074aef5ae5e81fa470b9317c313142c9e8e2afe3f8efa124db309720db96d1d222b82b84c834e5f87e7a614b44a4684b6683583118b87c833b3be40d4d8
  languageName: node
  linkType: hard

"deepmerge@npm:^4.3.1":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 10/058d9e1b0ff1a154468bf3837aea436abcfea1ba1d165ddaaf48ca93765fdd01a30d33c36173da8fbbed951dd0a267602bc782fe288b0fc4b7e1e7091afc4529
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10/abdcb2505d80a53524ba871273e5da75e77e52af9e15b3aa65d8aad82b8a3a424dad7aee2cc0b71470ac7acf501e08defac362e8b6a73cdb4309f028061df4ae
  languageName: node
  linkType: hard

"define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 10/b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.2"
    entities: "npm:^4.2.0"
  checksum: 10/e3bf9027a64450bca0a72297ecdc1e3abb7a2912268a9f3f5d33a2e29c1e2c3502c6e9f860fc6625940bfe0cfb57a44953262b9e94df76872fdfb8151097eeb3
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: 10/ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: "npm:^2.3.0"
  checksum: 10/809b805a50a9c6884a29f38aec0a4e1b4537f40e1c861950ed47d10b049febe6b79ab72adaeeebb3cc8fc1cd33f34e97048a72a9265103426d93efafa78d3e96
  languageName: node
  linkType: hard

"domutils@npm:^3.2.1":
  version: 3.2.2
  resolution: "domutils@npm:3.2.2"
  dependencies:
    dom-serializer: "npm:^2.0.0"
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
  checksum: 10/2e08842151aa406f50fe5e6d494f4ec73c2373199fa00d1f77b56ec604e566b7f226312ae35ab8160bb7f27a27c7285d574c8044779053e499282ca9198be210
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10/5add88a3d68d42d6e6130a0cac450b7c2edbe73364bbd2fc334564418569bea97c6943a8fcd70e27130bf32afc236f30982fc4905039b703f23e9e0433c29934
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10/9b1d3e1baefeaf7d70799db8774149cef33b97183a6addceeba0cf6b85ba23ee2686f302f14482006df32df75d32b17c509c143a3689627929e4a8efaf483952
  languageName: node
  linkType: hard

"ecdsa-sig-formatter@npm:1.0.11":
  version: 1.0.11
  resolution: "ecdsa-sig-formatter@npm:1.0.11"
  dependencies:
    safe-buffer: "npm:^5.0.1"
  checksum: 10/878e1aab8a42773320bc04c6de420bee21aebd71810e40b1799880a8a1c4594bcd6adc3d4213a0fb8147d4c3f529d8f9a618d7f59ad5a9a41b142058aceda23f
  languageName: node
  linkType: hard

"email-addresses@npm:^5.0.0":
  version: 5.0.0
  resolution: "email-addresses@npm:5.0.0"
  checksum: 10/a7897e3b43893f1e9cc61f0e8c7cbe59c36c6cdd0b5ad7e4061f1976893260f496fd799fb78b2621e483a95fa6c7caec4a035ba320193d9540159dfcdb737004
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10/c72d67a6821be15ec11997877c437491c313d924306b8da5d87d2a2bcc2cec9903cb5b04ee1a088460501d8e5b44f10df82fdc93c444101a7610b80c8b6938e1
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10/915acf859cea7131dac1b2b5c9c8e35c4849e325a1d114c30adb8cd615970f6dca0e27f64f3a4949d7d6ed86ecd79a1c5c63f02e697513cddd7b5835c90948b8
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10/bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"entities@npm:^4.2.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 10/ede2a35c9bce1aeccd055a1b445d41c75a14a2bb1cd22e242f20cf04d236cdcd7f9c859eb83f76885327bfae0c25bf03303665ee1ce3d47c5927b98b0e3e3d48
  languageName: node
  linkType: hard

"entities@npm:^6.0.0":
  version: 6.0.0
  resolution: "entities@npm:6.0.0"
  checksum: 10/cf37a4aad887ba8573532346da1c78349dccd5b510a9bbddf92fe59b36b18a8b26fe619a862de4e7fd3b8addc6d5e0969261198bbeb690da87297011a61b7066
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10/65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10/1d20d825cdcce8d811bfbe86340f4755c02655a7feb2f13f8c880566d9d72a3f6c92c192a6867632e490d6da67b678271f46e01044996a6443e870331100dfdd
  languageName: node
  linkType: hard

"es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.9":
  version: 1.23.10
  resolution: "es-abstract@npm:1.23.10"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.2"
    arraybuffer.prototype.slice: "npm:^1.0.4"
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    data-view-buffer: "npm:^1.0.2"
    data-view-byte-length: "npm:^1.0.2"
    data-view-byte-offset: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    es-set-tostringtag: "npm:^2.1.0"
    es-to-primitive: "npm:^1.3.0"
    function.prototype.name: "npm:^1.1.8"
    get-intrinsic: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    get-symbol-description: "npm:^1.1.0"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    internal-slot: "npm:^1.1.0"
    is-array-buffer: "npm:^3.0.5"
    is-callable: "npm:^1.2.7"
    is-data-view: "npm:^1.0.2"
    is-regex: "npm:^1.2.1"
    is-shared-array-buffer: "npm:^1.0.4"
    is-string: "npm:^1.1.1"
    is-typed-array: "npm:^1.1.15"
    is-weakref: "npm:^1.1.1"
    math-intrinsics: "npm:^1.1.0"
    object-inspect: "npm:^1.13.4"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.7"
    own-keys: "npm:^1.0.1"
    regexp.prototype.flags: "npm:^1.5.4"
    safe-array-concat: "npm:^1.1.3"
    safe-push-apply: "npm:^1.0.0"
    safe-regex-test: "npm:^1.1.0"
    set-proto: "npm:^1.0.0"
    string.prototype.trim: "npm:^1.2.10"
    string.prototype.trimend: "npm:^1.0.9"
    string.prototype.trimstart: "npm:^1.0.8"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-length: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
    typed-array-length: "npm:^1.0.7"
    unbox-primitive: "npm:^1.1.0"
    which-typed-array: "npm:^1.1.19"
  checksum: 10/d3b6d560fa5eb6f3b4da4d4031d0a9455a7ec18f64ae0698831c0ce13d241f074a40e9711be3eb5f50c5844dde6070439ccfd6ce789a12bc9676e76379616d0c
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10/f8dc9e660d90919f11084db0a893128f3592b781ce967e4fccfb8f3106cb83e400a4032c559184ec52ee1dbd4b01e7776c7cd0b3327b1961b1a4a7008920fe78
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10/96e65d640156f91b707517e8cdc454dd7d47c32833aa3e85d79f24f9eb7ea85f39b63e36216ef0114996581969b59fe609a94e30316b08f5f4df1d44134cf8d5
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10/54fe77de288451dae51c37bfbfe3ec86732dc3778f98f3eb3bdb4bf48063b2c0b8f9c93542656986149d08aa5be3204286e2276053d19582b76753f1a2728867
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10/86814bf8afbcd8966653f731415888019d4bc4aca6b6c354132a7a75bb87566751e320369654a101d23a91c87a85c79b178bcf40332839bd347aff437c4fb65f
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: "npm:^1.2.7"
    is-date-object: "npm:^1.0.5"
    is-symbol: "npm:^1.0.4"
  checksum: 10/17faf35c221aad59a16286cbf58ef6f080bf3c485dff202c490d074d8e74da07884e29b852c245d894eac84f73c58330ec956dfd6d02c0b449d75eb1012a3f9b
  languageName: node
  linkType: hard

"esbuild@npm:~0.25.0":
  version: 0.25.4
  resolution: "esbuild@npm:0.25.4"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.25.4"
    "@esbuild/android-arm": "npm:0.25.4"
    "@esbuild/android-arm64": "npm:0.25.4"
    "@esbuild/android-x64": "npm:0.25.4"
    "@esbuild/darwin-arm64": "npm:0.25.4"
    "@esbuild/darwin-x64": "npm:0.25.4"
    "@esbuild/freebsd-arm64": "npm:0.25.4"
    "@esbuild/freebsd-x64": "npm:0.25.4"
    "@esbuild/linux-arm": "npm:0.25.4"
    "@esbuild/linux-arm64": "npm:0.25.4"
    "@esbuild/linux-ia32": "npm:0.25.4"
    "@esbuild/linux-loong64": "npm:0.25.4"
    "@esbuild/linux-mips64el": "npm:0.25.4"
    "@esbuild/linux-ppc64": "npm:0.25.4"
    "@esbuild/linux-riscv64": "npm:0.25.4"
    "@esbuild/linux-s390x": "npm:0.25.4"
    "@esbuild/linux-x64": "npm:0.25.4"
    "@esbuild/netbsd-arm64": "npm:0.25.4"
    "@esbuild/netbsd-x64": "npm:0.25.4"
    "@esbuild/openbsd-arm64": "npm:0.25.4"
    "@esbuild/openbsd-x64": "npm:0.25.4"
    "@esbuild/sunos-x64": "npm:0.25.4"
    "@esbuild/win32-arm64": "npm:0.25.4"
    "@esbuild/win32-ia32": "npm:0.25.4"
    "@esbuild/win32-x64": "npm:0.25.4"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10/227ffe9b31f0b184a0b0a0210bb9d32b2b115b8c5c9b09f08db2c3928cb470fc55a22dbba3c2894365d3abcc62c2089b85638be96a20691d1234d31990ea01b2
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10/9d7169e3965b2f9ae46971afa392f6e5a25545ea30f2e2dd99c9b0a95a3f52b5653681a84f5b2911a413ddad2d7a93d3514165072f349b5ffc59c75a899970d6
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10/98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-compat-utils@npm:^0.6.4":
  version: 0.6.5
  resolution: "eslint-compat-utils@npm:0.6.5"
  dependencies:
    semver: "npm:^7.5.4"
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 10/bb95e89ed11e9a29b2e9184967292b4b52be1e10fb36c84e89de95b4a5b23764a456b3818a867e232aec685186bd954e19f165cbe50b39659f729d97f1125820
  languageName: node
  linkType: hard

"eslint-json-compat-utils@npm:^0.2.1":
  version: 0.2.1
  resolution: "eslint-json-compat-utils@npm:0.2.1"
  dependencies:
    esquery: "npm:^1.6.0"
  peerDependencies:
    eslint: "*"
    jsonc-eslint-parser: ^2.4.0
  peerDependenciesMeta:
    "@eslint/json":
      optional: true
  checksum: 10/083272c0cdbc6acd9fe9bfe939e0c76493a426400141203d7f0e76344d5874c5a88535c59300045e4c6f95baa5084eff512013f82bcd9e7426404c785e2ea55d
  languageName: node
  linkType: hard

"eslint-plugin-jsonc@npm:^2.20.1":
  version: 2.20.1
  resolution: "eslint-plugin-jsonc@npm:2.20.1"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.5.1"
    eslint-compat-utils: "npm:^0.6.4"
    eslint-json-compat-utils: "npm:^0.2.1"
    espree: "npm:^9.6.1 || ^10.3.0"
    graphemer: "npm:^1.4.0"
    jsonc-eslint-parser: "npm:^2.4.0"
    natural-compare: "npm:^1.4.0"
    synckit: "npm:^0.6.2 || ^0.7.3 || ^0.11.5"
  peerDependencies:
    eslint: ">=6.0.0"
  checksum: 10/fc02f74ca20e0003adbd2b2a0b356073814e1d6393f8f3067a15aaf8e7b33b482e4a21bbe188463fef676f0dbb83f3c9e3809d897bdd8be4b016099ee54232fd
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.3.0":
  version: 8.3.0
  resolution: "eslint-scope@npm:8.3.0"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10/ee1ff009e949423639a8b53453c0cb189967d9142c5d94dc3752bed9880140a0760007148ac6b0bd03557d70ede9cd7c3b1e66f9a7f3427b2dbeca2a5be22c91
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.0.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10/3f357c554a9ea794b094a09bd4187e5eacd1bc0d0653c3adeb87962c548e6a1ab8f982b86963ae1337f5d976004146536dcee5d0e2806665b193fbfbf1a9231b
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.0":
  version: 4.2.0
  resolution: "eslint-visitor-keys@npm:4.2.0"
  checksum: 10/9651b3356b01760e586b4c631c5268c0e1a85236e3292bf754f0472f465bf9a856c0ddc261fceace155334118c0151778effafbab981413dbf9288349343fa25
  languageName: node
  linkType: hard

"eslint@npm:^9.27.0":
  version: 9.27.0
  resolution: "eslint@npm:9.27.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.12.1"
    "@eslint/config-array": "npm:^0.20.0"
    "@eslint/config-helpers": "npm:^0.2.1"
    "@eslint/core": "npm:^0.14.0"
    "@eslint/eslintrc": "npm:^3.3.1"
    "@eslint/js": "npm:9.27.0"
    "@eslint/plugin-kit": "npm:^0.3.1"
    "@humanfs/node": "npm:^0.16.6"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@humanwhocodes/retry": "npm:^0.4.2"
    "@types/estree": "npm:^1.0.6"
    "@types/json-schema": "npm:^7.0.15"
    ajv: "npm:^6.12.4"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.6"
    debug: "npm:^4.3.2"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^8.3.0"
    eslint-visitor-keys: "npm:^4.2.0"
    espree: "npm:^10.3.0"
    esquery: "npm:^1.5.0"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^8.0.0"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: 10/75f02b851c6f8534d1289de1bd957637a56725754bea03a0a710d6740a036aca81d5e600557633fca7ab774275aa94044ca05772f88c4f464cd42834eff37145
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.3.0, espree@npm:^9.6.1 || ^10.3.0":
  version: 10.3.0
  resolution: "espree@npm:10.3.0"
  dependencies:
    acorn: "npm:^8.14.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10/3412d44d4204c9e29d6b5dd0277400cfa0cd68495dc09eae1b9ce79d0c8985c1c5cc09cb9ba32a1cd963f48a49b0c46bdb7736afe395a300aa6bb1c0d86837e8
  languageName: node
  linkType: hard

"espree@npm:^9.0.0":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: "npm:^8.9.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^3.4.1"
  checksum: 10/255ab260f0d711a54096bdeda93adff0eadf02a6f9b92f02b323e83a2b7fc258797919437ad331efec3930475feb0142c5ecaaf3cdab4befebd336d47d3f3134
  languageName: node
  linkType: hard

"esprima@npm:~ 1.0.2":
  version: 1.0.4
  resolution: "esprima@npm:1.0.4"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10/968f923484e6dd9d6f8dd1e737efb5d85c1b1580cd1e43bbf5db4fc92a4973cf444dbf8c31d01abac4a93ff751acaaaeb74a0daef0b7dc0bf785ae6f0ebf8a95
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0, esquery@npm:^1.6.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10/c587fb8ec9ed83f2b1bc97cf2f6854cc30bf784a79d62ba08c6e358bf22280d69aee12827521cf38e69ae9761d23fb7fde593ce315610f85655c139d99b05e5a
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10/44ffcd89e714ea6b30143e7f119b104fc4d75e77ee913f34d59076b40ef2d21967f84e019f84e1fd0465b42cdbf725db449f232b5e47f29df29ed76194db8e16
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10/37cbe6e9a68014d34dbdc039f90d0baf72436809d02edffcc06ba3c2a12eb298048f877511353b130153e532aac8d68ba78430c0dd2f44806ebc7c014b01585e
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10/b23acd24791db11d8f65be5ea58fd9a6ce2df5120ae2da65c16cfc5331ff59d5ac4ef50af66cd4bde238881503ec839928a0135b99a036a9cdfa22d17fd56cdb
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10/ca2f01f1aa4dafd3f3917bd531ab5be08c6f5f4b2389d2e974f903de3cbeb50b9633374353516b6afd70905775e33aba11afab1232d3acf0aa2963b98a611c51
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10/e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10/dcc6432b269762dd47381d8b8358bf964d8f4f60286ac6aa41c01ade70bda459ff2001b516690b96d5365f68a49242966112b5d5cc9cd82395fa8f9d017c90ad
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10/2c20055c1fa43c922428f16ca8bb29f2807de63e5c851f665f7ac9790176c01c3b40335257736b299764a8d383388dabc73c8083b8e1bc3d99f0a941444ec60e
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10/eb7e220ecf2bab5159d157350b81d01f75726a4382f5a9266f42b9150c4523b9795f7f5d9fbbbeaeac09a441b2369f05ee02db48ea938584205530fe5693cfe1
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10/75679dc226316341c4f2a6b618571f51eac96779906faecd8921b984e844d6ae42fabb2df69b1071327d398d5716693ea9c9c8941f64ac9e89ec2032ce59d730
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.4
  resolution: "fdir@npm:6.4.4"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10/d0000d6b790059b35f4ed19acc8847a66452e0bc68b28766c929ffd523e5ec2083811fc8a545e4a1d4945ce70e887b3a610c145c681073b506143ae3076342ed
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: "npm:^4.0.0"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10/a7095cb39e5bc32fada2aa7c7249d3f6b01bd1ce461a61b0adabacccabd9198500c6fb1f68a7c851a657e273fce2233ba869638897f3d7ed2e87a2d89b4436ea
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10/07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.4"
  checksum: 10/58ce851d9045fffc7871ce2bd718bc485ad7e777bf748c054904b87c351ff1080c2c11da00788d78738bfb51b71e4d5ea12d13b98eb36e3358851ffe495b62dc
  languageName: node
  linkType: hard

"flat@npm:5.0.2":
  version: 5.0.2
  resolution: "flat@npm:5.0.2"
  bin:
    flat: cli.js
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: "npm:^1.2.7"
  checksum: 10/330cc2439f85c94f4609de3ee1d32c5693ae15cdd7fe3d112c4fd9efd4ce7143f2c64ef6c2c9e0cfdb0058437f33ef05b5bdae5b98fcc903fb2143fbaf0fea0f
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0, foreground-child@npm:^3.1.1":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10/427b33f997a98073c0424e5c07169264a62cda806d8d2ded159b5b903fdfc8f0a1457e06b5fc35506497acb3f1e353f025edee796300209ac6231e80edece835
  languageName: node
  linkType: hard

"franc@npm:5.0.0":
  version: 5.0.0
  resolution: "franc@npm:5.0.0"
  dependencies:
    trigram-utils: "npm:^1.0.0"
  checksum: 10/5beaf301b76bb6553f92bc43ae91167a2edc8121c69d7322c700e5a554e419e6f19dc49624a99587df6d076977a0d5a5479a86552c59b48ae25d8ffeaedf2b0c
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/af143246cf6884fe26fa281621d45cfe111d34b30535a475bfa38dafe343dadb466c047a924ffc7d6b7b18265df4110224ce3803806dbb07173bf2087b648d7f
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/4c1ade961ded57cdbfbb5cac5106ec17bc8bccd62e16343c569a0ceeca83b9dfef87550b4dc5cbb89642da412b20c5071f304c8c464b80415446e8e155a038c0
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10/185e20d20f10c8d661d59aac0f3b63b31132d492e1b11fcc2a93cb2c47257ebaee7407c38513efd2b35cafdf972d9beb2ea4593c1e0f3bf8f2744836928d7454
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    functions-have-names: "npm:^1.2.3"
    hasown: "npm:^2.0.2"
    is-callable: "npm:^1.2.7"
  checksum: 10/25b9e5bea936732a6f0c0c08db58cc0d609ac1ed458c6a07ead46b32e7b9bf3fe5887796c3f83d35994efbc4fdde81c08ac64135b2c399b8f2113968d44082bc
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 10/0ddfd3ed1066a55984aaecebf5419fbd9344a5c38dd120ffb0739fac4496758dcf371297440528b115e4367fc46e3abc86a2cc0ff44612181b175ae967a11a05
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10/b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10/6e9dd920ff054147b6f44cb98104330e87caafae051b6d37b13384a45ba15e71af33c3baeac7cb630a0aaa23142718dcf25b45cfdd86c184c5dcb4e56d953a10
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10/a353e3a9595a74720b40fb5bae3ba4a4f826e186e83814d93375182384265676f59e49998b9cdfac4a2225ce95a3d32a68f502a2c5619303987f1c183ab80494
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.7.5":
  version: 4.10.1
  resolution: "get-tsconfig@npm:4.10.1"
  dependencies:
    resolve-pkg-maps: "npm:^1.0.0"
  checksum: 10/04d63f47fdecaefbd1f73ec02949be4ec4db7d6d9fbc8d4e81f9a4bb1c6f876e48943712f2f9236643d3e4d61d9a7b06da08564d08b034631ebe3f5605bef237
  languageName: node
  linkType: hard

"get-value@npm:^4.0.1":
  version: 4.0.1
  resolution: "get-value@npm:4.0.1"
  checksum: 10/f78559a83f65afdcc00e26df31928a7ed2f2cc3948c3d29068d639b8d363ddbf3a2786477f67cb605e297fda6a384a4b2062ed76928fc821c8550e210134b3ef
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10/32cd106ce8c0d83731966d31517adb766d02c3812de49c30cfe0675c7c0ae6630c11214c54a5ae67aca882cf738d27fd7768f21aa19118b9245950554be07247
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10/c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.4.1":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10/698dfe11828b7efd0514cd11e573eaed26b2dff611f0400907281ce3eab0c1e56143ef9b35adc7c77ecc71fba74717b510c7c223d34ca8a98ec81777b293d4ac
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 10/03939c8af95c6df5014b137cac83aa909090c3a3985caef06ee9a5a669790877af8698ab38007e4c0186873adc14c0b13764acc754b16a754c216cc56aa5f021
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
  checksum: 10/1f1fd078fb2f7296306ef9dd51019491044ccf17a59ed49d375b576ca108ff37e47f3d29aead7add40763574a992f16a5367dd1e2173b8634ef18556ab719ac4
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10/94e296d69f92dc1c0768fcfeecfb3855582ab59a7c75e969d5f96ce50c3d201fd86d5a2857c22565764d5bb8a816c7b1e58f133ec318cd56274da36c5e3fb1a1
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10/bf152d0ed1dc159239db1ba1f74fdbc40cb02f626770dcd5815c427ce0688c2635a06ed69af364396da4636d0408fcf7d4afdf7881724c3307e46aff30ca49e2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10/6dd60dba97007b21e3a829fab3f771803cc1292977fe610e240ea72afd67e5690ac9eeaafc4a99710e78962e5936ab5a460787c2a1180f1cb0ccfac37d29f897
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 10/90fb1b24d40d2472bcd1c8bd9dd479037ec240215869bdbff97b2be83acef57d28f7e96bdd003a21bed218d058b49097f4acc8821c05b1629cc5d48dd7bfcccd
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10/261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10/2d8c9ab8cebb572e3362f7d06139a4592105983d4317e68f7adba320fe6ddfc8874581e0971e899e633fd5f72e262830edce36d5a0bc863dad17ad20572484b2
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: "npm:^1.0.0"
  checksum: 10/7eaed07728eaa28b77fadccabce53f30de467ff186a766872669a833ac2e87d8922b76a22cc58339d7e0277aefe98d6d00762113b27a97cdf65adcf958970935
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10/959385c98696ebbca51e7534e0dc723ada325efa3475350951363cce216d27373e0259b63edb599f72eb94d6cde8577b4b2375f080b303947e560f85692834fa
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10/c74c5f5ceee3c8a5b8bc37719840dc3749f5b0306d818974141dda2471a1a2ca6c8e46b9d6ac222c5345df7a901c9b6f350b1e6d62763fec877e26609a401bfe
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10/7898a9c1788b2862cf0f9c345a6bec77ba4a0c0983c7f19d610c382343d4f98fa260686b225dfb1f88393a66679d2ec58ee310c1d6868c081eda7918f32cc70a
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: 10/034d74029dcca544a34fb6135e98d427acd73019796ffc17383eaa3ec2fe1c0471dcbbc8f8ed39e46e86d43ccd753a160631615e4048285e313569609b66d5b7
  languageName: node
  linkType: hard

"htmlparser2@npm:^10.0.0":
  version: 10.0.0
  resolution: "htmlparser2@npm:10.0.0"
  dependencies:
    domelementtype: "npm:^2.3.0"
    domhandler: "npm:^5.0.3"
    domutils: "npm:^3.2.1"
    entities: "npm:^6.0.0"
  checksum: 10/768870f0e020dca19dc45df206cb6ac466c5dba6566c8fca4ca880347eed409f9977028d08644ac516bca8628ac9c7ded5a3847dc3ee1c043f049abf9e817154
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10/4efd2dfcfeea9d5e88c84af450b9980be8a43c2c8179508b1c57c7b4421c855f3e8efe92fa53e0b3f4a43c85824ada930eabbc306d1b3beab750b6dcc5187693
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10/d062acfa0cb82beeb558f1043c6ba770ea892b5fb7b28654dbc70ea2aeea55226dd34c02a294f6c1ca179a5aa483c4ea641846821b182edbd9cc5d89b54c6848
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10/784b628cbd55b25542a9d85033bdfd03d4eda630fb8b3c9477959367f3be95dc476ed2ecbb9836c359c7c698027fc7b45723a302324433590f45d6c1706e8c13
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10/24e3292dd3dadaa81d065c6f8c41b274a47098150d444b96e5f53b4638a9a71482921ea6a91a1f59bb71d9796de25e04afd05919fa64c360347ba65d3766f10f
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10/cceb6a457000f8f6a50e1196429750d782afce5680dd878aa4221bd79972d68b3a55b4b1458fc682be978f4d3c6a249046aa0880637367216444ab7b014cfc98
  languageName: node
  linkType: hard

"ignore@npm:^7.0.0":
  version: 7.0.4
  resolution: "ignore@npm:7.0.4"
  checksum: 10/01ee59df2ffd14b0844efc17f5ab3642c848e45efdb7cc757928da5e076cb74313748f77f5ffe362a6407c5e7cc71f10fad5e8eb9d91c1a17c4e7ef2c1f8e40e
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10/a06b19461b4879cc654d46f8a6244eb55eb053437afd4cbb6613cad6be203811849ed3e4ea038783092879487299fda24af932b86bdfff67c9055ba3612b8c87
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10/2d30b157a91fe1c1d7c6f653cbf263f039be6c5bfa959245a16d4ee191fc0f2af86c08545b6e6beeb041c56b574d2d5b9f95343d378ab49c0f37394d541e7fc8
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    hasown: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10/1d5219273a3dab61b165eddf358815eefc463207db33c20fcfca54717da02e3f492003757721f972fd0bf21e4b426cab389c5427b99ceea4b8b670dc88ee6d4a
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10/1ed81e06721af012306329b31f532b5e24e00cb537be18ddc905a84f19fe8f83a09a1699862bf3a1ec4b9dea93c55a3fa5faf8b5ea380431469df540f38b092c
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10/ef1095c55b963cd0dcf6f88a113e44a0aeca91e30d767c475e7d746d28d1195b10c5076b94491a7a0cd85020ca6a4923070021d74651d093dc909e9932cf689b
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: "npm:^1.0.0"
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10/7c2ac7efdf671e03265e74a043bcb1c0a32e226bc2a42dfc5ec8644667df668bbe14b91c08e6c1414f392f8cf86cd1d489b3af97756e2c7a49dd1ba63fd40ca6
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: "npm:^1.0.2"
  checksum: 10/10cf327310d712fe227cfaa32d8b11814c214392b6ac18c827f157e1e85363cf9c8e2a22df526689bd5d25e53b58cc110894787afb54e138e7c504174dba15fd
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/051fa95fdb99d7fbf653165a7e6b2cba5d2eb62f7ffa81e793a790f3fb5366c91c1b7b6af6820aa2937dd86c73aa3ca9d9ca98f500988457b1c59692c52ba911
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 10/48a9297fb92c99e9df48706241a189da362bff3003354aea4048bd5f7b2eb0d823cd16d0a383cece3d76166ba16d85d9659165ac6fcce1ac12e6c649d66dbdb9
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    is-typed-array: "npm:^1.1.13"
  checksum: 10/357e9a48fa38f369fd6c4c3b632a3ab2b8adca14997db2e4b3fe94c4cd0a709af48e0fb61b02c64a90c0dd542fd489d49c2d03157b05ae6c07f5e4dec9e730a8
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/3a811b2c3176fb31abee1d23d3dc78b6c65fd9c07d591fcb67553cab9e7f272728c3dd077d2d738b53f9a2103255b0a6e8dfc9568a7805c56a78b2563e8d1dec
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10/df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10/0bfb145e9a1ba852ddde423b0926d2169ae5fe9e37882cde9e8f69031281a986308df4d982283e152396e88b86562ed2256cbaa5e6390fb840a4c25ab54b8a80
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10/44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.0"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10/5906ff51a856a5fbc6b90a90fce32040b0a6870da905f98818f1350f9acadfc9884f7c3dec833fce04b83dd883937b86a190b6593ede82e8b1af8b6c4ecf7cbd
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10/3ed74f2b0cdf4f401f38edb0442ddfde3092d79d7d35c9919c86641efdbcbb32e45aa3c0f70ce5eecc946896cd5a0f26e4188b9f2b881876f7cb6c505b82da11
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: 10/8de7b41715b08bcb0e5edb0fb9384b80d2d5bcd10e142188f33247d19ff078abaf8e9b6f858e2302d8d05376a26a55cd23a3c9f8ab93292b02fcd2cc9e4e92bb
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/a5922fb8779ab1ea3b8a9c144522b3d0bea5d9f8f23f7a72470e61e1e4df47714e28e0154ac011998b709cce260c3c9447ad3cd24a96c2f2a0abfdb2cbdc76c8
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10/6a6c3383f68afa1e05b286af866017c78f1226d43ac8cb064e115ff9ed85eb33f5c4f7216c96a71e4dfea289ef52c5da3aef5bbfade8ffe47a0465d70c0c8e86
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: 10/2a401140cfd86cabe25214956ae2cfee6fbd8186809555cd0e84574f88de7b17abacb2e477a6a658fa54c6083ecbda1e6ae404c7720244cd198903848fca70ca
  languageName: node
  linkType: hard

"is-primitive@npm:^3.0.1":
  version: 3.0.1
  resolution: "is-primitive@npm:3.0.1"
  checksum: 10/c4da6a6e6d487f31d85b9259b67695fffcc75dca6c9612b0a002e3050c734227b9911be09b877539ec6309710229c19f4edd0f9e26ed2a67924ee0916baf0bed
  languageName: node
  linkType: hard

"is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10/c42b7efc5868a5c9a4d8e6d3e9816e8815c611b09535c00fead18a1138455c5cb5e1887f0023a467ad3f9c419d62ba4dc3d9ba8bafe55053914d6d6454a945d2
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 10/5685df33f0a4a6098a98c72d94d67cad81b2bc72f1fb2091f3d9283c4a1c582123cd709145b02a9745f0ce6b41e3e43f1c944496d1d74d4ea43358be61308669
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10/0380d7c60cc692856871526ffcd38a8133818a2ee42d47bb8008248a0cd2121d8c8b5f66b6da3cac24bc5784553cacb6faaf678f66bc88c6615b42af2825230e
  languageName: node
  linkType: hard

"is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/5277cb9e225a7cc8a368a72623b44a99f2cfa139659c6b203553540681ad4276bfc078420767aad0e73eef5f0bd07d4abf39a35d37ec216917879d11cebc1f8b
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10/db495c0d8cd0a7a66b4f4ef7fccee3ab5bd954cb63396e8ac4d32efe0e9b12fdfceb851d6c501216a71f4f21e5ff20fc2ee845a3d52d455e021c466ac5eb2db2
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: "npm:^1.1.16"
  checksum: 10/e8cf60b9ea85667097a6ad68c209c9722cfe8c8edf04d6218366469e51944c5cc25bae45ffb845c23f811d262e4314d3b0168748eb16711aa34d12724cdf0735
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: 10/a7b7e23206c542dcf2fa0abc483142731788771527e90e7e24f658c0833a0d91948a4f7b30d78f7a65255a48512e41a0288b778ba7fc396137515c12e201fd11
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10/543506fd8259038b371bb083aac25b16cb4fd8b12fc58053aa3d45ac28dfd001cd5c6dffbba7aeea4213c74732d46b6cb2cfb5b412eed11f2db524f3f97d09a0
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10/1d5e1d0179beeed3661125a6faa2e59bfb48afda06fc70db807f178aa0ebebc3758fb6358d76b3d528090d5ef85148c345dcfbf90839592fe293e3e5e82f2134
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 10/1d8bc7911e13bb9f105b1b3e0b396c787a9e63046af0b8fe0ab1414488ab06b2b099b87a2d8a9e31d21c9a6fad773c7fc8b257c4880f2d957274479d28ca3414
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10/7c9f715c03aff08f35e98b1fadae1b9267b38f0615d501824f9743f3aab99ef10e303ce7db3f186763a0b70a19de5791ebfc854ff884d5a8c4d92211f642ec92
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10/7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: 10/db85c4c970ce30693676487cca0e61da2ca34e8d4967c2e1309143ff910c207133a969f9e4ddb2dc6aba670aabce4e0e307146c310350b298e74a31f7d464703
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.0":
  version: 3.2.2
  resolution: "istanbul-lib-coverage@npm:3.2.2"
  checksum: 10/40bbdd1e937dfd8c830fa286d0f665e81b7a78bdabcd4565f6d5667c99828bda3db7fb7ac6b96a3e2e8a2461ddbc5452d9f8bc7d00cb00075fa6a3e99f5b6a81
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0, istanbul-lib-report@npm:^3.0.1":
  version: 3.0.1
  resolution: "istanbul-lib-report@npm:3.0.1"
  dependencies:
    istanbul-lib-coverage: "npm:^3.0.0"
    make-dir: "npm:^4.0.0"
    supports-color: "npm:^7.1.0"
  checksum: 10/86a83421ca1cf2109a9f6d193c06c31ef04a45e72a74579b11060b1e7bb9b6337a4e6f04abfb8857e2d569c271273c65e855ee429376a0d7c91ad91db42accd1
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.1.6":
  version: 3.1.7
  resolution: "istanbul-reports@npm:3.1.7"
  dependencies:
    html-escaper: "npm:^2.0.0"
    istanbul-lib-report: "npm:^3.0.0"
  checksum: 10/f1faaa4684efaf57d64087776018d7426312a59aa6eeb4e0e3a777347d23cd286ad18f427e98f0e3dee666103d7404c9d7abc5f240406a912fa16bd6695437fa
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10/96f8786eaab98e4bf5b2a5d6d9588ea46c4d06bbc4f2eb861fdd7b6b182b16f71d8a70e79820f335d52653b16d4843b29dd9cdcf38ae80406756db9199497cf3
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10/c138a34a3fd0d08ebaf71273ad4465569a483b8a639e0b118ff65698d257c2791d3199e3f303631f2cb98213fa7b5f5d6a4621fd0fff819421b990d30d967140
  languageName: node
  linkType: hard

"js-yaml@npm:~2.1.0":
  version: 2.1.3
  resolution: "js-yaml@npm:2.1.3"
  dependencies:
    argparse: "npm:~ 0.1.11"
    esprima: "npm:~ 1.0.2"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10/5e59311627d725ffe3eba0c36208092079441ea35ceb8bcb9b0add0ac2868b1df577d82d37b352e451000d50b04896d8ee408ec1b18e5b864213a6e66919f421
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10/bebe7ae829bbd586ce8cbe83501dd8cb8c282c8902a8aeeed0a073a89dc37e8103b1244f3c6acd60278bcbfe12d93a3f83c9ac396868a3b3bbc3c5e5e3b648ef
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10/82876154521b7b68ba71c4f969b91572d1beabadd87bd3a6b236f85fbc7dc4695089191ed60bb59f9340993c51b33d479f45b6ba9f3548beb519705281c32c3c
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10/7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10/12786c2e2f22c27439e6db0532ba321f1d0617c27ad8cb1c352a0e9249a50182fd1ba8b52a18899291604b0c32eafa8afd09e51203f19109a0537f68db2b652d
  languageName: node
  linkType: hard

"jsonc-eslint-parser@npm:^2.4.0":
  version: 2.4.0
  resolution: "jsonc-eslint-parser@npm:2.4.0"
  dependencies:
    acorn: "npm:^8.5.0"
    eslint-visitor-keys: "npm:^3.0.0"
    espree: "npm:^9.0.0"
    semver: "npm:^7.3.5"
  checksum: 10/bd1d41c852c3488414605a1754617aa7c240ed6730a25a7fd7fb76473e92efdc5ba1728ad3f08f8069de3a19abf1fd275c2b145eb51e2f7f6ca293c8105e1ffe
  languageName: node
  linkType: hard

"jwa@npm:^2.0.0":
  version: 2.0.1
  resolution: "jwa@npm:2.0.1"
  dependencies:
    buffer-equal-constant-time: "npm:^1.0.1"
    ecdsa-sig-formatter: "npm:1.0.11"
    safe-buffer: "npm:^5.0.1"
  checksum: 10/b04312a1de85f912b96aa3a7211717b8336945fab5b4f7cbc7800f4c80934060c0a3111576fad8d76e41ad62887d6da4b21fd4c47e45c174197f8be7dc0c1694
  languageName: node
  linkType: hard

"jws@npm:^4.0.0":
  version: 4.0.0
  resolution: "jws@npm:4.0.0"
  dependencies:
    jwa: "npm:^2.0.0"
    safe-buffer: "npm:^5.0.1"
  checksum: 10/1d15f4cdea376c6bd6a81002bd2cb0bf3d51d83da8f0727947b5ba3e10cf366721b8c0d099bf8c1eb99eb036e2c55e5fd5efd378ccff75a2b4e0bd10002348b9
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10/167eb6ef64cc84b6fa0780ee50c9de456b422a1e18802209234f7c2cf7eae648c7741f32e50d7e24ccb22b24c13154070b01563d642755b156c357431a191e75
  languageName: node
  linkType: hard

"levenshtein-edit-distance@npm:^2.0.0":
  version: 2.0.5
  resolution: "levenshtein-edit-distance@npm:2.0.5"
  bin:
    levenshtein-edit-distance: cli.js
  checksum: 10/50618c01cd0c9bae6d4371d75af62c17c25a8f91bfd8d06400315b8b15976900cff951b48e102e074e9c5c6758260fff1675cfad186732afe124a5708e1032fd
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10/2e4720ff79f21ae08d42374b0a5c2f664c5be8b6c8f565bb4e1315c96ed3a8acaa9de788ffed82d7f2378cf36958573de07ef92336cb5255ed74d08b8318c9ee
  languageName: node
  linkType: hard

"libphonenumber-js@npm:^1.12.8":
  version: 1.12.8
  resolution: "libphonenumber-js@npm:1.12.8"
  checksum: 10/476e5268f5a86509de80ed5e573afbd2aff457d88cbe116208d167563f538705d3b29c9cc45e8d58dc6b5d00babc348443e49239c367c4e2e26e8c71e96372bc
  languageName: node
  linkType: hard

"limiter@npm:^3.0.0":
  version: 3.0.0
  resolution: "limiter@npm:3.0.0"
  checksum: 10/d45865d74b147b715dc2077cf9db1ed5c05c667f303b2ab69d60c65c6cbef0de099f4124065828294a02fcb093dfb2309034bbd382d1b2528f10988030ea8bd1
  languageName: node
  linkType: hard

"locale@npm:^0.1.0":
  version: 0.1.0
  resolution: "locale@npm:0.1.0"
  checksum: 10/486e6a604b9ce757be5cbc9cb9d3c1aade13e69b28a8fc9063eaeebdacae6c2b18f660599c409e5f563cf07a530e12cfab75846d99a2b58a237d35285887551d
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10/72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10/d0ea2dd0097e6201be083865d50c3fb54fbfbdb247d9cc5950e086c991f448b7ab0cdab0d57eacccb43473d3f2acd21e134db39f22dac2d6c9ba6bf26978e3d6
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10/e6e90267360476720fa8e83cc168aa2bf0311f3f2eea20a6ba78b90a885ae72071d9db132f40fda4129c803e7dcec3a6b6a6fbb44ca90b081630b810b5d6a41a
  languageName: node
  linkType: hard

"make-dir@npm:^4.0.0":
  version: 4.0.0
  resolution: "make-dir@npm:4.0.0"
  dependencies:
    semver: "npm:^7.5.3"
  checksum: 10/bf0731a2dd3aab4db6f3de1585cea0b746bb73eb5a02e3d8d72757e376e64e6ada190b1eddcde5b2f24a81b688a9897efd5018737d05e02e2a671dda9cff8a8a
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10/fce0385840b6d86b735053dfe941edc2dd6468fda80fe74da1eeff10cbd82a75760f406194f2bc2fa85b99545b2bc1f84c08ddf994b21830775ba2d1a87e8bdf
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10/11df2eda46d092a6035479632e1ec865b8134bdfc4bd9e571a656f4191525404f13a283a515938c3a8de934dbfd9c09674d9da9fa831e6eb7e22b50b197d2edd
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10/7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10/6bf2a01672e7965eb9941d1f02044fad2bd12486b5553dc1116ff24c09a8723157601dc992e74c911d896175918448762df3b3fd0a6b61037dd1a9766ddfbf58
  languageName: node
  linkType: hard

"minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10/e0b25b04cd4ec6732830344e5739b13f8690f8a012d73445a4a19fbc623f5dd481ef7a5827fde25954cd6026fede7574cc54dc4643c99d6c6b653d6203f94634
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/dd6a8927b063aca6d910b119e1f2df6d2ce7d36eab91de83167dd136bb85e1ebff97b0d3de1cb08bd1f7e018ca170b4962479fefab5b2a69e2ae12cb2edc8348
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10/7ddfebdbb87d9866e7b5f7eead5a9e3d9d507992af932a11d275551f60006cf7d9178e66d586dbb910894f3e3458d27c0ddf93c76e94d49d0a54a541ddc1263d
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/40982d8d836a52b0f37049a0a7e5d0f089637298e6d9b45df9c115d4f0520682a78258905e5c8b180fb41b593b0a82cc1361d2c74b45f7ada66334f84d1ecfdd
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10/a5c6ef069f70d9a524d3428af39f2b117ff8cd84172e19b754e7264a33df460873e6eb3d6e55758531580970de50ae950c496256bb4ad3691a2974cddff189f0
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10/c25f0ee8196d8e6036661104bacd743785b2599a21de5c516b32b3fa2b83113ac89a2358465bc04956baab37ffb956ae43be679b2262bf7be15fce467ccd7950
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10/c075bed1594f68dcc8c35122333520112daefd4d070e5d0a228bd4cf5580e9eed3981b96c0ae1d62488e204e80fd27b2b9d0068ca9a5ef3993e9565faf63ca41
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10/16fd79c28645759505914561e249b9a1f5fe3362279ad95487a4501e4467abeb714fd35b95307326b8fd03f3c7719065ef11a6f97b7285d7888306d1bd2232ba
  languageName: node
  linkType: hard

"ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10/aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"n-gram@npm:^1.0.0":
  version: 1.1.2
  resolution: "n-gram@npm:1.1.2"
  checksum: 10/e9f324cd4ebc57b2df927fb792c47a7d6fb65bc460d00ab8eda89d4214a15421b00c6c3415516011a7d3ce17bbf93ec787caf82eb31a4043199efcc2f716bc3f
  languageName: node
  linkType: hard

"nanoid@npm:3.3.8":
  version: 3.3.8
  resolution: "nanoid@npm:3.3.8"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10/2d1766606cf0d6f47b6f0fdab91761bb81609b2e3d367027aff45e6ee7006f660fb7e7781f4a34799fe6734f1268eeed2e37a5fdee809ade0c2d4eb11b0f9c40
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10/23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10/b5734e87295324fabf868e36fb97c84b7d7f3156ec5f4ee5bf6e488079c11054f818290fc33804cef7b1ee21f55eeb14caea83e7dafae6492a409b3e573153e5
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10/806fd8e3adc9157e17bf0d4a2c899cf6b98a0bbe9f453f630094ce791866271f6cddcaf2133e6513715d934fcba2014d287c7053d5d7934937b3a34d5a3d84ad
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10/26ab456c51a96f02a9e5aa8d1b80ef3219f2070f3f3528a040e32fb735b1e651e17bdf0f1476988d3a46d498f35c65ed662d122f340d38ce4a7e71dd7b20c4bc
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3, object-inspect@npm:^1.13.4":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10/aa13b1190ad3e366f6c83ad8a16ed37a19ed57d267385aa4bfdccda833d7b90465c057ff6c55d035a6b2e52c1a2295582b294217a0a3a1ae7abdd6877ef781fb
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10/3d81d02674115973df0b7117628ea4110d56042e5326413e4b4313f0bcdf7dd78d4a3acef2c831463fa3796a66762c49daef306f4a0ea1af44877d7086d73bde
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    object-keys: "npm:^1.1.1"
  checksum: 10/3fe28cdd779f2a728a9a66bd688679ba231a2b16646cd1e46b528fe7c947494387dda4bc189eff3417f3717ef4f0a8f2439347cf9a9aa3cef722fbfd9f615587
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10/a8398559c60aef88d7f353a4f98dcdff6090a4e70f874c827302bf1213d9106a1c4d5fcb68dacb1feb3c30a04c4102f41047aa55d4c576b863d6fc876e001af6
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.6"
    object-keys: "npm:^1.1.1"
    safe-push-apply: "npm:^1.0.0"
  checksum: 10/ab4bb3b8636908554fc19bf899e225444195092864cb61503a0d048fdaf662b04be2605b636a4ffeaf6e8811f6fcfa8cbb210ec964c0eb1a41eb853e1d5d2f41
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10/7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10/1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10/2ef48ccfc6dd387253d71bf502604f7893ed62090b2c9d73387f10006c342606b05233da0e4f29388227b61eb5aeface6197e166520c465c234552eeab2fe633
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10/58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10/6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10/505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10/55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10/5e8845c159261adda6f09814d7725683257fcc85a18f329880ab4d7cc1d12830967eae5d5894e453f341710d5484b8fdbbd4d75181b4d6e1eb2f4dc7aeadc434
  languageName: node
  linkType: hard

"picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10/60c2595003b05e4535394d1da94850f5372c9427ca4413b71210f437f7b2ca091dbd611c45e8b37d10036fa8eade25c1b8951654f9d3973bfa66a2ff4d3b08bc
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10/ce617b8da36797d09c0baacb96ca8a44460452c89362d7cb8f70ca46b4158ba8bc3606912de7c818eb4a939f7f9015cef3c766ec8a0c6bfc725fdc078e39c717
  languageName: node
  linkType: hard

"pinyin-pro@npm:^3.26.0":
  version: 3.26.0
  resolution: "pinyin-pro@npm:3.26.0"
  checksum: 10/06f6b08e0b0cd151eb2f202cd6606f455b5d9c996a45c8cac4f52de4c40e432a4561480b75e15410e15649a851c078392149d32dd188f32cf81cec640bb083e0
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: 10/2f44137b8d3dd35f4a7ba7469eec1cd9cfbb46ec164b93a5bc1f4c3d68599c9910ee3b91da1d28b4560e9cc8414c3cd56fedc07259c67e52cc774476270d3302
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10/0b9d2c76801ca652a7f64892dd37b7e3fab149a37d2424920099bf894acccc62abb4424af2155ab36dea8744843060a2d8ddc983518d0b1e22265a22324b72ed
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10/35610bdb0177d3ab5d35f8827a429fb1dc2518d9e639f2151ac9007f01a061c30e0c635a970c9b00c39102216160f6ec54b62377c92fac3b7bfc2ad4b98d195c
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10/96e1a82453c6c96eef53a37a1d6134c9f2482f94068f98a59145d0986ca4e497bf110a410adf73857e588165eab3899f0ebcf7b3890c1b3ce802abc0d65967d4
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10/febdc4362bead22f9e2608ff0171713230b57aff9dddc1c273aa2a651fbd366f94b7d6a71d78342a7c0819906750351ca7f2edd26ea41b626d87d6a13d1bd059
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10/72900df0616e473e824202113c3df6abae59150dfb73ed13273503127235320e9c8ca4aaaaccfd58cf417c6ca92a6e68ee9a5c3182886ae949a768639b388a7b
  languageName: node
  linkType: hard

"referer-parser@npm:^0.0.3":
  version: 0.0.3
  resolution: "referer-parser@npm:0.0.3"
  dependencies:
    js-yaml: "npm:~2.1.0"
  checksum: 10/c77dc3e7fccfa0b42443e05b86a58c0703fc6c7010094b13dd72c5ecb787c6aab3dcbbb94cfc0d50f8b43d67b25c814907b7e9b48569bb7e38742d4cdc31738d
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.1"
    which-builtin-type: "npm:^1.2.1"
  checksum: 10/80a4e2be716f4fe46a89a08ccad0863b47e8ce0f49616cab2d65dab0fbd53c6fdba0f52935fd41d37a2e4e22355c272004f920d63070de849f66eea7aeb4a081
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.4":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10/8ab897ca445968e0b96f6237641510f3243e59c180ee2ee8d83889c52ff735dd1bf3657fcd36db053e35e1d823dd53f2565d0b8021ea282c9fe62401c6c3bd6d
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10/a72468e2589270d91f06c7d36ec97a88db53ae5d6fe3787fadc943f0b0276b10347f89b363b2a82285f650bdcc135ad4a257c61bdd4d00d6df1fa24875b0ddaf
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10/91eb76ce83621eea7bbdd9b55121a5c1c4a39e54a9ce04a9ad4517f102f8b5131c2cf07622c738a6683991bf54f2ce178f5a42803ecbd527ddc5105f362cc9e3
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 10/0763150adf303040c304009231314d1e84c6e5ebfa2d82b7d94e96a6e82bacd1dcc0b58ae257315f3c8adb89a91d8d0f12928241cba2df1680fbe6f60bf99b0e
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10/1f914879f97e7ee931ad05fe3afa629bd55270fc6cf1c1e589b6a99fab96d15daad0fa1a52a00c729ec0078045fe3e399bd4fd0c93bcc906957bdc17f89cb8e6
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10/af47851b547e8a8dc89af144fceee17b80d5beaf5e6f57ed086432d79943434ff67ca526e92275be6f54b6189f6920a24eace75c2657eed32d02c400312b21ec
  languageName: node
  linkType: hard

"rfdc@npm:^1.4.1":
  version: 1.4.1
  resolution: "rfdc@npm:1.4.1"
  checksum: 10/2f3d11d3d8929b4bfeefc9acb03aae90f971401de0add5ae6c5e38fec14f0405e6a4aad8fdb76344bfdd20c5193110e3750cbbd28ba86d73729d222b6cf4a729
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10/cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    has-symbols: "npm:^1.1.0"
    isarray: "npm:^2.0.5"
  checksum: 10/fac4f40f20a3f7da024b54792fcc61059e814566dcbb04586bfefef4d3b942b2408933f25b7b3dd024affd3f2a6bbc916bef04807855e4f192413941369db864
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.0.1":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10/32872cd0ff68a3ddade7a7617b8f4c2ae8764d8b7d884c651b74457967a9e0e886267d3ecc781220629c44a865167b61c375d2da6c720c840ecd73f45d5d9451
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    isarray: "npm:^2.0.5"
  checksum: 10/2bd4e53b6694f7134b9cf93631480e7fafc8637165f0ee91d5a4af5e7f33d37de9562d1af5021178dd4217d0230cde8d6530fa28cfa1ebff9a431bf8fff124b4
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-regex: "npm:^1.2.1"
  checksum: 10/ebdb61f305bf4756a5b023ad86067df5a11b26898573afe9e52a548a63c3bd594825d9b0e2dde2eb3c94e57e0e04ac9929d4107c394f7b8e56a4613bed46c69a
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10/7eaf7a0cf37cc27b42fb3ef6a9b1df6e93a1c6d98c6c6702b02fe262d5fcbd89db63320793b99b21cb5348097d0a53de81bd5f4e8b86e20cc9412e3f1cfb4e83
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.0":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10/7a24cffcaa13f53c09ce55e05efe25cd41328730b2308678624f8b9f5fc3093fc4d189f47950f0b811ff8f3c3039c24a2c36717ba7961615c682045bf03e1dda
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/505d62b8e088468917ca4e3f8f39d0e29f9a563b97dbebf92f4bd2c3172ccfb3c5b8e4566d5fcd00784a00433900e7cb8fbc404e2dbd8c3818ba05bb9d4a8a6d
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/c7614154a53ebf8c0428a6c40a3b0b47dac30587c1a19703d1b75f003803f73cdfa6a93474a9ba678fa565ef5fbddc2fae79bca03b7d22ab5fd5163dbe571a74
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/b87f8187bca595ddc3c0721ece4635015fd9d7cb294e6dd2e394ce5186a71bbfa4dc8a35010958c65e43ad83cde09642660e61a952883c24fd6b45ead15f045c
  languageName: node
  linkType: hard

"set-value@npm:^4.1.0":
  version: 4.1.0
  resolution: "set-value@npm:4.1.0"
  dependencies:
    is-plain-object: "npm:^2.0.4"
    is-primitive: "npm:^3.0.1"
  checksum: 10/67eebb0d78be89242478daf8ab5357c59a35add1d7bdccdb56236e6004128a79bdf298c952e286b60b63ed17b24dcdb399734fbdacc9b76e7fd2e3e01546a42e
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10/6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10/1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10/603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10/5771861f77feefe44f6195ed077a9e4f389acc188f895f570d56445e251b861754b547ea9ef73ecee4e01fdada6568bfe9020d2ec2dfc5571e9fa1bbc4a10615
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10/a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10/7d53b9db292c6262f326b6ff3bc1611db84ece36c2c7dc0e937954c13c73185b0406c56589e2bb8d071d6fee468e14c39fb5d203ee39be66b7b8174f179afaba
  languageName: node
  linkType: hard

"sift@npm:^17.1.3":
  version: 17.1.3
  resolution: "sift@npm:17.1.3"
  checksum: 10/278a0308cb4c7a48620caeb25339072fcef50d9a1296bc78c9710b1bb974ef5b3e375acf6646fe0a698e0f3ea19af81c11a348980dc28ff80c979e050f7f8585
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10/c9fa63bbbd7431066174a48ba2dd9986dfd930c3a8b59de9c29d7b6854ec1c12a80d15310869ea5166d413b99f041bfa3dd80a7947bcd44ea8e6eb3ffeabfa1f
  languageName: node
  linkType: hard

"similarity@npm:^1.2.1":
  version: 1.2.1
  resolution: "similarity@npm:1.2.1"
  dependencies:
    levenshtein-edit-distance: "npm:^2.0.0"
  bin:
    similarity: cli.js
  checksum: 10/7010abfb53ea72fcecb3b9f59e0753f589256b8a134886a5282cd1ee21226fdc7f11bfe45f673631928d8ae88283f23e6b6f5c4e84ab3f15d175b2d520e1bffe
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10/927484aa0b1640fd9473cee3e0a0bcad6fce93fd7bbc18bac9ad0c33686f5d2e2c422fba24b5899c184524af01e11dd2bd051c2bf2b07e47aff8ca72cbfc60d2
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10/ee99e1dacab0985b52cbe5a75640be6e604135e9489ebdc3048635d186012fbaecc20fbbe04b177dee434c319ba20f09b3e7dfefb7d932466c0d707744eac05c
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.4
  resolution: "socks@npm:2.8.4"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10/ab3af97aeb162f32c80e176c717ccf16a11a6ebb4656a62b94c0f96495ea2a1f4a8206c04b54438558485d83d0c5f61920c07a1a5d3963892a589b40cc6107dd
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10/e7587128c423f7e43cc625fe2f87e6affdf5ca51c1cc468e910d8aaca46bb44a7fbcfa552f787b1d3987f7043aeb4527d1b99559e6621e01b42b3f45e5a24cbb
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/7024c1a6e39b3f18aa8f1c8290e884fe91b0f9ca5a6c6d410544daad54de0ba664db879afe16412e187c6c292fd60b937f047ee44292e5c2af2dcc6d8e1a9b48
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10/e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-data-property: "npm:^1.1.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-object-atoms: "npm:^1.0.0"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/47bb63cd2470a64bc5e2da1e570d369c016ccaa85c918c3a8bb4ab5965120f35e66d1f85ea544496fac84b9207a6b722adf007e6c548acd0813e5f8a82f9712a
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/140c73899b6747de9e499c7c2e7a83d549c47a26fa06045b69492be9cfb9e2a95187499a373983a08a115ecff8bc3bd7b0fb09b8ff72fb2172abe766849272ef
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/160167dfbd68e6f7cb9f51a16074eebfce1571656fc31d40c3738ca9e30e35496f2c046fe57b6ad49f65f238a152be8c86fd9a2dd58682b5eba39dad995b3674
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10/ae3b5436d34fadeb6096367626ce987057713c566e1e7768818797e00ac5d62023d0f198c4e681eae9e20701721980b26a64a8f5b91238869592a9c6800719a2
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10/475f53e9c44375d6e72807284024ac5d668ee1d06010740dec0b9744f2ddf47de8d7151f80e5f6190fc8f384e802fdf9504b76a7e9020c9faee7103623338be2
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10/492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10/c8bb7afd564e3b26b50ca6ee47572c217526a1389fe018d00345856d4a9b08ffbd61fadaf283a87368d94c3dcdb8f5ffe2650a5a65863e21ad2730ca0f05210a
  languageName: node
  linkType: hard

"synckit@npm:^0.6.2 || ^0.7.3 || ^0.11.5":
  version: 0.11.6
  resolution: "synckit@npm:0.11.6"
  dependencies:
    "@pkgr/core": "npm:^0.2.4"
  checksum: 10/e3fc13b064cb401ad4214e846f3e70037a44a221fcd303ad638c02d14e3f497ddd501b5b9f5e324e03fa2a3ca101df43f0850faa1696abaf0230a16ce9693408
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10/12a2a4fc6dee23e07cc47f1aeb3a14a1afd3f16397e1350036a8f4cdfee8dcac7ef5978337a4e7b2ac2c27a9a6d46388fc2088ea7c80cb6878c814b1425f8ecf
  languageName: node
  linkType: hard

"test-exclude@npm:^7.0.1":
  version: 7.0.1
  resolution: "test-exclude@npm:7.0.1"
  dependencies:
    "@istanbuljs/schema": "npm:^0.1.2"
    glob: "npm:^10.4.1"
    minimatch: "npm:^9.0.4"
  checksum: 10/e6f6f4e1df2e7810e082e8d7dfc53be51a931e6e87925f5e1c2ef92cc1165246ba3bf2dae6b5d86251c16925683dba906bd41e40169ebc77120a2d1b5a0dbbe0
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10/3d306d319718b7cc9d79fb3f29d8655237aa6a1f280860a217f93417039d0614891aee6fc47c5db315f4fcc6ac8d55eb8e23e2de73b2c51a431b42456d9e5764
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10/10dda13571e1f5ad37546827e9b6d4252d2e0bc176c24a101252153ef435d83696e2557fe128c4678e4e78f5f01e83711c703eef9814eb12dab028580d45980a
  languageName: node
  linkType: hard

"traverse@npm:^0.6.11":
  version: 0.6.11
  resolution: "traverse@npm:0.6.11"
  dependencies:
    gopd: "npm:^1.2.0"
    typedarray.prototype.slice: "npm:^1.0.5"
    which-typed-array: "npm:^1.1.18"
  checksum: 10/f70d9ea9dd7e2f14e805815b9734bcac5f459b5946b32b4dbfc6e66f1738dc21921c7353722b61e7bd4d3b79ff5b51234078e0a243d72eaeccef802353cc9075
  languageName: node
  linkType: hard

"trigram-utils@npm:^1.0.0":
  version: 1.0.3
  resolution: "trigram-utils@npm:1.0.3"
  dependencies:
    collapse-white-space: "npm:^1.0.0"
    n-gram: "npm:^1.0.0"
    trim: "npm:0.0.1"
  checksum: 10/0249ed2a7e5a382ef00f986d5c3fd7403d11505a7b0da9855621ecb8b3e46abc1b380a5c804dcc2ffa76a6ee05a0912b1d5dff405f19357ca6a2569abcbd9a5c
  languageName: node
  linkType: hard

"trim@npm:0.0.1":
  version: 0.0.1
  resolution: "trim@npm:0.0.1"
  checksum: 10/2b4646dff99a222e8e1526edd4e3a43bbd925af0b8e837c340455d250157e7deefaa4da49bb891ab841e5c27b1afc5e9e32d4b57afb875d2dfcabf4e319b8f7f
  languageName: node
  linkType: hard

"ts-api-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "ts-api-utils@npm:2.1.0"
  peerDependencies:
    typescript: ">=4.8.4"
  checksum: 10/02e55b49d9617c6eebf8aadfa08d3ca03ca0cd2f0586ad34117fdfc7aa3cd25d95051843fde9df86665ad907f99baed179e7a117b11021417f379e4d2614eacd
  languageName: node
  linkType: hard

"tslib@npm:^2.8.1":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10/3e2e043d5c2316461cb54e5c7fe02c30ef6dccb3384717ca22ae5c6b5bc95232a6241df19c622d9c73b809bea33b187f6dbc73030963e29950c2141bc32a79f7
  languageName: node
  linkType: hard

"tsx@npm:^4.19.4":
  version: 4.19.4
  resolution: "tsx@npm:4.19.4"
  dependencies:
    esbuild: "npm:~0.25.0"
    fsevents: "npm:~2.3.3"
    get-tsconfig: "npm:^4.7.5"
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    tsx: dist/cli.mjs
  checksum: 10/4dde315aeda70b9cadfecbc8d05b1625f5831018b9cb2db25cbbd03c5f5ee9c59cdc6652a0fd8492176b50944a5af1d5af352b944d024f4a719f58d6f2ac3a7f
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10/14687776479d048e3c1dbfe58a2409e00367810d6960c0f619b33793271ff2a27f81b52461f14a162f1f89a9b1d8da1b237fc7c99b0e1fdcec28ec63a86b1fec
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10/3fb91f0735fb413b2bbaaca9fabe7b8fc14a3fa5a5a7546bab8a57e755be0e3788d893195ad9c2b842620592de0e68d4c077d4c2c41f04ec25b8b5bb82fa9a80
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10/269dad101dda73e3110117a9b84db86f0b5c07dad3a9418116fd38d580cab7fc628a4fc167e29b6d7c39da2f53374b78e7cb578b3c5ec7a556689d985d193519
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.15"
    reflect.getprototypeof: "npm:^1.0.9"
  checksum: 10/c2869aa584cdae24ecfd282f20a0f556b13a49a9d5bca1713370bb3c89dff0ccbc5ceb45cb5b784c98f4579e5e3e2a07e438c3a5b8294583e2bd4abbd5104fb5
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.7"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.13"
    possible-typed-array-names: "npm:^1.0.0"
    reflect.getprototypeof: "npm:^1.0.6"
  checksum: 10/d6b2f0e81161682d2726eb92b1dc2b0890890f9930f33f9bcf6fc7272895ce66bc368066d273e6677776de167608adc53fcf81f1be39a146d64b630edbf2081c
  languageName: node
  linkType: hard

"typedarray.prototype.slice@npm:^1.0.5":
  version: 1.0.5
  resolution: "typedarray.prototype.slice@npm:1.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    math-intrinsics: "npm:^1.1.0"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
  checksum: 10/df1a35ccb86bd30280310d628d6def7a9a78fe3c787fe63b67559473ffe5cb0d3b6351f38a1105cd9f15c6d3707420ca60720bfffe41f79e376071f6a9c8104f
  languageName: node
  linkType: hard

"typescript-eslint@npm:^8.32.1":
  version: 8.32.1
  resolution: "typescript-eslint@npm:8.32.1"
  dependencies:
    "@typescript-eslint/eslint-plugin": "npm:8.32.1"
    "@typescript-eslint/parser": "npm:8.32.1"
    "@typescript-eslint/utils": "npm:8.32.1"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10/4a042815f73825b510954579938a26c8664c2134e9663b0ac6f771f5f7338d4d41d78e5ded685ab42c1c6410117d955bac2c01fcc7e8ad3ef06c5e7dc2c95a7d
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-bigints: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    which-boxed-primitive: "npm:^1.1.1"
  checksum: 10/fadb347020f66b2c8aeacf8b9a79826fa34cc5e5457af4eb0bbc4e79bd87fed0fa795949825df534320f7c13f199259516ad30abc55a6e7b91d8d996ca069e50
  languageName: node
  linkType: hard

"underscore.string@npm:~2.4.0":
  version: 2.4.0
  resolution: "underscore.string@npm:2.4.0"
  checksum: 10/573febd70e44d9aaa9c1a8d90b69f3287ad64af21a940e96527d0b2ab84ba26863f3f2eeae198702b7182409790207ba02cbc11cae374619d75a3d8676c5bcd9
  languageName: node
  linkType: hard

"underscore@npm:>1.4.4":
  version: 1.13.7
  resolution: "underscore@npm:1.13.7"
  checksum: 10/1ce3368dbe73d1e99678fa5d341a9682bd27316032ad2de7883901918f0f5d50e80320ccc543f53c1862ab057a818abc560462b5f83578afe2dd8dd7f779766c
  languageName: node
  linkType: hard

"underscore@npm:~1.7.0":
  version: 1.7.0
  resolution: "underscore@npm:1.7.0"
  checksum: 10/4b0bb1a30631a4edd64ab1349d3e676ce54cb6dfae50d865a80696418d4e77b689b58dbf5f67e2d336030c4f191e014a1cc8c98d069aee8330358898ce19ab7e
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: 10/ec8f41aa4359d50f9b59fa61fe3efce3477cc681908c8f84354d8567bb3701fafdddf36ef6bff307024d3feb42c837cf6f670314ba37fc8145e219560e473d14
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10/6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10/beafdf3d6f44990e0a5ce560f8f881b4ee811be70b6ba0db25298c31c8cf525ed963572b48cd03be1c1349084f9e339be4241666d7cf1ebdad20598d3c652b27
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10/b271ca7e3d46b7160222e3afa3e531505161c9a4e097febae9664e4b59912f4cbe94861361a4175edac3a03fee99d91e44b6a58c17a634bc5a664b19fc76fbcb
  languageName: node
  linkType: hard

"v8-to-istanbul@npm:^9.0.0":
  version: 9.3.0
  resolution: "v8-to-istanbul@npm:9.3.0"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.12"
    "@types/istanbul-lib-coverage": "npm:^2.0.1"
    convert-source-map: "npm:^2.0.0"
  checksum: 10/fb1d70f1176cb9dc46cabbb3fd5c52c8f3e8738b61877b6e7266029aed0870b04140e3f9f4550ac32aebcfe1d0f38b0bac57e1e8fb97d68fec82f2b416148166
  languageName: node
  linkType: hard

"validator@npm:^13.15.0":
  version: 13.15.0
  resolution: "validator@npm:13.15.0"
  checksum: 10/cc4435e37a72d65c01d0a90d0f65da1fe09a805de35be1ec37e5379e1a612ab280277d7034a5369cd491a4f94c3b551f75dc4851090efafedf529a1ef76b1e0f
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: "npm:^1.1.0"
    is-boolean-object: "npm:^1.2.1"
    is-number-object: "npm:^1.1.1"
    is-string: "npm:^1.1.1"
    is-symbol: "npm:^1.1.1"
  checksum: 10/a877c0667bc089518c83ad4d845cf8296b03efe3565c1de1940c646e00a2a1ae9ed8a185bcfa27cbf352de7906f0616d83b9d2f19ca500ee02a551fb5cf40740
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    function.prototype.name: "npm:^1.1.6"
    has-tostringtag: "npm:^1.0.2"
    is-async-function: "npm:^2.0.0"
    is-date-object: "npm:^1.1.0"
    is-finalizationregistry: "npm:^1.1.0"
    is-generator-function: "npm:^1.0.10"
    is-regex: "npm:^1.2.1"
    is-weakref: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    which-boxed-primitive: "npm:^1.1.0"
    which-collection: "npm:^1.0.2"
    which-typed-array: "npm:^1.1.16"
  checksum: 10/22c81c5cb7a896c5171742cd30c90d992ff13fb1ea7693e6cf80af077791613fb3f89aa9b4b7f890bd47b6ce09c6322c409932359580a2a2a54057f7b52d1cbe
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: "npm:^2.0.3"
    is-set: "npm:^2.0.3"
    is-weakmap: "npm:^2.0.2"
    is-weakset: "npm:^2.0.3"
  checksum: 10/674bf659b9bcfe4055f08634b48a8588e879161b9fefed57e9ec4ff5601e4d50a05ccd76cf10f698ef5873784e5df3223336d56c7ce88e13bcf52ebe582fc8d7
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.18, which-typed-array@npm:^1.1.19":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    for-each: "npm:^0.3.5"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/12be30fb88567f9863186bee1777f11bea09dd59ed8b3ce4afa7dd5cade75e2f4cc56191a2da165113cc7cf79987ba021dac1e22b5b62aa7e5c56949f2469a68
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10/4782f8a1d6b8fc12c65e968fea49f59752bf6302dc43036c3bf87da718a80710f61a062516e9764c70008b487929a73546125570acea95c5b5dcc8ac3052c70f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10/6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10/1ec6f6089f205f83037be10d0c4b34c9183b0b63fca0834a5b3cee55dd321429d73d40bb44c8fc8471b5203d6e8f8275717f49a8ff4b2b0ab41d7e1b563e0854
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10/cebdaeca3a6880da410f75209e68cd05428580de5ad24535f22696d7d9cab134d1f8498599f344c3cf0fb37c1715807a183778d8c648d6cc0cb5ff2bb4236540
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7b1e4b35e9bb2312d2ee9ee7dc95b8cb5f8b4b5a89f7dde5543fe66c1e3715663094defa50d75454ac900bd210f702d575f15f3f17fa9ec0291806d2578d1ddf
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10/5f1b5f95e3775de4514edbb142398a2c37849ccfaf04a015be5d75521e9629d3be29bd4432d23c57f37e5b61ade592fb0197022e9993f81a06a5afbdcda9346d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10/4cb02b42b8a93b5cf50caf5d8e9beb409400a8a4d85e83bb0685c1457e9ac0b7a00819e9f5991ac25ffabb56a78e2f017c1acc010b3a1babfe6de690ba531abd
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10/1884d272d485845ad04759a255c71775db0fac56308764b4c77ea56a20d56679fad340213054c8c9c9c26fcfd4c4b2a90df993b7e0aaf3cdb73c618d1d1a802a
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10/9dc2c217ea3bf8d858041252d43e074f7166b53f3d010a8c711275e09cd3d62a002969a39858b92bbda2a6a63a585c7127014534a560b9c69ed2d923d113406e
  languageName: node
  linkType: hard

"yargs@npm:^17.7.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: "npm:^8.0.1"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.3"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^21.1.1"
  checksum: 10/abb3e37678d6e38ea85485ed86ebe0d1e3464c640d7d9069805ea0da12f69d5a32df8e5625e370f9c96dd1c2dc088ab2d0a4dd32af18222ef3c4224a19471576
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10/f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard
