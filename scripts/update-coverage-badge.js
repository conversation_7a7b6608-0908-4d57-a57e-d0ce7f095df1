#!/usr/bin/env node

/**
 * This script updates the coverage badge in the README.md file
 * based on the latest coverage report.
 * 
 * Usage: node scripts/update-coverage-badge.js
 */

const fs = require('fs');
const path = require('path');

// Path to README.md
const readmePath = path.join(__dirname, '..', 'README.md');

// Function to extract coverage from c8 output
async function getCoverage() {
  try {
    // Run c8 report to get coverage data
    const { execSync } = require('child_process');
    const output = execSync('npx c8 report --reporter=text-summary').toString();
    
    // Extract line coverage percentage
    const match = output.match(/Lines\s*:\s*([\d.]+)%/i);
    if (match && match[1]) {
      return Math.round(parseFloat(match[1]));
    }
    
    console.error('Could not extract coverage percentage from c8 output');
    return null;
  } catch (error) {
    console.error('Error getting coverage:', error.message);
    return null;
  }
}

// Function to update the badge in README.md
async function updateBadge(coverage) {
  if (coverage === null) {
    console.error('No coverage data available');
    return;
  }
  
  try {
    // Read README.md
    let readme = fs.readFileSync(readmePath, 'utf8');
    
    // Determine badge color based on coverage
    let color = 'red';
    if (coverage >= 90) {
      color = 'brightgreen';
    } else if (coverage >= 80) {
      color = 'green';
    } else if (coverage >= 70) {
      color = 'yellowgreen';
    } else if (coverage >= 60) {
      color = 'yellow';
    } else if (coverage >= 50) {
      color = 'orange';
    }
    
    // Create new badge URL
    const newBadge = `[![Coverage](https://img.shields.io/badge/coverage-${coverage}%25-${color}.svg)]()`;
    
    // Replace existing badge
    const badgeRegex = /\[\!\[Coverage\]\(https:\/\/img\.shields\.io\/badge\/coverage-\d+%25-[a-z]+\.svg\)\]\(\)/;
    const updatedReadme = readme.replace(badgeRegex, newBadge);
    
    // Write updated README.md
    fs.writeFileSync(readmePath, updatedReadme, 'utf8');
    
    console.log(`Updated coverage badge to ${coverage}%`);
  } catch (error) {
    console.error('Error updating badge:', error.message);
  }
}

// Main function
async function main() {
  const coverage = await getCoverage();
  await updateBadge(coverage);
}

main();
