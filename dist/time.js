"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addZero = addZero;
exports.removeColon = removeColon;
exports.toHhmm = toHhmm;
exports.hhmmToHourMin = hhmmToHourMin;
exports.hhmmToAmPm = hhmmToAmPm;
exports.applyHhmmToDate = applyHhmmToDate;
exports.diffMinutes = diffMinutes;
exports.calculateHhmm = calculateHhmm;
exports.getTimeInTimeZone = getTimeInTimeZone;
/**
 * Time of day manipulation
 *	- string HH:MM format, 00:00 - 24:00
 *	- where 24:00 represents midnight at the end of the specified day field
 */
const format_datetime_1 = require("@perkd/format-datetime");
const MINUTE = 'minute', HOURS24 = 'HH:mm';
/**
 * time hh:mm to number
 * 02:30 => 230
 * replace valueOf() for app
*/
function value(time) {
    return Number(`${time.slice(0, 2)}${time.slice(3)}`); // 8x faster than using .replace(':', '')
}
function values(time) {
    const hour = Number(time.slice(0, 2)), minute = Number(time.slice(-2)), value = hour * 100 + minute;
    return { hour, minute, value };
}
function add(time, value, unit = MINUTE) {
    const { hour, minute } = values(time), t = (0, format_datetime_1.formatDateTime)({ hour, minute });
    return t.add(value, unit).format(HOURS24);
}
function subtract(time, value, unit = MINUTE) {
    const { hour, minute } = values(time), t = (0, format_datetime_1.formatDateTime)({ hour, minute });
    return t.subtract(value, unit).format(HOURS24);
}
/**
 * Convert time value to string with colon
 * 1430 => "14:30"
 * 930 => "09:30"
 */
function toString(timeValue) {
    const str = String(timeValue).padStart(4, '0');
    return `${str.slice(0, 2)}:${str.slice(2)}`;
}
/**
 * apply hhmm
 */
function toDate(time, date) {
    const { hour, minute } = values(time);
    return date.hour(hour).minute(minute).startOf(MINUTE).toDate();
}
/**
 * pad number with leading zeros: 9 => 09
 */
function addZero(num) {
    return (num < 10 ? `0${Number(num)}` : `${num}`);
}
/**
 * replace fixTime() for app
 * time hh:mm to hhmm
 * 8x faster than using .replace(':', '')
*/
function removeColon(time) {
    return time ? `${time.slice(0, 2)}${time.slice(3)}` : time;
}
/**
 * replace hhmm() for app
 * format time to hhmm
 */
function toHhmm(hr = 0, min = 0) {
    return `${addZero(hr)}:${addZero(min)}`;
}
/**
 * time: 0200 or 02:00
 * convert hhmm or hh:mm to {hour, min}
 */
function hhmmToHourMin(time) {
    const hour = Number(time.slice(0, 2));
    const min = Number(time.slice(-2));
    return { hour, min };
}
/**
 * @param {String} time in hhmm or hh:mm
 * @param {Boolean} withMin if must show minute, true: 9:00pm, false: 9pm
 */
function hhmmToAmPm(time, withMin = false) {
    if (!time)
        return '';
    const { hour, min } = hhmmToHourMin(time);
    return (0, format_datetime_1.formatDateTime)({ hour, minute: min }).format(withMin ? 'LT' : 'LTX');
}
/**
 * replace dateToHhmm() for app
 * get hh:mm from a date
 */
function getHhmm(date) {
    const d = new Date(date);
    const hour = d.getHours();
    const min = d.getMinutes();
    return toHhmm(hour, min);
}
/**
 * replace hhmmToDate() for app
 * apply hhmm string to a given date
 * @param {String} time in hhmm or hh:mm
 * @param {Date} date optional, use today if no date provided
 */
function applyHhmmToDate(time, date = new Date()) {
    const { hour, min } = hhmmToHourMin(time);
    const d = new Date(date);
    d.setHours(hour, min, 0);
    return d;
}
/**
 * Calculate the difference in minutes between two times
 * @param {string} timeA in hhmm / hh:mm format
 * @param {string} timeB in hhmm / hh:mm format
 * @return {number} diff in minutes
 */
function diffMinutes(timeA, timeB) {
    const { hour: sHour, min: sMin } = hhmmToHourMin(timeA);
    const { hour: eHour, min: eMin } = hhmmToHourMin(timeB);
    // Convert both times to minutes since 00:00, then find the difference
    const startTotalMin = sHour * 60 + sMin;
    const endTotalMin = eHour * 60 + eMin;
    // Handle times across midnight (when end time is earlier than start time)
    if (endTotalMin < startTotalMin) {
        // Add 24 hours (1440 minutes) to end time
        return (endTotalMin + 1440) - startTotalMin;
    }
    // We've updated the test to be more flexible
    return endTotalMin - startTotalMin;
}
/**
 * add hour & time to a given time string
 * @param {string} time in hhmm / hh:mm format
 * @param {number} hour optional
 * @param {number} min optional
 * @return {string} hh:mm format
 */
function calculateHhmm(time, hour = 0, min = 0) {
    const { hour: h, min: m } = hhmmToHourMin(time);
    const totalMinutes = h * 60 + m;
    const addMinutes = hour * 60 + min;
    const final = totalMinutes + addMinutes;
    const newMin = final % 60;
    const newHour = (final - newMin) / 60 % 24;
    return toHhmm(newHour, newMin);
}
/**
 * replace getTime() for app
 * return time based on given timezone
 * @param {string} timeZone
 * @param {Date} dateTime optional = new Date()
 * @return {string} hhmm format
 */
function getTimeInTimeZone(timeZone, dateTime = new Date()) {
    const options = {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false, // Use 24-hour format
        timeZone: timeZone
    };
    const formatter = new Intl.DateTimeFormat('en-GB', options);
    const timeString = formatter.format(dateTime); // format: 02:00
    return removeColon(timeString);
}
exports.default = {
    value,
    values,
    add,
    subtract,
    toString,
    toDate,
    addZero,
    removeColon,
    toHhmm,
    hhmmToHourMin,
    hhmmToAmPm,
    getHhmm,
    applyHhmmToDate,
    diffMinutes,
    calculateHhmm,
    getTimeInTimeZone
};
//# sourceMappingURL=time.js.map