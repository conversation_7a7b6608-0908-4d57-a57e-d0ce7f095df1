declare function mark(marker?: string): bigint;
declare function get(marker: string): bigint;
declare function elapsedTime(start: string, end: string, verbose: boolean): number | undefined;
declare function diff(start: bigint, end?: bigint): number;
declare function time(date: Date): string;
declare function now(): number;
declare function max(marker: string): number;
declare function min(marker: string): number;
declare function reset(): void;
export declare const bm: {
    mark: typeof mark;
    get: typeof get;
    elapsedTime: typeof elapsedTime;
    diff: typeof diff;
    time: typeof time;
    now: typeof now;
    max: typeof max;
    min: typeof min;
    reset: typeof reset;
};
export {};
