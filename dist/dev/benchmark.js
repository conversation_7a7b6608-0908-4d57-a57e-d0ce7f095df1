"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bm = void 0;
let log = {}, // stores history of all markers, identical named markers will override previous value
maximum = {}, minimum = {};
function mark(marker) {
    const ts = process.hrtime.bigint(); // current high-resolution real time in a [seconds, nanoseconds]
    if (marker)
        log[marker] = ts;
    return ts;
}
function get(marker) {
    return log[marker];
}
function elapsedTime(start, end, verbose) {
    if (typeof end === 'boolean' || (end === undefined && verbose === undefined)) {
        verbose = (typeof end === 'boolean') ? end : verbose;
        end = start + '.end';
        start += '.start';
    }
    if (log[start] && log[end]) {
        const elapsed = diff(log[start], log[end]);
        maximum[start] = maximum[start] ? Math.max(maximum[start], elapsed) : elapsed;
        minimum[start] = minimum[start] ? Math.min(minimum[start], elapsed) : elapsed;
        if (verbose) {
            console.log('>>> Benchmark: ' + start + '->' + end + ':  \t' + elapsed + ' ms');
        }
        return elapsed;
    }
    return -1;
}
function diff(start, end) {
    end = end || process.hrtime.bigint();
    return Math.floor((Number(end - start) / 1000000) * 1000) / 1000; // return in milliseconds
}
function time(date) {
    date = date || new Date();
    return date.toTimeString().substring(0, 8);
}
function now() {
    return Date.now();
}
function max(marker) {
    return maximum[marker];
}
function min(marker) {
    return minimum[marker];
}
function reset() {
    log = {};
    maximum = {};
    minimum = {};
}
exports.bm = {
    mark, get, elapsedTime, diff, time, now, max, min, reset
};
//# sourceMappingURL=benchmark.js.map