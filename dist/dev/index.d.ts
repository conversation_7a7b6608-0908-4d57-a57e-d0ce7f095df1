export * from './benchmark';
declare const _default: {
    bm: {
        mark: (marker?: string) => bigint;
        get: (marker: string) => bigint;
        elapsedTime: (start: string, end: string, verbose: boolean) => number | undefined;
        diff: (start: bigint, end?: bigint) => number;
        time: (date: Date) => string;
        now: () => number;
        max: (marker: string) => number;
        min: (marker: string) => number;
        reset: () => void;
    };
};
export default _default;
