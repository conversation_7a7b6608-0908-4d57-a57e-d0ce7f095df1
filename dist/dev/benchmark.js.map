{"version": 3, "file": "benchmark.js", "sourceRoot": "", "sources": ["../../src/dev/benchmark.ts"], "names": [], "mappings": ";;;AAIA,IAAI,GAAG,GAAe,EAAE,EAAG,sFAAsF;AAChH,OAAO,GAAS,EAAE,EAClB,OAAO,GAAS,EAAE,CAAA;AAEnB,SAAS,IAAI,CAAC,MAAe;IAC5B,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,CAAE,gEAAgE;IACpG,IAAI,MAAM;QAAE,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAA;IAC5B,OAAO,EAAE,CAAA;AACV,CAAC;AAED,SAAS,GAAG,CAAC,MAAc;IAC1B,OAAO,GAAG,CAAC,MAAM,CAAC,CAAA;AACnB,CAAC;AAED,SAAS,WAAW,CAAE,KAAa,EAAE,GAAW,EAAE,OAAgB;IACjE,IAAI,OAAO,GAAG,KAAK,SAAS,IAAI,CAAC,GAAG,KAAK,SAAS,IAAI,OAAO,KAAK,SAAS,CAAC,EAAE,CAAC;QAC9E,OAAO,GAAG,CAAC,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAA;QACpD,GAAG,GAAG,KAAK,GAAG,MAAM,CAAA;QACpB,KAAK,IAAI,QAAQ,CAAA;IAClB,CAAC;IAED,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;QAE1C,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;QAC7E,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;QAE7E,IAAI,OAAO,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,iBAAiB,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,OAAO,GAAG,OAAO,GAAG,KAAK,CAAC,CAAA;QAChF,CAAC;QACD,OAAO,OAAO,CAAA;IACf,CAAC;IACD,OAAO,CAAC,CAAC,CAAA;AACV,CAAC;AAED,SAAS,IAAI,CAAC,KAAa,EAAE,GAAY;IACxC,GAAG,GAAG,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA;IACpC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAA,CAAC,yBAAyB;AAC3F,CAAC;AAED,SAAS,IAAI,CAAC,IAAU;IACvB,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE,CAAA;IACzB,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAC3C,CAAC;AAED,SAAS,GAAG;IACX,OAAO,IAAI,CAAC,GAAG,EAAE,CAAA;AAClB,CAAC;AAED,SAAS,GAAG,CAAC,MAAc;IAC1B,OAAO,OAAO,CAAC,MAAM,CAAC,CAAA;AACvB,CAAC;AAED,SAAS,GAAG,CAAC,MAAc;IAC1B,OAAO,OAAO,CAAC,MAAM,CAAC,CAAA;AACvB,CAAC;AAED,SAAS,KAAK;IACb,GAAG,GAAG,EAAE,CAAA;IACR,OAAO,GAAG,EAAE,CAAA;IACZ,OAAO,GAAG,EAAE,CAAA;AACb,CAAC;AAEY,QAAA,EAAE,GAAG;IACjB,IAAI,EAAE,GAAG,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK;CACxD,CAAA"}