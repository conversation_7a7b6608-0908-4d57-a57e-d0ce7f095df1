/**
 * Return the best match language
 * @param preferred languages ['zh', 'en'...]
 * @param supported ['zh-Hant', 'en']
 * @param fallback default language, 'zh-Hans'
 */
export declare function bestLanguage(preferred: string[] | undefined, supported: string[], fallback?: string): string | null;
/**
 * Return the best match content
 * @param preferred languages
 * @param t - globalize.t
 * @param fallback default language
 */
export declare function localizeContent(preferred: string[], t: any, fallback: string): any;
/**
 * Maps language code from app (locale in install header) to code used by globalize
 */
export declare function languageOf(locale?: any): any;
export declare function mergeGlobalize(obj: any, language: string): any;
declare const _default: {
    bestLanguage: typeof bestLanguage;
    localizeContent: typeof localizeContent;
    languageOf: typeof languageOf;
    mergeGlobalize: typeof mergeGlobalize;
};
export default _default;
