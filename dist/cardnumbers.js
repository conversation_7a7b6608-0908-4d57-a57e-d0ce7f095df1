"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cardNumberFromBarcode = cardNumberFromBarcode;
exports.isValidCardNumber = isValidCardNumber;
exports.cleanseCardNumber = cleanseCardNumber;
/**
 * Use regex to retrieve Card number from barcode
 * @param barcode
 * @param patterns e.g. [ "/(?!CARD\\|2[0-1]\\|)\\d{8,10}/g" ]
 * @return card number
 */
function cardNumberFromBarcode(barcode, patterns) {
    let cardNumber = barcode;
    for (let i = 0; i < patterns.length; i += 1) {
        const regex = getRegex(patterns[i]), res = barcode.match(regex);
        if (res && res[0]) {
            [cardNumber] = res;
            break;
        }
    }
    return cardNumber;
}
/**
 * Use regex to validate Card number format
 * @param cardNumber
 * @param patterns e.g. [ "/\\d{8,10}/g" ]
 * @return valid
 */
function isValidCardNumber(cardNumber, patterns) {
    let valid = false;
    for (let i = 0; i < patterns.length; i += 1) {
        const regex = getRegex(patterns[i]), res = cardNumber.match(regex);
        if (res?.[0]) {
            valid = true;
            break;
        }
    }
    return valid;
}
/**
 * Remove spaces & symbols in cardNumber
 */
function cleanseCardNumber(cardNumber) {
    return typeof cardNumber === 'string'
        ? cardNumber.replace(/[^\w\d]/gi, '')
        : cardNumber;
}
// -----  Private Functions  -----
function getRegex(str) {
    const lastSlashIndex = str.lastIndexOf('/');
    // handle regex with flag
    if (str[0] === '/' && lastSlashIndex !== -1 && lastSlashIndex !== 0) {
        const flag = str.substring(lastSlashIndex + 1, str.length), regexStr = str.substring(1, lastSlashIndex);
        return flag ? new RegExp(regexStr, flag) : new RegExp(regexStr);
    }
    return new RegExp(str);
}
exports.default = {
    cardNumberFromBarcode,
    isValidCardNumber,
    cleanseCardNumber,
};
//# sourceMappingURL=cardnumbers.js.map