"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.intersection = intersection;
exports.union = union;
exports.difference = difference;
exports.symmetricDifference = symmetricDifference;
function intersection(sets) {
    if (sets.length <= 1)
        return sets[0];
    const set = new Set();
    sets.sort((a, b) => a.size - b.size);
    sets[0].forEach(elem => {
        let allMatch = true;
        for (let i = 1; i < sets.length; i++) {
            if (!sets[`${i}`].has(elem)) {
                allMatch = false;
                break;
            }
        }
        if (allMatch)
            set.add(elem);
    });
    return set;
}
function union(sets) {
    const set = new Set();
    sets.forEach(aSet => {
        aSet.forEach(elem => set.add(elem));
    });
    return set;
}
function difference(a, sets) {
    const set = new Set(a);
    for (let i = 0; i < sets.length; i++) {
        sets[`${i}`].forEach(elem => set.delete(elem));
    }
    return set;
}
function symmetricDifference(a, b) {
    const set = new Set(a);
    b.forEach(elem => {
        set.has(elem) ? set.delete(elem) : set.add(elem);
    });
    return set;
}
exports.default = {
    intersection,
    union,
    difference,
    symmetricDifference,
};
//# sourceMappingURL=sets.js.map