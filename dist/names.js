"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NAME_ORDER = exports.camelcase = void 0;
exports.unvowel = unvowel;
exports.formatName = formatName;
exports.splitIntoWords = splitIntoWords;
exports.getName = getName;
exports.detectLanguage = detectLanguage;
exports.nameConnector = nameConnector;
exports.deriveNameOrder = deriveNameOrder;
exports.validateName = validateName;
exports.cleanseName = cleanseName;
const tslib_1 = require("tslib");
const franc_1 = tslib_1.__importDefault(require("franc"));
var camelcase_1 = require("camelcase");
Object.defineProperty(exports, "camelcase", { enumerable: true, get: function () { return tslib_1.__importDefault(camelcase_1).default; } });
var NAME_ORDER;
(function (NAME_ORDER) {
    NAME_ORDER["fg"] = "familygiven";
    NAME_ORDER["gf"] = "givenfamily";
    NAME_ORDER["g"] = "given";
    NAME_ORDER["f"] = "family";
    NAME_ORDER["a"] = "alias";
    NAME_ORDER["af"] = "aliasfamily";
    NAME_ORDER["fga"] = "familygivenalias";
    NAME_ORDER["afg"] = "aliasfamilygiven";
    NAME_ORDER["gfa"] = "givenfamilyalias";
})(NAME_ORDER || (exports.NAME_ORDER = NAME_ORDER = {}));
const CHINESE = 'cmn', JAPANESE = 'jpn', KOREAN = 'kor', 
// NON_ALPHABETIC = [CHINESE, JAPANESE, KOREAN],
SUPPORTED = ['eng', 'cmn', 'ind', 'jpn', 'kor', 'mal', 'ben', 'hin', 'tam', 'vie', 'tha', 'mya', 'spa', 'rus', 'por', 'fra', 'deu', 'ita', 'tur', 'pol', 'nld', 'swe', 'fin', 'dan', 'nob', 'arb', 'heb'], NAME = {
    fg: 'familygiven',
    gf: 'givenfamily',
    g: 'given',
    f: 'family',
    a: 'alias',
    af: 'aliasfamily',
    fga: 'familygivenalias',
    afg: 'aliasfamilygiven',
    gfa: 'givenfamilyalias',
}, INVALID_NAME_PATTERN = /(^[\s\d!"#$%&'()*+,-./:;<=>?@[\\。，？！\]^_`{|}~]+)|([\s\d!"#$%&'()*+,-./:;<=>?@[\\。，？！\]^_`{|}~]+$)/g; // punctuations, numbers and spaces (at the beginning or end)
function unvowel(name) {
    return name.replace(/[aeiou\s]/gi, '');
}
function capitalize(string) {
    return string.replace(/(^|[^a-zA-Z\u00C0-\u017F])([a-zA-Z\u00C0-\u017F])/g, m => m.toUpperCase());
}
function formatName(name = '') {
    return capitalize(name.toLowerCase());
}
function splitIntoWords(text) {
    const regex = /\b[^\s]+\b(?:\.)?/g, matches = [];
    let result;
    while ((result = regex.exec(text)) !== null) {
        matches.push(result[0].toLowerCase());
    }
    return matches;
}
function getName(profile = {}, nameOrder = '', separator = ' ') {
    const { fg, gf, g, f, a, af, fga, afg, gfa } = NAME, { familyName = '', givenName = '', alias = '' } = profile, format = (first, last, sep) => first && last ? `${first}${sep}${last}` : first ?? last;
    if (!nameOrder)
        return '';
    if (nameOrder === g)
        return givenName;
    if (nameOrder === f)
        return familyName;
    if (nameOrder === a)
        return alias;
    if (nameOrder.includes(fg)) {
        const familyGiven = format(familyName, givenName, separator);
        if (nameOrder === fga)
            return format(familyGiven, alias, separator);
        if (nameOrder === afg)
            return format(alias, familyGiven, separator);
        return familyGiven;
    }
    if (nameOrder.includes(gf)) {
        const givenFamily = format(givenName, familyName, separator);
        return (nameOrder === gfa)
            ? format(givenFamily, alias, separator)
            : givenFamily;
    }
    if (nameOrder === af)
        return format(alias, familyName, separator);
    return '';
}
function detectLanguage(whitelist, str = '') {
    return (0, franc_1.default)(str, {
        minLength: Math.min(str.length, 20),
        whitelist: whitelist ?? SUPPORTED,
    });
}
function nameConnector(language) {
    let connector = ' ';
    switch (language) {
        case CHINESE:
        case JAPANESE:
        case KOREAN:
            connector = '';
    }
    return connector;
}
function deriveNameOrder(displayAs) {
    const { fg, gf } = NAME, map = {
        familygiven: fg,
        familygivenalias: fg,
        aliasfamilygiven: fg,
        givenfamilyalias: gf,
        givenfamily: gf,
    };
    return map[displayAs] || null;
}
function validateName(name) {
    return name ? !INVALID_NAME_PATTERN.test(name) : '';
}
function cleanseName(name) {
    return name ? name.replaceAll(INVALID_NAME_PATTERN, '') : '';
}
exports.default = {
    unvowel,
    formatName,
    splitIntoWords,
    getName,
    detectLanguage,
    nameConnector,
    deriveNameOrder,
    validateName,
    cleanseName
};
//# sourceMappingURL=names.js.map