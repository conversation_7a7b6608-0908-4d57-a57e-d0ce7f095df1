{"version": 3, "file": "objects.js", "sourceRoot": "", "sources": ["../src/objects.ts"], "names": [], "mappings": ";;;AAaA,gCAKC;AAOD,8BAEC;AAKD,8CAEC;AAOD,4BAKC;AAQD,4BAYC;AAQD,oBA4BC;AAOD,sBAsCC;AAQD,0BAUC;AAOD,gCASC;AASD,4BA6BC;AAaD,sCA0BC;AAED,8BAuBC;AAUD,gCA8CC;AAED,8CAUC;;AA/VD,kEAAgC;AAChC,wDAAuB;AACvB,qBAAqB;AACrB,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAA;AAC5B,uCAA4C;AAAnC,2HAAA,OAAO,OAAS;AACzB,uCAA+C;AAAtC,8HAAA,OAAO,OAAY;AAC5B,uCAA+C;AAAtC,8HAAA,OAAO,OAAY;AAE5B,MAAM,KAAK,GAAG,IAAA,cAAI,GAAE,CAAA;AAEpB;;GAEG;AACH,SAAgB,UAAU,CAAC,GAAQ;IAClC,IAAI,CAAC,GAAG;QAAE,OAAO,IAAI,CAAA;IACrB,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,GAAG,CAAA;IAC5C,KAAK,MAAM,GAAG,IAAI,IAAI;QAAE,IAAI,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS;YAAE,OAAO,KAAK,CAAA;IACtG,OAAO,IAAI,CAAA;AACZ,CAAC;AAED;;;;GAIG;AACH,SAAgB,SAAS,CAAC,GAAQ,EAAE,OAAa;IAChD,OAAO,KAAK,CAAC,GAAG,CAAC,CAAA;AAClB,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,GAAQ;IACzC,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAA;AAC/E,CAAC;AAED;;;;GAIG;AACH,SAAgB,QAAQ,CAAC,GAAQ,EAAE,GAAW;IAC7C,IAAI,OAAO,GAAG,KAAK,QAAQ;QAAE,OAAO,SAAS,CAAA;IAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;QAAE,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA,CAAC,QAAQ;IAC/F,IAAI,GAAG,CAAC,GAAG,CAAC;QAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAA;IAC7B,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA,CAAE,SAAS;AAC5F,CAAC;AAED;;;;;GAKG;AACH,SAAgB,QAAQ,CAAC,GAAQ,EAAE,KAAa;IAC/C,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI;QAAE,OAAO,IAAI,CAAA;IAExD,MAAM,YAAY,GAAG,IAAA,eAAO,EAAC,GAAG,CAAC,EAChC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,YAAuC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,CAAA;IAEhG,IAAI,KAAK,EAAE,CAAC;QACX,MAAM,CAAE,SAAS,EAAE,UAAU,CAAE,GAAG,KAAK,CAAA;QACvC,MAAM,WAAW,GAAG,EAAE,CAAC,SAAS,CAAC,EAAE,UAAU,EAAE,CAAA;QAC/C,OAAO,IAAA,iBAAS,EAAC,WAAW,CAAC,CAAA;IAC9B,CAAC;IACD,OAAO,EAAE,CAAA;AACV,CAAC;AAED;;;;;GAKG;AACH,SAAgB,IAAI,CAAC,OAAY,EAAE,GAAW,EAAE,OAAiB;IAChE,IAAI,OAAO,CAAC,aAAa,CAAC,KAAK,KAAK,EAAE,CAAC;QACtC,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,CAAA;IACxB,CAAC;IAED,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE,CAAC;QACvE,OAAO;YACN,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE;YACzB,CAAC,EAAE,QAAQ;SACX,CAAA;IACF,CAAC;IAED,MAAM,MAAM,GAAQ,EAAE,CAAA;IAEtB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QACpD,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,KAAe,CAAC,CAAC,CAAC,GAAG,CAAA;QACzC,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAe,CAAA;QAEzC,IAAI,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,qDAAqD;YACrD,MAAM,CAAC,CAAC,KAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACzD,CAAC;aAAM,CAAC;YACP,kDAAkD;YAClD,MAAM,CAAC,CAAC,CAAC,GAAG,IAAA,mBAAQ,EAAC,GAAG,EAAE,CAAC,CAAC,CAAA;QAC7B,CAAC;IACF,CAAC;IAED,OAAO,MAAM,CAAA;AACd,CAAC;AAED;;;;GAIG;AACH,SAAgB,KAAK,CAAC,GAAW,EAAE,IAAS;IAC3C,MAAM,MAAM,GAAQ,EAAE,CAAA;IAEtB,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;QACzB,IAAI,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAA;YAExB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC1B,MAAM,CAAE,GAAG,CAAE,GAAG,KAAK,CAAA;gBAErB,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;oBAC3C,yBAAyB;oBACzB,MAAM,MAAM,GAAG,IAAA,mBAAQ,EAAC,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;oBAC9C,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;gBAC7D,CAAC;qBACI,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;oBAC5C,wFAAwF;oBACxF,KAAK,MAAM,CAAC,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;wBACxB,MAAM,CAAC,GAAG,IAAA,mBAAQ,EAAC,GAAG,EAAE,CAAC,CAAC,CAAA;wBAC1B,IAAI,CAAC,EAAE,CAAC;4BACP,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;4BAChB,MAAK;wBACN,CAAC;oBACF,CAAC;gBACF,CAAC;qBACI,CAAC;oBACL,MAAM,CAAC,IAAI,CAAC,GAAG,IAAA,mBAAQ,EAAC,GAAG,EAAE,KAAK,CAAC,CAAA;gBACpC,CAAC;YACF,CAAC;iBACI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACpC,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;YACjC,CAAC;iBACI,CAAC;gBACL,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAA;YACrB,CAAC;QACF,CAAC;IACF,CAAC;IACD,OAAO,MAAM,CAAA;AACd,CAAC;AAED;;;;;EAKE;AACF,SAAgB,OAAO,CAAC,GAA4B,EAAE,aAAuB,EAAE;IAC9E,IAAI,CAAC,GAAG;QAAE,OAAO,GAAG,CAAA;IACpB,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAO,GAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,+BAA+B;IACnF,GAAG,GAA4B,EAAE,CAAA;IAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5C,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;QACzB,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;YAAE,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;IACnD,CAAC;IACD,OAAO,GAAG,CAAA;AACX,CAAC;AAED;;;;GAIG;AACH,SAAgB,UAAU,CAAC,IAAS,EAAE,UAAe,EAAE;IACtD,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EACjC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,IAAY,EAAE,EAAE;QAChD,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;QACzB,GAAG,CAAC,GAAG,CAAC,GAAG,IAAA,mBAAQ,EAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAC/B,OAAO,GAAG,CAAA;IACX,CAAC,EAAE,EAAE,CAAC,CAAA;IAEP,OAAO,MAAM,CAAA;AACd,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,QAAQ,CAAC,SAAiB,EAAE,EAAE,SAA0B,EAAE,IAAc;IACvF,MAAM,KAAK,GAAoB,SAAS,EACvC,MAAM,GAAwB,EAAE,CAAA;IAEjC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,IAAA,mBAAQ,EAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAC/B,CAAC;IAED,KAAK,MAAM,CAAE,GAAG,EAAE,IAAI,CAAE,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACnD,IAAI,CAAC,MAAM,EAAO,IAAI,EAAE,GAAG,CAAC,CAAA;IAC7B,CAAC;IAED,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAA,iBAAS,EAAC,MAAM,CAAC,CAAA;IAExC,SAAS,IAAI,CAAC,MAAW,EAAE,IAA8B,EAAE,GAAW,EAAE,YAAkB;QACzF,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QACpC,CAAC;aACI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAA,mBAAQ,EAAC,MAAM,EAAE,IAAI,IAAI,GAAG,CAAC,CAAA;YAC3C,IAAI,SAAS,KAAK,MAAM,CAAC,GAAG,CAAC;gBAAE,MAAM,CAAC,GAAG,CAAC,GAAG,YAAY,CAAA;QAC1D,CAAC;aACI,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;YACrC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC,CAAA;QACjD,CAAC;aACI,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YACxB,MAAM,CAAC,GAAG,CAAC,GAAG,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAA;QAC/D,CAAC;IACF,CAAC;AACF,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,aAAa,CAAC,IAA0B,EAAE,IAAa,EAAE,cAAuB,KAAK;IACpG,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;QAAE,OAAO,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAA;IAEvD,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAC/B,IAAI,WAAW;YAAE,OAAO,MAAM,CAAA;QAC9B,OAAO,MAAM,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC;IACD,MAAM,KAAK,GAAG,IAAA,mBAAQ,EAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAClC,IAAI,WAAW,EAAE,CAAC;QACjB,IAAI,CAAC,KAAK;YAAE,OAAO,EAAE,CAAA;QACrB,OAAO,CAAE,KAAK,CAAE,CAAA;IACjB,CAAC;IACD,OAAO,KAAK,CAAA;IAEZ,SAAS,IAAI,CAAC,MAA2B,EAAE,GAAW;QACrD,IAAI,GAAG,IAAI,MAAM;YAAE,OAAO,CAAE,MAAM,CAAC,GAAG,CAAC,CAAE,CAAA;QACzC,MAAM,MAAM,GAAG,EAAE,CAAA;QAEjB,KAAK,MAAM,QAAQ,IAAI,MAAM,EAAE,CAAC;YAC/B,IAAI,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC5B,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM;gBAC1E,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAA;QACvB,CAAC;QACD,OAAO,MAAM,CAAA;IACd,CAAC;AACF,CAAC;AAED,SAAgB,SAAS,CAAC,GAAW,EAAE,YAAkB,EAAE,IAAa;IACvE,4BAA4B;IAC5B,IAAI,GAAG,KAAK,cAAc,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;QAC/C,OAAO,CAAC,KAAK,CAAC,aAAa,IAAI,EAAE,EAAE;YAClC,GAAG,EAAE;gBACJ,OAAO,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE;aACpC;SACD,CAAC,CAAA;QACF,OAAO,KAAK,CAAA;IACb,CAAC;IAED,IAAI,GAAG,GAAG,IAAI,CAAA;IACd,IAAI,CAAC;QACJ,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IACtB,CAAC;IACD,OAAO,GAAQ,EAAE,CAAC;QACjB,GAAG,CAAC,OAAO,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE,CAAA;QACzC,IAAI,IAAI,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,aAAa,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAA;QAC5C,CAAC;QACD,OAAO,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAA;IACzD,CAAC;IACD,OAAO,GAAG,CAAA;AACX,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,UAAU,CAAC,QAAuC,EAAE,OAA4B,EAAE,EAAE,UAA+B,EAAE;IACpI,IAAI,CAAC,QAAQ;QAAE,OAAO,QAAQ,CAAA;IAE9B,OAAO,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,CAAA;IAEnE,MAAM,OAAO,GAAG,GAAI,YAAY,CAAC,OAAO,CAAC,MAAM,CAAE,QAAS,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EACvF,EAAE,GAAG,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,EAE7B,QAAQ,GAAG,OAAO,QAAQ,KAAK,QAAQ,EACvC,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;IAE9D,IAAI,MAAM,GAAG,IAAI,CAAA;IAEjB,KAAK,IAAI,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACpE,MAAM,CAAE,GAAG,EAAE,GAAG,CAAE,GAAG,OAAO,EAC3B,KAAK,GAAG,IAAA,mBAAQ,EAAC,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,CAAA;QAEpC,QAAQ,OAAO,KAAK,EAAE,CAAC;YACvB,KAAK,QAAQ;gBACZ,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAE,EAAE,CAAC,CAAA;gBAClE,MAAK;YAEN,KAAK,QAAQ;gBACZ,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA,CAAE,8CAA8C;gBACxG,MAAK;YAEN,KAAK,SAAS;gBACb,MAAM,GAAG,MAAM;qBACb,OAAO,CAAC,KAAK,GAAG,GAAG,EAAE,IAAI,KAAK,EAAE,CAAC;qBACjC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;gBAC7B,MAAK;YAEN;gBACC,MAAM,GAAG,MAAM;qBACb,OAAO,CAAC,IAAI,GAAG,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC;qBACnC,OAAO,CAAC,IAAI,GAAG,GAAG,EAAE,KAAK,CAAC;qBAC1B,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;gBACrB,MAAK;QACN,CAAC;IACF,CAAC;IAED,OAAO,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;IAEhE,SAAS,YAAY,CAAC,GAAW;QAChC,OAAO,GAAG,CAAC,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAA;IACvD,CAAC;AACF,CAAC;AAED,SAAgB,iBAAiB,CAAC,GAAQ,EAAE,IAAY;IACvD,IAAI,CAAC,IAAI;QAAE,OAAO,GAAG,CAAA;IAErB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAC3B,OAAO,GAAG,CAAC,MAAM,EAAE,CAAC;QACnB,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,CAAA;QACxB,IAAI,IAAI,KAAK,SAAS;YAAE,MAAK;QAC7B,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAA;IAChB,CAAC;IACD,OAAO,GAAG,CAAA;AACX,CAAC;AAED,gDAAgD;AACnC,QAAA,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;AACtB,QAAA,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;AAEvC,kBAAe;IACd,UAAU;IACV,SAAS;IACT,iBAAiB;IACjB,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,KAAK;IACL,OAAO;IACP,UAAU;IACV,QAAQ;IACR,aAAa;IACb,SAAS;IACT,UAAU;IACV,iBAAiB;IACjB,OAAO,EAAP,eAAO;IACP,SAAS,EAAT,iBAAS;CACT,CAAA"}