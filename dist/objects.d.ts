export { default as merge } from 'deepmerge';
export { default as getValue } from 'get-value';
export { default as setValue } from 'set-value';
/**
 * fastest way to test obj is empty
 */
export declare function isEmptyObj(obj: any): boolean;
/**
 * Deep clone object:	https://www.npmjs.com/package/rfdc
 * @param obj to clone
 * @param options not supported yet
 */
export declare function cloneDeep(obj: any, options?: any): any;
/**
 * return obj with empty (null | undefined) values removed
 */
export declare function removeEmptyValues(obj: any): any;
/**
 * Find value of key (first of breadth-first search)
 * @param {Object} obj
 * @param {String} key
 */
export declare function deepFind(obj: any, key: string): any;
/**
 * Find the path of a given value in an object
 * @param obj - The object to search
 * @param value - The value to find
 * @returns The path of the value, or an empty object if not found
 */
export declare function findPath(obj: any, value: string): any;
/**
 * Pick object properties
 * @param mapping - { [object path]: <property name> }, eg. { 'a.b.c': 'key' }
 * @param obj
 * @param reverse - reverse mapping
 */
export declare function pick(mapping: any, obj: object, reverse?: boolean): any;
/**
 * Pick object properties
 * @param obj
 * @param defn - { [object path]: <property name> }, eg. { 'a.b.c': 'key' }
 */
export declare function pick2(obj: object, defn: any): any;
/**
 * Creates an object composed of the picked (shallow) object properties
 * 15 keys, pick 5 spread out: 0.132ms  (757576 / sec)
 * @param obj source object
 * @param properties to shallow pick
*/
export declare function pickObj(obj: Record<string, unknown>, properties?: string[]): any;
/**
 * Pick and map object to object (hint: use cloneDeep after if necessary)
 * @param data
 * @param mapping - { [object path]: <property name> }, eg. { 'a.b.c': 'key' }
 */
export declare function pickAndMap(data: any, mapping?: any): any;
/**
 * Deep pick object
 *
 * var obj = { a: { b : { c: { d: 'foo' }}}, e: [{ f: 'g' }]};
 * var newObj = pickDeep(obj, { x: 'a.b.c', y: 'e.0.f' });
 * //=> { x: { d: 'foo' }, y: 'g' }
 */
export declare function pickDeep(object: object | undefined, pickProps: object | string, flat?: boolean): any;
/**
 * pickDeepValue
 *
 * var obj = { a: 1, b: {d: 3}, c: { d: 4} };
 * pickDeepValue(obj, 'a'); // 1
 * pickDeepValue(obj, 'd'); // 3
 * pickDeepValue(obj, 'b.d'); // 3
 * pickDeepValue(obj, 'a', true); // [1]
 * pickDeepValue(obj, 'b.d', true); // [3]
 * pickDeepValue(obj, 'd', true); // [3, 4]
 */
export declare function pickDeepValue(data?: Record<string, any>, prop?: string, multiValues?: boolean): any;
export declare function parseJSON(str: string, defaultValue?: any, name?: string): any;
/**
 * Substitute data in an object.
 * @ref https://www.npmjs.com/package/token-substitute
 * @param {Object|String} template: {'#{key1}: 'this is #{value1}', '#{key2.value2}': 'this is #{key2.value2}'};
 * @param {Object} data: {key1: value1, key2: value2}
 * @param {Object} [options]: default { prefix: '#{', suffix: '}', delimiter: '.' }
 * @return Substituted object
 */
export declare function substitute(template?: string | Record<string, any>, data?: Record<string, any>, options?: Record<string, any>): any;
export declare function getDescendantProp(obj: any, desc: string): any;
export declare const flatten: any;
export declare const unflatten: any;
declare const _default: {
    isEmptyObj: typeof isEmptyObj;
    cloneDeep: typeof cloneDeep;
    removeEmptyValues: typeof removeEmptyValues;
    deepFind: typeof deepFind;
    findPath: typeof findPath;
    pick: typeof pick;
    pick2: typeof pick2;
    pickObj: typeof pickObj;
    pickAndMap: typeof pickAndMap;
    pickDeep: typeof pickDeep;
    pickDeepValue: typeof pickDeepValue;
    parseJSON: typeof parseJSON;
    substitute: typeof substitute;
    getDescendantProp: typeof getDescendantProp;
    flatten: any;
    unflatten: any;
};
export default _default;
