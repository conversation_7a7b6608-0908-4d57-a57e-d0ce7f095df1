{"version": 3, "file": "names.js", "sourceRoot": "", "sources": ["../src/names.ts"], "names": [], "mappings": ";;;AAwCA,0BAEC;AAMD,gCAEC;AAED,wCASC;AAED,0BA8BC;AAED,wCAKC;AAED,sCASC;AAED,0CAWC;AAED,oCAEC;AAED,kCAEC;;AApID,0DAAyB;AAEzB,uCAAgD;AAAvC,+HAAA,OAAO,OAAa;AAQ7B,IAAY,UAUX;AAVD,WAAY,UAAU;IACrB,gCAAkB,CAAA;IAClB,gCAAkB,CAAA;IAClB,yBAAW,CAAA;IACX,0BAAY,CAAA;IACZ,yBAAW,CAAA;IACX,gCAAkB,CAAA;IAClB,sCAAwB,CAAA;IACxB,sCAAwB,CAAA;IACxB,sCAAwB,CAAA;AACzB,CAAC,EAVW,UAAU,0BAAV,UAAU,QAUrB;AAED,MAAM,OAAO,GAAG,KAAK,EACpB,QAAQ,GAAG,KAAK,EAChB,MAAM,GAAG,KAAK;AACd,gDAAgD;AAChD,SAAS,GAAG,CAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAE,EAC3M,IAAI,GAAG;IACN,EAAE,EAAE,aAAa;IACjB,EAAE,EAAE,aAAa;IACjB,CAAC,EAAE,OAAO;IACV,CAAC,EAAE,QAAQ;IACX,CAAC,EAAE,OAAO;IACV,EAAE,EAAE,aAAa;IACjB,GAAG,EAAE,kBAAkB;IACvB,GAAG,EAAE,kBAAkB;IACvB,GAAG,EAAE,kBAAkB;CACvB,EACD,oBAAoB,GAAG,oGAAoG,CAAA,CAAC,6DAA6D;AAE1L,SAAgB,OAAO,CAAE,IAAY;IACpC,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAA;AACvC,CAAC;AAED,SAAS,UAAU,CAAE,MAAc;IAClC,OAAO,MAAM,CAAC,OAAO,CAAC,oDAAoD,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAA;AAClG,CAAC;AAED,SAAgB,UAAU,CAAE,OAAe,EAAE;IAC5C,OAAO,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAA;AACtC,CAAC;AAED,SAAgB,cAAc,CAAE,IAAY;IAC3C,MAAM,KAAK,GAAG,oBAAoB,EACjC,OAAO,GAAG,EAAE,CAAA;IAEb,IAAI,MAAM,CAAA;IACV,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QAC7C,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAA;IACtC,CAAC;IACD,OAAO,OAAO,CAAA;AACf,CAAC;AAED,SAAgB,OAAO,CAAE,UAAiB,EAAE,EAAE,YAA2B,EAAE,EAAE,SAAS,GAAG,GAAG;IAC3F,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAClD,EAAE,UAAU,GAAG,EAAE,EAAE,SAAS,GAAG,EAAE,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,EACzD,MAAM,GAAG,CAAC,KAAa,EAAE,IAAY,EAAE,GAAW,EAAE,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAA;IAE/G,IAAI,CAAC,SAAS;QAAE,OAAO,EAAE,CAAA;IACzB,IAAI,SAAS,KAAK,CAAC;QAAE,OAAO,SAAS,CAAA;IACrC,IAAI,SAAS,KAAK,CAAC;QAAE,OAAO,UAAU,CAAA;IACtC,IAAI,SAAS,KAAK,CAAC;QAAE,OAAO,KAAK,CAAA;IAEjC,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;QAC5B,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;QAE5D,IAAI,SAAS,KAAK,GAAG;YAAE,OAAO,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC,CAAA;QACnE,IAAI,SAAS,KAAK,GAAG;YAAE,OAAO,MAAM,CAAC,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC,CAAA;QAEnE,OAAO,WAAW,CAAA;IACnB,CAAC;IAED,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;QAC5B,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,EAAE,UAAU,EAAE,SAAS,CAAC,CAAA;QAE5D,OAAO,CAAC,SAAS,KAAK,GAAG,CAAC;YACzB,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC;YACvC,CAAC,CAAC,WAAW,CAAA;IACf,CAAC;IAED,IAAI,SAAS,KAAK,EAAE;QAAE,OAAO,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,SAAS,CAAC,CAAA;IAEjE,OAAO,EAAE,CAAA;AACV,CAAC;AAED,SAAgB,cAAc,CAAE,SAA0B,EAAE,MAAc,EAAE;IAC3E,OAAO,IAAA,eAAK,EAAC,GAAG,EAAE;QACjB,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC;QACnC,SAAS,EAAE,SAAS,IAAI,SAAS;KACjC,CAAC,CAAA;AACH,CAAC;AAED,SAAgB,aAAa,CAAE,QAAuB;IACrD,IAAI,SAAS,GAAG,GAAG,CAAA;IACnB,QAAQ,QAAQ,EAAE,CAAC;QACnB,KAAK,OAAO,CAAC;QACb,KAAK,QAAQ,CAAC;QACd,KAAK,MAAM;YACV,SAAS,GAAG,EAAE,CAAA;IACf,CAAC;IACD,OAAO,SAAS,CAAA;AACjB,CAAC;AAED,SAAgB,eAAe,CAAC,SAAiB;IAChD,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,EACtB,GAAG,GAA2B;QAC7B,WAAW,EAAE,EAAE;QACf,gBAAgB,EAAE,EAAE;QACpB,gBAAgB,EAAE,EAAE;QACpB,gBAAgB,EAAE,EAAE;QACpB,WAAW,EAAE,EAAE;KACf,CAAA;IAEF,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAA;AAC9B,CAAC;AAED,SAAgB,YAAY,CAAE,IAAa;IAC1C,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;AACpD,CAAC;AAED,SAAgB,WAAW,CAAE,IAAa;IACzC,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;AAC7D,CAAC;AAED,kBAAe;IACd,OAAO;IACP,UAAU;IACV,cAAc;IACd,OAAO;IACP,cAAc;IACd,aAAa;IACb,eAAe;IACf,YAAY;IACZ,WAAW;CACX,CAAA"}