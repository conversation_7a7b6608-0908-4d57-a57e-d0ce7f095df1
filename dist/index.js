"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dayjs = exports.Events = exports.Sets = exports.Time = exports.Identities = exports.Dev = exports.Mongo = exports.CardNumbers = exports.Html = exports.Scripts = exports.Security = exports.Qualify = exports.Flows = exports.Lists = exports.Objects = exports.Addresses = exports.Emails = exports.Phones = exports.Languages = exports.Currencies = exports.Names = exports.Hours = exports.Dates = exports.Numbers = exports.Strings = exports.Multitenancy = void 0;
const tslib_1 = require("tslib");
var multitenancy_1 = require("./multitenancy");
Object.defineProperty(exports, "Multitenancy", { enumerable: true, get: function () { return tslib_1.__importDefault(multitenancy_1).default; } });
var strings_1 = require("./strings");
Object.defineProperty(exports, "Strings", { enumerable: true, get: function () { return tslib_1.__importDefault(strings_1).default; } });
var numbers_1 = require("./numbers");
Object.defineProperty(exports, "Numbers", { enumerable: true, get: function () { return tslib_1.__importDefault(numbers_1).default; } });
var dates_1 = require("./dates");
Object.defineProperty(exports, "Dates", { enumerable: true, get: function () { return tslib_1.__importDefault(dates_1).default; } });
var hours_1 = require("./hours");
Object.defineProperty(exports, "Hours", { enumerable: true, get: function () { return tslib_1.__importDefault(hours_1).default; } });
var names_1 = require("./names");
Object.defineProperty(exports, "Names", { enumerable: true, get: function () { return tslib_1.__importDefault(names_1).default; } });
var currencies_1 = require("./currencies");
Object.defineProperty(exports, "Currencies", { enumerable: true, get: function () { return tslib_1.__importDefault(currencies_1).default; } });
var languages_1 = require("./languages");
Object.defineProperty(exports, "Languages", { enumerable: true, get: function () { return tslib_1.__importDefault(languages_1).default; } });
var phones_1 = require("./phones");
Object.defineProperty(exports, "Phones", { enumerable: true, get: function () { return tslib_1.__importDefault(phones_1).default; } });
var emails_1 = require("./emails");
Object.defineProperty(exports, "Emails", { enumerable: true, get: function () { return tslib_1.__importDefault(emails_1).default; } });
var addresses_1 = require("./addresses");
Object.defineProperty(exports, "Addresses", { enumerable: true, get: function () { return tslib_1.__importDefault(addresses_1).default; } });
var objects_1 = require("./objects");
Object.defineProperty(exports, "Objects", { enumerable: true, get: function () { return tslib_1.__importDefault(objects_1).default; } });
var lists_1 = require("./lists");
Object.defineProperty(exports, "Lists", { enumerable: true, get: function () { return tslib_1.__importDefault(lists_1).default; } });
var flows_1 = require("./flows");
Object.defineProperty(exports, "Flows", { enumerable: true, get: function () { return tslib_1.__importDefault(flows_1).default; } });
var qualify_1 = require("./qualify");
Object.defineProperty(exports, "Qualify", { enumerable: true, get: function () { return tslib_1.__importDefault(qualify_1).default; } });
var security_1 = require("./security");
Object.defineProperty(exports, "Security", { enumerable: true, get: function () { return tslib_1.__importDefault(security_1).default; } });
var scripts_1 = require("./scripts");
Object.defineProperty(exports, "Scripts", { enumerable: true, get: function () { return tslib_1.__importDefault(scripts_1).default; } });
var html_1 = require("./html");
Object.defineProperty(exports, "Html", { enumerable: true, get: function () { return tslib_1.__importDefault(html_1).default; } });
var cardnumbers_1 = require("./cardnumbers");
Object.defineProperty(exports, "CardNumbers", { enumerable: true, get: function () { return tslib_1.__importDefault(cardnumbers_1).default; } });
var mongo_1 = require("./mongo");
Object.defineProperty(exports, "Mongo", { enumerable: true, get: function () { return tslib_1.__importDefault(mongo_1).default; } });
var dev_1 = require("./dev");
Object.defineProperty(exports, "Dev", { enumerable: true, get: function () { return tslib_1.__importDefault(dev_1).default; } });
// exported in namespace only:
exports.Identities = tslib_1.__importStar(require("./identities"));
var time_1 = require("./time");
Object.defineProperty(exports, "Time", { enumerable: true, get: function () { return tslib_1.__importDefault(time_1).default; } });
var sets_1 = require("./sets");
Object.defineProperty(exports, "Sets", { enumerable: true, get: function () { return tslib_1.__importDefault(sets_1).default; } });
var events_1 = require("./events");
Object.defineProperty(exports, "Events", { enumerable: true, get: function () { return tslib_1.__importDefault(events_1).default; } });
tslib_1.__exportStar(require("./multitenancy"), exports);
tslib_1.__exportStar(require("./strings"), exports);
tslib_1.__exportStar(require("./numbers"), exports);
tslib_1.__exportStar(require("./dates"), exports);
tslib_1.__exportStar(require("./hours"), exports);
tslib_1.__exportStar(require("./names"), exports);
tslib_1.__exportStar(require("./currencies"), exports);
tslib_1.__exportStar(require("./languages"), exports);
tslib_1.__exportStar(require("./phones"), exports);
tslib_1.__exportStar(require("./emails"), exports);
tslib_1.__exportStar(require("./addresses"), exports);
tslib_1.__exportStar(require("./objects"), exports);
tslib_1.__exportStar(require("./lists"), exports);
tslib_1.__exportStar(require("./flows"), exports);
tslib_1.__exportStar(require("./qualify"), exports);
tslib_1.__exportStar(require("./security"), exports);
tslib_1.__exportStar(require("./scripts"), exports);
tslib_1.__exportStar(require("./html"), exports);
tslib_1.__exportStar(require("./cardnumbers"), exports);
tslib_1.__exportStar(require("./mongo"), exports);
tslib_1.__exportStar(require("./dev"), exports);
tslib_1.__exportStar(require("./rateLimit"), exports);
const format_datetime_1 = require("@perkd/format-datetime");
Object.defineProperty(exports, "dayjs", { enumerable: true, get: function () { return format_datetime_1.formatDateTime; } });
format_datetime_1.formatDateTime.tz.setDefault('Asia/Singapore');
format_datetime_1.formatDateTime.locale('en');
//# sourceMappingURL=index.js.map