"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Jwt = void 0;
exports.safeCredentials = safeCredentials;
exports.generateKey = generateKey;
exports.generateCode = generateCode;
exports.randomString = randomString;
const tslib_1 = require("tslib");
const jws_1 = tslib_1.__importDefault(require("jws"));
const isEmail_1 = tslib_1.__importDefault(require("validator/lib/isEmail"));
const TYP = 'JWT', HS256 = 'HS256', CODE_STRING = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
class Jwt {
    secret;
    constructor(secret) {
        this.secret = secret;
    }
    static decode(token) {
        return jws_1.default.decode(token);
    }
    encode(payload, header) {
        return jws_1.default.sign({
            header: { typ: TYP, ...header, alg: HS256 }, // Default to hash
            payload,
            secret: this.secret,
        });
    }
    decode(token) {
        return Jwt.decode(token);
    }
    verify(token, alg = HS256) {
        try {
            return jws_1.default.verify(token, alg, this.secret);
        }
        catch (e) {
            return false;
        }
    }
}
exports.Jwt = Jwt;
// strong-type, either undefined or 'string' only
function safeCredentials(credentials = {}) {
    const { username, email, password, tenant } = credentials;
    return ((username ? typeof username === 'string' : true) &&
        (email ? (typeof email === 'string' && (0, isEmail_1.default)(email)) : true) &&
        (tenant ? typeof tenant === 'string' : true) &&
        (password ? typeof password === 'string' : true));
}
// remove vowels & non-word, last 6 char for randomness
function generateKey(name, keyLength = 16) {
    const short = name
        ? name.toLowerCase().replace(/[aeiou\W]/g, '').slice(0, keyLength - 6)
        : '';
    return short + (short.length < keyLength ? Math.random().toString().slice(short.length - keyLength) : '');
}
/**
 * Generate OTP code
 */
function generateCode(length) {
    return randomString(length, '123456789');
}
function randomString(size = 6, codeString = CODE_STRING) {
    const maxNum = codeString.length;
    let newCode = '';
    while (size > 0) {
        newCode += codeString.charAt(Math.floor(Math.random() * maxNum));
        size--;
    }
    return newCode;
}
exports.default = {
    Jwt,
    safeCredentials,
    generateKey,
    generateCode,
    randomString
};
//# sourceMappingURL=security.js.map