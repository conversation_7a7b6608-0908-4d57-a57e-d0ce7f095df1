{"version": 3, "file": "strings.js", "sourceRoot": "", "sources": ["../src/strings.ts"], "names": [], "mappings": ";;AASA,4BAEC;AAED,gCAEC;AAED,8BAEC;AAED,gCAqBC;AAED,4BAkCC;AAED,sBAEC;AAED,oBAEC;;AAtFD,oEAAgC;AAChC,yCAA+C;AAE/C;;;;;GAKG;AACH,SAAgB,QAAQ,CAAC,GAAG,GAAG,EAAE,EAAE,IAAS;IAC3C,OAAO,GAAG,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAA;AACxE,CAAC;AAED,SAAgB,UAAU,CAAC,GAAW;IACrC,OAAO,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACpC,CAAC;AAED,SAAgB,SAAS,CAAC,GAAW;IACpC,OAAO,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACnC,CAAC;AAED,SAAgB,UAAU,CAAC,CAAM,EAAE,CAAM;IACxC,MAAM,WAAW,GAAG,CAAC,CAAM,EAAE,EAAE;QAC7B,MAAM,KAAK,GAAG,OAAO,CAAC,CAAA;QACtB,QAAQ,KAAK,EAAE,CAAC;YACf,KAAK,QAAQ;gBACZ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAAE,OAAO,CAAC,CAAC,WAAW,EAAE,CAAA;gBACjD,MAAK;YACN,KAAK,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;YACpC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAA;QAClB,CAAC;IACF,CAAC,EACD,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,EAClB,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,EAClB,KAAK,GAAG,OAAO,CAAC,EAChB,KAAK,GAAG,OAAO,CAAC,CAAA;IAEjB,IAAI,KAAK,KAAK,KAAK,EAAE,CAAC;QACrB,IAAI,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,KAAK;YAAE,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IACvE,CAAC;IAED,OAAO,IAAA,oBAAO,EAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AACrB,CAAC;AAED,SAAgB,QAAQ,CAAC,GAAW;IACnC,SAAS;IACT,+EAA+E;IAC/E,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,qGAAqG,CAAC,CAAA;IAE/H,IAAI,CAAC,MAAM;QAAE,OAAM;IAEnB,IAAI,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,EACtB,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;IAEpB,MAAM,QAAQ,GAAG,OAAO,GAAG,OAAO,EACjC,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;IAEzC,IAAI,OAAO,GAAG,CAAC,CAAA;IAEf,IAAI,UAAU,EAAE,CAAC;QAChB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;QAC3C,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;QAClB,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;QAClB,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAA;IAClE,CAAC;IAED,OAAO;QACN,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;QACnB,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;QACnB,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;QACf,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,sBAAsB;QAC3D,UAAU,EAAE,OAAO;QACnB,OAAO,EAAE,GAAG,GAAG,OAAO;QACtB,QAAQ,EAAE,QAAQ;QAClB,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;QACf,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;QAChB,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;KAChB,CAAA;AACF,CAAC;AAED,SAAgB,KAAK,CAAC,GAAW;IAChC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;AAChD,CAAC;AAED,SAAgB,IAAI,CAAC,GAAW;IAC/B,OAAO,IAAA,gBAAa,EAAC,GAAG,CAAC,CAAC;AAC3B,CAAC;AAED,kBAAe;IACd,QAAQ;IACR,UAAU;IACV,SAAS;IACT,UAAU;IACV,QAAQ;IACR,KAAK;IACL,IAAI;CACJ,CAAA"}