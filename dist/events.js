"use strict";
/**
 * Used by Eventbus & related packages
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.domainOf = domainOf;
exports.match = match;
const WILD = '*';
function domainOf(eventName = '') {
    return eventName.split('.')[0];
}
/**
 * Match str with pattern, wildcards supported (domain MUST be present):
 * 		domain.*
 * 		domain.actor*
 * 		domain.*.update
 * ILLEGAL: *, *.update, *.action*
 * @param str
 * @param pattern
 */
function match(str, pattern) {
    if (!pattern.includes(WILD))
        return (pattern === str);
    const domain = domainOf(pattern), [before, after] = pattern.split(WILD);
    // Reject patterns that are just a domain or start with a wildcard
    if (domain === pattern || pattern.startsWith(WILD))
        return false;
    return (!before || (before && str.startsWith(before))) &&
        (!after || (after && str.endsWith(after))) ? true : false;
}
exports.default = {
    domainOf,
    match,
};
//# sourceMappingURL=events.js.map