{"version": 3, "file": "identities.js", "sourceRoot": "", "sources": ["../src/identities.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAKH,kDAMC;AAED,gDAMC;AAjBD,MAAM,MAAM,GAAG,IAAI,EAClB,SAAS,GAAG,IAAI,CAAA;AAEjB,SAAgB,mBAAmB,CAAC,OAAe,EAAE,YAAoB;IACxE,QAAQ,OAAO,EAAE,CAAC;QAClB,KAAK,MAAM,CAAC,CAAC,OAAO,MAAM,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAA;QAC5D,KAAK,SAAS,CAAC,CAAC,OAAO,SAAS,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAA;IAClE,CAAC;IACD,OAAO,IAAI,CAAA;AACZ,CAAC;AAED,SAAgB,kBAAkB,CAAC,OAAe,EAAE,QAAgB;IACnE,QAAQ,OAAO,EAAE,CAAC;QACjB,KAAK,MAAM,CAAC,CAAC,OAAO,MAAM,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAA;QAC/D,KAAK,SAAS,CAAC,CAAC,OAAO,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;IAClD,CAAC;IACD,OAAO,IAAI,CAAA;AACZ,CAAC;AAED,IAAiB,SAAS,CAuCzB;AAvCD,WAAiB,SAAS;IACzB,MAAM,wCAAwC;IAC7C,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAC/B,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACpE,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACpE,SAAS,GAAG,oBAAoB,EAChC,yBAAyB,GAAG,sEAAsE,CAAA;IAEnG,2EAA2E;IAC3E,SAAgB,mBAAmB,CAAC,GAAW;QAC9C,OAAO,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3C,CAAC;IAFe,6BAAmB,sBAElC,CAAA;IAED,SAAgB,MAAM,CAAC,IAAY;QAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,OAAO,KAAK,CAAA;QAEvC,8CAA8C;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,EACrB,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,EAC/C,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;QAEnB,qBAAqB;QACrB,IAAI,GAAG,GAAG,CAAC,CAAA;QACX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;QAC9B,CAAC;QAED,6BAA6B;QAC7B,IAAI,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;YACtC,GAAG,IAAI,CAAC,CAAA;QACT,CAAC;QAED,MAAM,SAAS,GAAG,GAAG,GAAG,EAAE,EACzB,QAAQ,GAAG,CAAC,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,CAAC;YAC5C,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC;YACvB,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAA;QAEzB,OAAO,QAAQ,KAAK,QAAQ,CAAA;IAC7B,CAAC;IAzBe,gBAAM,SAyBrB,CAAA;AACF,CAAC,EAvCgB,SAAS,yBAAT,SAAS,QAuCzB;AAED,IAAiB,MAAM,CAetB;AAfD,WAAiB,MAAM;IACtB,MAAM,uBAAuB,GAAG,6BAA6B,EAC5D,yBAAyB,GAAG,SAAS,CAAA;IAEtC,qDAAqD;IAErD,QAAQ;IACR,SAAgB,0BAA0B,CAAC,GAAW;QACrD,OAAO,uBAAuB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACzC,CAAC;IAFe,iCAA0B,6BAEzC,CAAA;IAED,OAAO;IACP,SAAgB,mBAAmB,CAAC,GAAW;QAC9C,OAAO,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3C,CAAC;IAFe,0BAAmB,sBAElC,CAAA;AACF,CAAC,EAfgB,MAAM,sBAAN,MAAM,QAetB"}