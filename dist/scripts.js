"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadPackage = loadPackage;
exports.loadModule = loadModule;
exports.scriptPath = scriptPath;
const node_path_1 = require("node:path");
const SCRIPT_ROOT = '/lib/providers';
function loadPackage(name) {
    try {
        return require(name);
    }
    catch (err) {
        return err;
    }
}
function loadModule(name, path) {
    try {
        return require(scriptPath(name, path));
    }
    catch (err) {
        return err;
    }
}
function scriptPath(filename, pathStr = SCRIPT_ROOT) {
    return `${(0, node_path_1.resolve)(__dirname)}${pathStr}${filename ? '/' + filename : ''}`;
}
exports.default = {
    loadPackage,
    loadModule,
    scriptPath
};
//# sourceMappingURL=scripts.js.map