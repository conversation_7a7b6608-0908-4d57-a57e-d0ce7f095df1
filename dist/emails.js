"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailType = exports.isEmail = void 0;
exports.emailAddressType = emailAddressType;
exports.isPersonalEmail = isPersonalEmail;
const tslib_1 = require("tslib");
const email_addresses_1 = require("email-addresses");
var isEmail_1 = require("validator/lib/isEmail");
Object.defineProperty(exports, "isEmail", { enumerable: true, get: function () { return tslib_1.__importDefault(isEmail_1).default; } });
var EmailType;
(function (EmailType) {
    EmailType["HOME"] = "home";
    EmailType["WORK"] = "work";
    EmailType["OTHERS"] = "others";
})(EmailType || (exports.EmailType = EmailType = {}));
const { HOME, WORK, OTHERS } = EmailType, PERSONAL_DOMAINS = [
    'gmail', 'hotmail', 'live', 'outlook', 'msn', 'icloud', 'mac', 'yahoo', 'aol', 'mail', 'singnet', 'qq',
];
function emailAddressType(email) {
    const parts = (0, email_addresses_1.parseOneAddress)(email) || {}, { domain = '' } = parts || {};
    return domain ? (isPersonalDomain(domain) ? HOME : WORK) : OTHERS;
}
function isPersonalEmail(email) {
    const parts = (0, email_addresses_1.parseOneAddress)(email), { domain = '' } = parts || {};
    return domain ? isPersonalDomain(domain) : false;
}
// ---- Private Functions
function isPersonalDomain(domain) {
    const [secondLevel] = domain.split('.');
    return PERSONAL_DOMAINS.indexOf(secondLevel) >= 0;
}
exports.default = {
    isPersonalEmail,
    emailAddressType,
    parseEmailAddress: email_addresses_1.parseOneAddress
};
//# sourceMappingURL=emails.js.map