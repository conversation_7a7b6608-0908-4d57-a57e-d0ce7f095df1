import ObjectId from 'bson-objectid';
import isMongoId from 'validator/lib/isMongoId';
export type DatasourceOptions = {
    name: string;
    host: string;
    username?: string;
    password?: string;
    authDb?: string;
    dbSet?: string;
    maxPoolSize?: string;
};
export declare function getDatabaseUrl(options: DatasourceOptions): string;
export declare function shortId(len?: number): string;
export { ObjectId };
declare const _default: {
    isMongoId: typeof isMongoId;
    getDatabaseUrl: typeof getDatabaseUrl;
    shortId: typeof shortId;
};
export default _default;
