"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.operations = void 0;
exports.satisfyCondition = satisfyCondition;
exports.decodeFilterKeys = decodeFilterKeys;
exports.substituteMoments = substituteMoments;
exports.addCondition = addCondition;
exports.substituteVariables = substituteVariables;
const tslib_1 = require("tslib");
const sift_1 = tslib_1.__importStar(require("sift"));
const bson_objectid_1 = tslib_1.__importDefault(require("bson-objectid"));
const traverse_1 = tslib_1.__importDefault(require("traverse"));
const get_value_1 = tslib_1.__importDefault(require("get-value"));
const isMongoId_1 = tslib_1.__importDefault(require("validator/lib/isMongoId"));
const format_datetime_1 = require("@perkd/format-datetime");
const dates_1 = require("./dates");
function satisfyCondition(object = {}, condition, options) {
    try {
        if (!condition)
            return true;
        if (typeof condition === 'string') {
            condition = JSON.parse(condition);
        }
        const decoded = decodeFilterKeys(condition), formatted = substituteMoments(decoded);
        return (0, sift_1.default)(formatted, options)(object);
    }
    catch (err) {
        return false;
    }
}
function decodeFilterKeys(filter) {
    // replace "_" to "." in filter key
    // replace "#" to "$" in filter key
    // example: {"where":{"gender":2,"dateList_month":3,"dateList_name":"birth"}}
    // replace to : {"where":{"gender":2,"dateList.month":3,"dateList.name":"birth"}}
    return (0, traverse_1.default)(filter).forEach(decode);
    function decode(node) {
        if (node && typeof node === 'object') {
            let newKey = '';
            for (const key in node) {
                if (key !== '_gId' && key !== '_data' && key !== '_id') {
                    newKey = key.replace(/#/i, '$');
                    newKey = newKey.replace(/(?<!(\.|\b|_))_/ig, '.');
                    node[newKey] = node[key];
                    if (key !== newKey) {
                        node[newKey] = decodeFilterKeys(node[newKey]);
                        delete node[key];
                    }
                }
            }
            this.update(node);
        }
    }
}
// sift operations
exports.operations = {
    // usage: {'member.behaviors.purchase.last.at': { $ltDays: 540 }}
    // means the days between current date and value of 'member.behaviors.purchase.last.at', should be less than 540 days
    $ltDays(params, ownerQuery, options) {
        return (0, sift_1.createEqualsOperation)((value) => (new Date().getTime() - value) < (params * 24 * 60 * 60 * 1000), ownerQuery, options);
    },
    // usage: { "endTime":{"$gtDays":-364} }
    $gtDays(params, ownerQuery, options) {
        return (0, sift_1.createEqualsOperation)((value) => (new Date().getTime() - value) > (params * 24 * 60 * 60 * 1000), ownerQuery, options);
    },
    /*
     * Apply quaifiers for max count of memberships of same program, for example: Gift Card
     * @param	{Object} params - {
     * 				properties: ['memberships.current'],
     * 				query: { 'programId': { $eq: '62dc1aa50e4b5d001d9492da' } },
     * 				compare: { $gte: 3 }
     * 			}
     *			{Object} properties[] - get the arrays to filter
     *			{Object} query - apply to previous result
     *			{Object} compare - check count of previous result
     */
    $count(params, ownerQuery, options) {
        const { properties, query, compare } = params, qualifier = (0, sift_1.default)(query), check = (0, sift_1.default)(compare), fn = (profile) => {
            const count = properties.reduce((result, property) => {
                const arr = (0, get_value_1.default)(profile || {}, property);
                return result + arr.filter(qualifier).length;
            }, 0);
            return (profile ? check(count) : false);
        };
        return (0, sift_1.createEqualsOperation)(fn, ownerQuery, options);
    },
    /*
     * Apply quaifiers for total amount of selected items within an order, for example: different spend on selected products triggers different offers
     * @param	{Object} params - {
     * 				properties: ['itemList'],
     * 				query: { 'product.external.shopify.productId': { $in: ['7938905702569','7938905669801'] } },
     * 				compare: { $gte: 50 }
     * 				field: 'price'
     * 			}
     *			{Object} properties[] - get the arrays to filter
     *			{Object} query - apply to previous result
     *			{Object} compare - check total amount of previous result
     *			{String} field - which field to sum
     */
    $sum(params, ownerQuery, options) {
        const { properties, query, compare, field } = params, qualifier = (0, sift_1.default)(query), check = (0, sift_1.default)(compare), fn = (profile) => {
            const total = properties.reduce((result, property) => {
                const arr = (0, get_value_1.default)(profile || {}, property), filtered = arr.filter(qualifier), subTotal = filtered.reduce((t, item = {}) => t + (item[field] ?? 0), 0);
                return result + subTotal;
            }, 0);
            return (profile ? check(total) : false);
        };
        return (0, sift_1.createEqualsOperation)(fn, ownerQuery, options);
    },
    // $cmp(params, ownerQuery, options) {
    // 	return sift.createEqualsOperation(
    // 		value => _.get(value, params[0]) === _.get(value, params[1]),
    // 		ownerQuery, options
    // 	);
    // },
};
/**
 * Customize datetime in JSON Object
 */
function substituteMoments(obj, values = {}, timeZone = dates_1.TIMEZONE) {
    if (typeof obj !== 'object')
        return obj;
    const now = (0, format_datetime_1.formatDateTime)().tz(timeZone), yesterday = (0, format_datetime_1.formatDateTime)().tz(timeZone).subtract(1, 'day'), tomorrow = (0, format_datetime_1.formatDateTime)().tz(timeZone).add(1, 'day'), variables = Object.assign({
        $now: new Date(now.format()),
        $date: now.format('YYYY-MM-DD'),
        $day: Number(now.format('D')),
        $month: Number(now.format('M')),
        $year: Number(now.year()),
        $yesterday: yesterday.format('YYYY-MM-DD'),
        $tomorrow: tomorrow.format('YYYY-MM-DD'),
    }, values);
    (0, traverse_1.default)(obj).forEach(substitute);
    return obj;
    function substitute(node) {
        const result = Object.keys(node || {}).find(key => key !== 'moment' && typeof now[key] !== 'function');
        if (node && typeof node === 'object' && node.moment && !result) {
            this.update(calcTime(node));
        }
        else if (node && typeof node === 'string') {
            if (node.toLowerCase().indexOf('$startof') === 0) {
                const unit = node.slice(8).toLowerCase();
                this.update(new Date(now.startOf(unit).format()));
            }
            else if (node.toLowerCase().indexOf('$endof') === 0) {
                const unit = node.slice(6).toLowerCase();
                this.update(new Date(now.endOf(unit).format()));
            }
            else if (typeof variables[node] !== 'undefined') {
                this.update(variables[node]);
            }
            else if (node.indexOf('$') === 0 && (0, isMongoId_1.default)(node.slice(1))) {
                this.update((0, bson_objectid_1.default)(node.slice(1)));
            }
        }
    }
    function calcTime(node) {
        let time = (node.moment.toLowerCase() === 'now' ? (0, format_datetime_1.formatDateTime)() : (0, format_datetime_1.formatDateTime)(node.moment))
            .tz(node.tz || timeZone);
        for (const [key, value] of Object.entries(node)) {
            if (typeof time[key] === 'function') {
                time = (value === null) ? time[key]() : time[key](value);
                if (key === 'month')
                    time += 1;
            }
        }
        return format_datetime_1.formatDateTime.isDayjs(time)
            ? time.toDate()
            : (node.format?.toLowerCase() === 'x') ? Number(time) : time;
    }
}
function addCondition(where = {}, condition, operator = 'and') {
    const op = Object.keys(where)[0]?.toLowerCase();
    if (op === 'and') {
        if (operator === 'and') {
            if (!where.and)
                where.and = [];
            where.and.push(condition);
        }
        else {
            where = { or: [condition, { and: where.and }] };
        }
    }
    else if (op === 'or') {
        if (operator === 'and') {
            where = { and: [condition, { or: where.or }] };
        }
        else {
            if (!where.or)
                where.or = [];
            where.or.push(condition);
        }
    }
    else {
        if (operator === 'and') {
            Object.assign(where, condition);
        }
        else {
            where = { or: [condition, where] };
        }
    }
    return where;
}
/**
 * Replace $xxxx in JSON Object
 * $xxxx can be any type
 */
function substituteVariables(obj, values, json) {
    if (typeof obj === 'object' && !json) {
        (0, traverse_1.default)(obj).forEach(function (node) {
            if (node && typeof node === 'string' && typeof values[node] !== 'undefined') {
                this.update(values[node]);
            }
        });
        return obj;
    }
    let str = JSON.stringify(obj);
    for (const v in values) {
        if ({}.hasOwnProperty.call(values, v)) {
            const regex = new RegExp('\\$' + v, 'g');
            str = str.replace(regex, values[v]);
        }
    }
    return JSON.parse(str);
}
exports.default = {
    satisfyCondition,
    decodeFilterKeys,
    operations: exports.operations,
    substituteMoments,
    addCondition,
    substituteVariables
};
//# sourceMappingURL=qualify.js.map