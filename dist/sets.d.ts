export declare function intersection<T>(sets: Set<T>[]): Set<any>;
export declare function union<T>(sets: Set<T>[]): Set<any>;
export declare function difference<T>(a: Set<T>, sets: Set<T>[]): Set<T>;
export declare function symmetricDifference<T>(a: Set<T>, b: Set<T>): Set<T>;
declare const _default: {
    intersection: typeof intersection;
    union: typeof union;
    difference: typeof difference;
    symmetricDifference: typeof symmetricDifference;
};
export default _default;
