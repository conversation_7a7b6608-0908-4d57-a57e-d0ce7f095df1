"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bestLanguage = bestLanguage;
exports.localizeContent = localizeContent;
exports.languageOf = languageOf;
exports.mergeGlobalize = mergeGlobalize;
const tslib_1 = require("tslib");
const locale_1 = tslib_1.__importDefault(require("locale"));
const CHINESE_MAP = {
    'zh-HK': 'zh-Hant-HK',
    'zh-TW': 'zh-Hant',
    'zh-CN': 'zh-<PERSON>',
    'yue-<PERSON>': 'zh-<PERSON>',
    'yue-Hant': 'zh-Hant-HK',
};
/**
 * Return the best match language
 * @param preferred languages ['zh', 'en'...]
 * @param supported ['zh-Hant', 'en']
 * @param fallback default language, 'zh-Hans'
 */
function bestLanguage(preferred = [], supported, fallback) {
    const all = preferred.reduce((res, language) => {
        const language2 = language.replace(/-[A-Z]{2}$/, '');
        res.push(language);
        res.push(language2);
        CHINESE_MAP[language] && res.push(CHINESE_MAP[language]);
        CHINESE_MAP[language2] && res.push(CHINESE_MAP[language2]);
        return res;
    }, []), found = all.find((language) => supported.includes(language));
    if (found)
        return found;
    const candidates = new locale_1.default.Locales(supported, fallback);
    return new locale_1.default.Locales(all).best(candidates).toString();
}
/**
 * Return the best match content
 * @param preferred languages
 * @param t - globalize.t
 * @param fallback default language
 */
function localizeContent(preferred, t, fallback) {
    const supportedLanguages = Object.keys(t), [first] = supportedLanguages, lng = bestLanguage(preferred, supportedLanguages, fallback || first);
    return lng && t[lng] || t[first];
}
/**
 * Maps language code from app (locale in install header) to code used by globalize
 */
function languageOf(locale = {}) {
    const { languages = [] } = locale, [first = ''] = languages, [lang, dialect, country] = first.split('-');
    if ((country === 'HK' || country === 'MO') && dialect === 'Hant') {
        return 'zh-Hant-HK';
    }
    return dialect ? `${lang}-${dialect}` : lang;
}
function mergeGlobalize(obj, language) {
    if (obj.globalize) {
        const { globalize, ...data } = obj, g = globalize, { t = {} } = g;
        return Object.assign({}, data, t[g.default], t[language]);
    }
    return obj;
}
exports.default = {
    bestLanguage,
    localizeContent,
    languageOf,
    mergeGlobalize
};
//# sourceMappingURL=languages.js.map