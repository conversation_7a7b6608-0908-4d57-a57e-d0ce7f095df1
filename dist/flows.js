"use strict";
/**
 * Flows Module - defines a Flow as a sequence of steps and provides utility
 *		functions to navigate through these steps.
 *
 * The Flow type represents a multi-step process using:
 *  - `at`: the current step index.
 *  - `steps`: an array of step names (strings) that defines the sequence.
 *
 * These functions are useful for managing the progression and transition logic
 * in multi-step processes such as registration, onboarding, or wizard interfaces.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.nextStepOf = nextStepOf;
exports.preStepOf = preStepOf;
exports.stepOf = stepOf;
exports.hasStep = hasStep;
exports.atStep = atStep;
exports.isLastStep = isLastStep;
/**
 * Get the position of next step for the flow
 * @param step one of the step names in the flow, eg. request, register, share, done...
 * @param flow
 */
function nextStepOf(step, flow) {
    const currentIndex = stepOf(step, flow);
    // Return -1 if the step doesn't exist in the flow
    if (currentIndex === -1)
        return -1;
    const nextIndex = currentIndex + 1;
    return flow.steps[nextIndex] ? nextIndex : currentIndex;
}
/**
 * Get the position of previous step for the flow
 * @param step name
 * @param flow
 */
function preStepOf(step, flow) {
    const index = stepOf(step, flow) - 1;
    return flow.steps[index] ? index : index + 1;
}
/**
 * Get the position of given step in the flow
 * @param step name
 * @param flow
 */
function stepOf(step, flow) {
    return indexOf(step, flow.steps);
}
/**
 * Check if the step is in the flow
 * @param step name
 * @param flow
 */
function hasStep(step, flow) {
    return (flow.steps || []).includes(step);
}
/**
 * Check if the step is at step in the flow
 * @param step name
 * @param flow
 */
function atStep(step, flow) {
    const { at } = flow;
    return stepOf(step, flow) === at;
}
/**
 * Check if the step is the last step in the flow
 * @param flow
 * @param step name
 */
function isLastStep(flow, step) {
    const { at, steps } = flow, last = steps.length - 1;
    return step ? indexOf(step, steps) === last : at === last;
}
function indexOf(step, steps = []) {
    return steps.findIndex(s => s === step);
}
exports.default = {
    nextStepOf,
    preStepOf,
    stepOf,
    atStep,
    hasStep,
    isLastStep
};
//# sourceMappingURL=flows.js.map