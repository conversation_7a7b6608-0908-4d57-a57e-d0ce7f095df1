/**
 * Time of day manipulation
 *	- string HH:MM format, 00:00 - 24:00
 *	- where 24:00 represents midnight at the end of the specified day field
 */
import { formatDateTime as dayjs } from '@perkd/format-datetime';
type TimeDetail = {
    hour: number;
    min: number;
};
/**
 * time hh:mm to number
 * 02:30 => 230
 * replace valueOf() for app
*/
declare function value(time: string): number;
declare function values(time: string): {
    hour: number;
    minute: number;
    value: number;
};
declare function add(time: string, value: number, unit?: dayjs.ManipulateType): string;
declare function subtract(time: string, value: number, unit?: dayjs.ManipulateType): string;
/**
 * Convert time value to string with colon
 * 1430 => "14:30"
 * 930 => "09:30"
 */
declare function toString(timeValue: number): string;
/**
 * apply hhmm
 */
declare function toDate(time: string, date: dayjs.Dayjs): Date;
/**
 * pad number with leading zeros: 9 => 09
 */
export declare function addZero(num: number): string;
/**
 * replace fixTime() for app
 * time hh:mm to hhmm
 * 8x faster than using .replace(':', '')
*/
export declare function removeColon(time: string): string;
/**
 * replace hhmm() for app
 * format time to hhmm
 */
export declare function toHhmm(hr?: number, min?: number): string;
/**
 * time: 0200 or 02:00
 * convert hhmm or hh:mm to {hour, min}
 */
export declare function hhmmToHourMin(time: string): TimeDetail;
/**
 * @param {String} time in hhmm or hh:mm
 * @param {Boolean} withMin if must show minute, true: 9:00pm, false: 9pm
 */
export declare function hhmmToAmPm(time: string, withMin?: boolean): string;
/**
 * replace dateToHhmm() for app
 * get hh:mm from a date
 */
declare function getHhmm(date: string | number | Date): string;
/**
 * replace hhmmToDate() for app
 * apply hhmm string to a given date
 * @param {String} time in hhmm or hh:mm
 * @param {Date} date optional, use today if no date provided
 */
export declare function applyHhmmToDate(time: string, date?: Date): Date;
/**
 * Calculate the difference in minutes between two times
 * @param {string} timeA in hhmm / hh:mm format
 * @param {string} timeB in hhmm / hh:mm format
 * @return {number} diff in minutes
 */
export declare function diffMinutes(timeA: string, timeB: string): number;
/**
 * add hour & time to a given time string
 * @param {string} time in hhmm / hh:mm format
 * @param {number} hour optional
 * @param {number} min optional
 * @return {string} hh:mm format
 */
export declare function calculateHhmm(time: string, hour?: number, min?: number): string;
/**
 * replace getTime() for app
 * return time based on given timezone
 * @param {string} timeZone
 * @param {Date} dateTime optional = new Date()
 * @return {string} hhmm format
 */
export declare function getTimeInTimeZone(timeZone: string, dateTime?: Date): string;
declare const _default: {
    value: typeof value;
    values: typeof values;
    add: typeof add;
    subtract: typeof subtract;
    toString: typeof toString;
    toDate: typeof toDate;
    addZero: typeof addZero;
    removeColon: typeof removeColon;
    toHhmm: typeof toHhmm;
    hhmmToHourMin: typeof hhmmToHourMin;
    hhmmToAmPm: typeof hhmmToAmPm;
    getHhmm: typeof getHhmm;
    applyHhmmToDate: typeof applyHhmmToDate;
    diffMinutes: typeof diffMinutes;
    calculateHhmm: typeof calculateHhmm;
    getTimeInTimeZone: typeof getTimeInTimeZone;
};
export default _default;
