export declare function satisfyCondition(object: any | undefined, condition: null | string | any, options?: object): boolean;
export declare function decodeFilterKeys(filter: object): any;
export declare const operations: {
    $ltDays(params: number, ownerQuery: any, options: any): any;
    $gtDays(params: number, ownerQuery: any, options: any): any;
    $count(params: any, ownerQuery: any, options: any): import("sift").EqualsOperation<any>;
    $sum(params: any, ownerQuery: any, options: any): import("sift").EqualsOperation<any>;
};
/**
 * Customize datetime in JSON Object
 */
export declare function substituteMoments(obj: any, values?: {}, timeZone?: string): any;
export declare function addCondition(where: any | undefined, condition: any, operator?: 'and' | 'or'): any;
/**
 * Replace $xxxx in JSON Object
 * $xxxx can be any type
 */
export declare function substituteVariables(obj: any, values: {
    [key: string]: any;
}, json?: boolean): any;
declare const _default: {
    satisfyCondition: typeof satisfyCondition;
    decodeFilterKeys: typeof decodeFilterKeys;
    operations: {
        $ltDays(params: number, ownerQuery: any, options: any): any;
        $gtDays(params: number, ownerQuery: any, options: any): any;
        $count(params: any, ownerQuery: any, options: any): import("sift").EqualsOperation<any>;
        $sum(params: any, ownerQuery: any, options: any): import("sift").EqualsOperation<any>;
    };
    substituteMoments: typeof substituteMoments;
    addCondition: typeof addCondition;
    substituteVariables: typeof substituteVariables;
};
export default _default;
