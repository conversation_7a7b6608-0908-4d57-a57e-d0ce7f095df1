"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.splitIntoChunks = splitIntoChunks;
const DEFAULT_CHUNK = 5000;
/**
 * Creates an array of elements split into groups the length of chunkSize.
 * If array can't be split evenly, the final chunk will be the remaining elements.
 * - this function is 30x faster than _.chunk()
*/
function splitIntoChunks(arr, chunkSize = DEFAULT_CHUNK) {
    const chunks = [];
    for (let i = 0, j = arr.length; i < j; i += chunkSize) {
        chunks.push(arr.slice(i, i + chunkSize));
    }
    return chunks;
}
exports.default = {
    splitIntoChunks
};
//# sourceMappingURL=lists.js.map