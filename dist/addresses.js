"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddressType = void 0;
exports.formatAddress = formatAddress;
exports.addressComponents2Address = addressComponents2Address;
exports.completeAddress = completeAddress;
const objects_1 = require("./objects");
// TODO: import from lookup
var GeoType;
(function (GeoType) {
    GeoType["POINT"] = "Point";
})(GeoType || (GeoType = {}));
var AddressType;
(function (AddressType) {
    AddressType["HOME"] = "home";
    AddressType["WORK"] = "work";
    AddressType["OTHERS"] = "others";
})(AddressType || (exports.AddressType = AddressType = {}));
const STORE = 'store';
const COUNTRY_NAME = {
    TW: '台灣',
    SG: 'Singapore',
    MY: 'Malaysia',
    HK: 'Hong Kong',
    JP: 'Japan',
    KR: 'Korea',
}, FORMATS = {
    Default: {
        full: 'No.{unit}, {floor}/F',
        floor: '{floor}/F',
        unit: 'No.{unit}',
        formatted: '{unitNo} {address}', // TODO:
        short: '{unitNo} {address}', // TODO:
    },
    SG: {
        full: '#{floor}-{unit}',
        floor: '#{floor}',
        unit: '#{unit}',
        formatted: '{house} {street}, {unitNo} {premise}, {country} {postCode}',
        short: '{house} {street}, {unitNo} {premise}',
    },
    TW: {
        full: '{floor}樓之{unit}',
        floor: '{floor}樓',
        unit: '之{unit}號',
        formatted: '{postCode}{country}{state}{street}{house}{premise}{unitNo}',
        short: '{state}{street}{house}{premise}{unitNo}',
    },
    MY: {
        full: 'Lot {unit}, {floor}/F',
        floor: '{floor}/F',
        unit: 'Lot {unit}',
        formatted: '{house} {unitNo}, {street}, {postCode} {city} {state}, {country}',
        short: '{house} {unitNo}, {street}',
    },
    CN: {
        full: '{floor}楼{unit}号',
        floor: '{floor}楼',
        unit: '{unit}号',
        formatted: '{address} {unitNo}', // TODO: improve format, replace {address} with combination of {country}{state}{city}... etc.
        short: '{unitNo} {address}', // TODO:
    },
    JP: {
        full: '{floor}F {unit}',
        floor: '{floor}F',
        unit: '{unit}',
        formatted: '{address} {unitNo}', // TODO:
        short: '{unitNo} {address}', // TODO:
    },
    US: {
        full: 'Level {floor}, Suite {unit}',
        floor: 'Level {floor}',
        unit: 'Suite {unit}',
        formatted: '{address} {unitNo}', // TODO:
        short: '{unitNo} {address}', // TODO:
    },
}, ComponentsToRemove = ['country', 'state', 'postCode', 'house', 'premise', 'unitNo']; // for getting street from formatted or short
function formatAddress(address) {
    address.short = format('short', address);
    address.formatted = format('formatted', address);
    return address;
}
function addressComponents2Address(addressComponents, formattedAddr) {
    const SHORT_CONFIG = {
        DEFAULT: { include: true, types: ['route', 'street_number'] },
        TW: { include: true, types: ['route', 'street_number', 'administrative_area_level_3'] },
        HK: { include: true, types: ['route', 'street_number', 'neighborhood'] },
        MY: { include: true, types: ['route', 'street_number', 'administrative_area_level_1'] },
        AU: { include: true, types: ['route', 'street_number', 'administrative_area_level_2'] },
    };
    const postCode = addressComponents.find(comp => comp.types
        && comp.types.indexOf('postal_code') >= 0), country = addressComponents.find(comp => comp.types
        && comp.types.indexOf('country') >= 0), state = addressComponents.find(comp => comp.types
        && comp.types.indexOf('neighborhood') >= 0 || comp.types.indexOf('administrative_area_level_1') >= 0), city = addressComponents.find(comp => comp.types
        && (comp.types.indexOf('locality') >= 0 || comp.types.indexOf('administrative_area_level_2') >= 0)), street1 = addressComponents.find(comp => comp.types
        && (comp.types.indexOf('route') >= 0 || comp.types.indexOf('administrative_area_level_3') >= 0)), street2 = addressComponents.find(comp => comp.types
        && (comp.types.indexOf('administrative_area_level_4') >= 0)), house = addressComponents.find(comp => comp.types
        && comp.types.indexOf('street_number') >= 0), level = addressComponents.find(comp => comp.types
        && comp.types.indexOf('floor') >= 0), street = street2
        ? (street2.long_name + ' ' + street1?.long_name)
        : street1?.long_name;
    const { short_name } = country ?? {}, shortConfig = (short_name && SHORT_CONFIG[short_name])
        ? SHORT_CONFIG[short_name]
        : SHORT_CONFIG.DEFAULT, address = {
        type: STORE,
        level: level?.short_name,
        house: house?.short_name,
        city: city?.short_name,
        street,
        state: state?.short_name,
        country: country?.short_name,
        postCode: postCode?.short_name,
        formatted: formattedAddr,
        short: getShortAddress(addressComponents, formattedAddr, shortConfig.types, shortConfig.include),
        optIn: true,
    };
    return address;
    function getShortAddress(addressComponents, formattedAddr, types, include = true) {
        const indexedComp = indexComponents(formattedAddr, addressComponents), sortedComp = indexedComp.sort((a, b) => (a.index || 0) - (b.index || 0)), shortNames = [];
        for (let i = 0; i < sortedComp.length; i++) {
            const comp = sortedComp[i].types.find(type => types.includes(type));
            if (include ? comp : !comp) {
                const { short_name: short, long_name: long } = sortedComp[i], hasComma = formattedAddr.includes(short + ',') || formattedAddr.includes(long + ','), hasSpace = formattedAddr.includes(short + ' ') || formattedAddr.includes(long + ' ');
                shortNames.push(hasComma ? (short + ', ') : (hasSpace ? (short + ' ') : short));
            }
        }
        const shortAddr = shortNames.join('').trim().replace(/(^,)|(,$)/g, '');
        if (shortAddr)
            return shortAddr;
        const filteredAddr = filterAddress(formattedAddr, addressComponents);
        return filteredAddr;
    }
    function indexComponents(formattedAddr, addressComponents) {
        const fullAddr = formattedAddr;
        for (let i = 0; i < addressComponents.length; i++) {
            const comp = addressComponents[i];
            if (comp.types.includes('street_number'))
                (formatStreetNumber(comp, '號') || formatStreetNumber(comp, '号'));
            const longIndex = fullAddr.indexOf(comp.long_name);
            if (longIndex !== -1) {
                comp.index = longIndex;
                continue;
            }
            const shortIndex = fullAddr.indexOf(comp.short_name);
            comp.index = shortIndex;
        }
        return addressComponents.filter(comp => comp.index !== -1);
    }
    function formatStreetNumber(comp, str) {
        const long = comp.long_name + str, short = comp.short_name + str;
        if (formattedAddr.includes(long))
            comp.long_name = long;
        if (formattedAddr.includes(short))
            comp.short_name = short;
        return long || short;
    }
    function filterAddress(formattedAddr, addressComponents) {
        let filteredAddr = formattedAddr;
        const excludes = addressComponents.filter(comp => comp.types.includes('country') || comp.types.includes('postal_code'));
        for (let i = 0; i < excludes.length; i++) {
            const { long_name: long, short_name: short } = excludes[i];
            filteredAddr = (formattedAddr.includes(long)) ? filteredAddr.replace(long, '') : filteredAddr.replace(short, '');
        }
        return filteredAddr.trim().replace(/(^,)|(,$)/g, '');
    }
}
function completeAddress(address, geocoding) {
    const result = (0, objects_1.cloneDeep)(address.toJSON ? address.toJSON() : address), { address_components, formatted_address, geometry } = geocoding, parsed = addressComponents2Address(address_components, formatted_address), { house, level, unit, street, city, state, postCode, country, short } = parsed, { location } = geometry, { lng, lat } = location;
    if (!address.house)
        result.house = house;
    if (!address.level)
        result.level = level;
    if (!address.unit)
        result.unit = unit;
    if (!address.street)
        result.street = street;
    if (!address.city)
        result.city = city;
    if (!address.state)
        result.state = state;
    if (!address.postCode)
        result.postCode = postCode;
    if (!address.country)
        result.country = country;
    if (!address.short)
        result.short = short;
    if (!address.geo)
        result.geo = { type: GeoType.POINT, coordinates: [lng, lat] };
    if (address.valid === undefined)
        result.valid = true;
    return result;
}
exports.default = {
    formatAddress,
    completeAddress,
    addressComponents2Address
};
// -----  Private Functions  -----
function format(type, address) {
    const addr = address[type] || '', { house = '', level = '', unit = '', country: countryCode = '' } = address, country = COUNTRY_NAME[countryCode], unitNo = formatUnitNo(level, unit, countryCode) || '', fallback = type === 'short' ? 'formatted' : 'short', fallbackAddr = address[type] || address[fallback] || '', street = getStreet(address.street || fallbackAddr, { ...address, unitNo, country, countryCode }); // use when street is missing
    // if (unitNo && addr.includes(unitNo)) return addr; // TODO: use app version
    const FORMAT = (FORMATS[countryCode] || FORMATS.Default)[type], data = { ...address, street, unitNo, country }, components = FORMAT.split(', '), trimmed = trim(components.reduce((res, component) => {
        const str = trim(substitute(component, data));
        return str ? (res ? `${res}, ${str}` : str) : res;
    }, ''));
    return trimmed || fallbackAddr;
}
function formatUnitNo(floor = '', unit = '', country) {
    if (!unit && !floor)
        return '';
    const { full = FORMATS.Default.full, floor: floorFormat = FORMATS.Default.floor, unit: unitFormat = FORMATS.Default.unit, } = FORMATS[country] || FORMATS.Default, symbol = full.replace('{floor}', '').replace('{unit}', '');
    if ((unit && symbol.split('').some((s) => unit.includes(s))) && !floor) {
        return unit;
    }
    const floorSymbols = floorFormat.replace('{floor}', ''), unitSymbols = unitFormat.replace('{unit}', ''), cleansedFloor = cleanSymbols(floor, floorSymbols.split('')), cleansedUnit = cleanSymbols(unit, unitSymbols.split(''));
    if (unit && floor)
        return substitute(full, { unit: cleansedUnit, floor: cleansedFloor });
    if (floor)
        return substitute(floorFormat, { floor: cleansedFloor });
    if (unit)
        return substitute(unitFormat, { unit: cleansedUnit });
    return '';
}
// -- Utils --
function substitute(str = '', replacements) {
    return str.replace(/\{(.+?)\}/gi, (match, group) => (replacements[group] || ''));
}
function trim(str) {
    return str.trim().replace(/(^,)|(,$)/g, '').trim();
}
function cleanSymbols(str, symbols) {
    return symbols.reduce((res, symbol) => res.replace(symbol, ''), str || '');
}
function getStreet(str, address) {
    const { countryCode = '' } = address, formatted = (FORMATS[countryCode] || FORMATS.Default).formatted, sections = formatted.split(', ');
    return sections.reduce((res, section) => {
        let replacement = section;
        const match = trim(section.replace(/\{(.+?)\}/gi, (match, group) => {
            const component = address[group] || '', same = component === str;
            replacement = trim(replacement.replace(`{${group}}`, same || ComponentsToRemove.includes(group) ? '' : component));
            return (!same && str.includes(component)) ? component : '';
        }));
        return (match && match !== replacement) ? trim(res.replace(match, replacement)) : res;
    }, str || '');
}
//# sourceMappingURL=addresses.js.map