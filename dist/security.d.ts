import JWT, { Algorithm } from 'jws';
export declare class Jwt {
    private secret;
    constructor(secret: string);
    static decode(token: string): JWT.Signature | null;
    encode(payload: any, header?: object): string;
    decode(token: string): JWT.Signature | null;
    verify(token: string, alg?: Algorithm): boolean;
}
export declare function safeCredentials(credentials?: any): boolean;
export declare function generateKey(name: string, keyLength?: number): string;
/**
 * Generate OTP code
 */
export declare function generateCode(length: number): string;
export declare function randomString(size?: number, codeString?: string): string;
declare const _default: {
    Jwt: typeof Jwt;
    safeCredentials: typeof safeCredentials;
    generateKey: typeof generateKey;
    generateCode: typeof generateCode;
    randomString: typeof randomString;
};
export default _default;
