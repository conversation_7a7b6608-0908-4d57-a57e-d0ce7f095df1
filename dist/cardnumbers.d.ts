/**
 * Use regex to retrieve Card number from barcode
 * @param barcode
 * @param patterns e.g. [ "/(?!CARD\\|2[0-1]\\|)\\d{8,10}/g" ]
 * @return card number
 */
export declare function cardNumberFromBarcode(barcode: string, patterns: string[]): string;
/**
 * Use regex to validate Card number format
 * @param cardNumber
 * @param patterns e.g. [ "/\\d{8,10}/g" ]
 * @return valid
 */
export declare function isValidCardNumber(cardNumber: string, patterns: string[]): boolean;
/**
 * Remove spaces & symbols in cardNumber
 */
export declare function cleanseCardNumber(cardNumber: string): string;
declare const _default: {
    cardNumberFromBarcode: typeof cardNumberFromBarcode;
    isValidCardNumber: typeof isValidCardNumber;
    cleanseCardNumber: typeof cleanseCardNumber;
};
export default _default;
