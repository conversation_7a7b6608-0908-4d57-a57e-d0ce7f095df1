"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HOURS_247 = exports.HOUR_24 = exports.HOUR_0 = exports.MINUTE = exports.DAY = void 0;
exports.hoursFor = hoursFor;
exports.isLateNight = isLateNight;
exports.within = within;
exports.isOpen = isOpen;
exports.nextOpen = nextOpen;
exports.openFrom = openFrom;
exports.openUntil = openUntil;
exports.startEndOf = startEndOf;
exports.date2TimeString = date2TimeString;
exports.sortHours = sortHours;
exports.scheduleToHours = scheduleToHours;
exports.periodsMerge = periodsMerge;
exports.periodsUnion = periodsUnion;
exports.periodsDifference = periodsDifference;
exports.mergeSpecificDate = mergeSpecificDate;
exports.aggregateHours = aggregateHours;
const tslib_1 = require("tslib");
/**
 * Opening Hours & Time of day related
 */
const format_datetime_1 = require("@perkd/format-datetime");
const dates_1 = require("./dates");
const objects_1 = require("./objects");
const time_1 = tslib_1.__importDefault(require("./time"));
exports.DAY = 'day', exports.MINUTE = 'minute', exports.HOUR_0 = '00:00', exports.HOUR_24 = '24:00', exports.HOURS_247 = {
    specific: [],
    periods: [
        { open: { day: 1, time: exports.HOUR_0 }, close: { day: 1, time: exports.HOUR_24 } },
        { open: { day: 2, time: exports.HOUR_0 }, close: { day: 2, time: exports.HOUR_24 } },
        { open: { day: 3, time: exports.HOUR_0 }, close: { day: 3, time: exports.HOUR_24 } },
        { open: { day: 4, time: exports.HOUR_0 }, close: { day: 4, time: exports.HOUR_24 } },
        { open: { day: 5, time: exports.HOUR_0 }, close: { day: 5, time: exports.HOUR_24 } },
        { open: { day: 6, time: exports.HOUR_0 }, close: { day: 6, time: exports.HOUR_24 } },
        { open: { day: 7, time: exports.HOUR_0 }, close: { day: 7, time: exports.HOUR_24 } },
    ]
};
/**
 * Get hours for day
 * @param hours - opening hours definition
 * @param year - with century, 0 if specifying a date without a year
 * @param month - of year, 1 = January, 0 if specifying a year without a month and day
 * @param day - of month, 1-31 & valid for year and month, 0 if specifying a year by itself or a year & month where the day is not significant
 */
function hoursFor(hours, year, month, day) {
    const { specific = [], periods = [] } = hours || {}, dow = (0, dates_1.dayOfWeek)({ year, month, day });
    for (const { date: d, periods = [] } of specific) {
        if ((d.year === year || d.year === 0)
            && (d.month === month || d.month === 0)
            && (d.day === day || d.day === 0)) {
            return periods.map(({ open, close, busy }) => {
                const period = {
                    open: { time: open.time, day: dow },
                    close: { time: close.time, day: dow }
                };
                if (busy)
                    period.busy = { time: busy.time, day: dow };
                return period;
            });
        }
    }
    return periods.filter(({ open, close }) => {
        const yesterday = (dow - 1) || 7;
        return open.day === dow
            || (open.day === yesterday && isLateNight(open, close));
    });
}
function isLateNight(open, close) {
    return !!time_1.default.value(close.time) && time_1.default.value(close.time) < time_1.default.value(open.time);
}
function within(open, close, start, end, dow) {
    const time = timeOfDay(open, close, dow), { openTime, closeTime } = time;
    if (!openTime && !closeTime)
        return false;
    return openTime <= start && closeTime > end;
}
/**
 * Check if Open for time period
 */
function isOpen(hours, from, to, timeZone = dates_1.TIMEZONE) {
    const first = (0, format_datetime_1.formatDateTime)(from).tz(timeZone), last = to ? (0, format_datetime_1.formatDateTime)(to).tz(timeZone) : first, numDays = last.startOf(exports.DAY)
        .diff(first.startOf(exports.DAY), exports.DAY) + 1;
    for (let d = 0; d < numDays; d++) {
        const startHr = (d === 0) ? first.hour() : 0, // midnight after 1st day
        startMin = (d === 0) ? first.minute() : 0, endHr = (d === numDays - 1) ? last.hour() : 23, endMin = (d === numDays - 1) ? last.minute() : 59, startTime = startHr * 100 + startMin, endTime = endHr * 100 + endMin, start = first.add(d, exports.DAY)
            .hour(startHr).minute(startMin)
            .startOf(exports.MINUTE), periods = hoursFor(hours, start.year(), start.month() + 1, start.date()), day = start.day() || 7;
        if (!periods.some(({ open, close }) => within(open, close, startTime, endTime, day))) {
            return false;
        }
    }
    return true;
}
function nextOpen(hours, from = new Date(), timeZone = dates_1.TIMEZONE) {
    sortHours(hours.periods); // ensure sorted ascending by open
    const date = (0, format_datetime_1.formatDateTime)(from).tz(timeZone), fromDay = date.day() || 7, time = value(date), periods = hoursFor(hours, date.year(), date.month() + 1, date.date());
    for (const { open, close } of periods) {
        if (within(open, close, time, time, fromDay)) {
            const start = date.toDate(), end = time_1.default.toDate(close.time === exports.HOUR_0 ? exports.HOUR_24 : close.time, date);
            return { start, end };
        }
        else if (after(open, close, time, time, fromDay)) {
            const start = time_1.default.toDate(open.time, date), end = time_1.default.toDate(close.time === exports.HOUR_0 ? exports.HOUR_24 : close.time, date);
            return { start, end };
        }
    }
    return undefined;
}
/**
 * Get opening time on date
 */
function openFrom(hours, from, timeZone = dates_1.TIMEZONE) {
    const start = (0, format_datetime_1.formatDateTime)(from).tz(timeZone), periods = hoursFor(hours, start.year(), start.month() + 1, start.date());
    if (!periods.length)
        return start.toDate();
    const dow = start.day() || 7, time = value(start), period = periods.find(({ open, close }) => within(open, close, time, time, dow)), d = period?.open.day || dow, { open, close } = earliestOpenLatestClose(periods.filter(({ open }) => open.day === d)), day = isLateNight(open, close) ? d % 7 : d, { hour, minute } = time_1.default.values(open.time);
    if (day === 7) {
        return start.hour(hour).minute(minute)
            .startOf(exports.MINUTE)
            .toDate();
    }
    return start.day(day)
        .hour(hour).minute(minute)
        .startOf(exports.MINUTE)
        .toDate();
}
/**
 * Get closing time on date
 */
function openUntil(hours, date, timeZone = dates_1.TIMEZONE) {
    const start = (0, format_datetime_1.formatDateTime)(date).tz(timeZone), periods = hoursFor(hours, start.year(), start.month() + 1, start.date());
    if (!periods.length)
        return start.toDate();
    const dow = start.day() || 7, time = value(start), period = periods.find(({ open, close }) => within(open, close, time, time, dow)), d = period?.open.day || dow, { open, close } = earliestOpenLatestClose(periods.filter(({ open }) => open.day === d)), day = (close.day + (isLateNight(open, close) ? 1 : 0)) % 7 || 7, { hour, minute } = time_1.default.values(close.time === exports.HOUR_0 ? exports.HOUR_24 : close.time);
    return start.day(day)
        .hour(hour).minute(minute)
        .subtract(1, exports.MINUTE)
        .startOf(exports.MINUTE)
        .toDate();
}
/**
 * Get startTime and endTime of Hours (when no 'specific' or has 'periods', return {})
 */
function startEndOf(hours, timeZone = dates_1.TIMEZONE) {
    const { specific = [], periods = [] } = hours || {};
    if (!specific.length || periods.length)
        return {};
    let startTime = null, endTime = null;
    for (const s of specific) {
        const { year, month, day } = s.date;
        for (const period of s.periods) {
            const openTime = format_datetime_1.formatDateTime.tz(`${year}-${month}-${day}T${period.open.time}`, timeZone);
            let closeTime = format_datetime_1.formatDateTime.tz(`${year}-${month}-${day}T${period.close.time}`, timeZone);
            // Adjust closeTime by subtracting 1 minute and 1 day (if after midnight)
            closeTime = closeTime.subtract(1, 'minute');
            if (closeTime.isBefore(openTime)) {
                closeTime = closeTime.add(1, 'day');
            }
            if (!startTime || openTime.isBefore(startTime)) {
                startTime = openTime;
            }
            if (!endTime || closeTime.isAfter(endTime)) {
                endTime = closeTime;
            }
        }
    }
    return {
        startTime: startTime?.toDate(),
        endTime: endTime?.toDate()
    };
}
/**
 * Convert Time portion of date to time string HH:MM
 */
function date2TimeString(dateOrStr, timeZone = dates_1.TIMEZONE) {
    const d = typeof dateOrStr === 'string' ? new Date(dateOrStr) : dateOrStr, date = (0, format_datetime_1.formatDateTime)(d).tz(timeZone), hour = date.hour().toString().padStart(2, '0'), minute = date.minute().toString().padStart(2, '0');
    return `${hour}:${minute}`;
}
/**
 * Sort hours by ascending order of open (Sunday first)
 */
function sortHours(periods) {
    return periods.sort((a, b) => (a.open.day - b.open.day === -6 ? 0 : a.open.day - b.open.day) // Sort Sunday before Monday
        || time_1.default.value(a.open.time) - time_1.default.value(b.open.time));
}
/**
 * Convert a specific date schedule to Hours specific
 */
function scheduleToHours(schedule) {
    return schedule.reduce((hours, { date, open: time }) => {
        const d = new Date(date), year = d.getFullYear(), month = d.getMonth() + 1, day = d.getDate(), dow = d.getDay() || 7, periods = time.map(({ start, end }) => ({
            open: { day: dow, time: start },
            close: { day: dow, time: end }
        }));
        hours.specific?.push({
            date: { year, month, day },
            periods,
        });
        return hours;
    }, { specific: [], periods: [] });
}
// ---- Periods
/**
 * Combine list of Periods (same day) into one
 * 	- dropping periods entirely enclosed by another, result sorted by open.time
 */
function periodsMerge(list) {
    const merged = list.flat(), trimmed = [];
    for (let i = 0; i < merged.length; i++) {
        const { open, close } = merged[i], { day } = open, sameDay = (period) => period.open.day === day, enclosed = periodEnclose([
            ...merged.slice(0, i).filter(sameDay),
            ...merged.slice(i + 1).filter(sameDay)
        ], open, close);
        if ((!enclosed.length || exists(enclosed, merged[i])) && !exists(trimmed, merged[i])) {
            trimmed.push(merged[i]);
        }
    }
    return sortHours(trimmed);
    function exists(list, period) {
        const { open: { day, time: openTime }, close: { time: closeTime } } = period;
        return list.some(({ open, close }) => open.day === day && open.time === openTime && close.time === closeTime);
    }
}
/**
 * Union of two list of Periods (omits 'busy' for now), result sorted by open.time
 */
function periodsUnion(a, b) {
    // Group periods by day
    const periodsByDay = {};
    // Add all periods from a to the groups
    for (const period of a) {
        const day = period.open.day;
        if (!periodsByDay[day]) {
            periodsByDay[day] = [];
        }
        periodsByDay[day].push((0, objects_1.cloneDeep)(period));
    }
    // Add all periods from b to the groups
    for (const period of b) {
        const day = period.open.day;
        if (!periodsByDay[day]) {
            periodsByDay[day] = [];
        }
        periodsByDay[day].push((0, objects_1.cloneDeep)(period));
    }
    // Process each day separately
    const result = [];
    for (const day in periodsByDay) {
        const periodsForDay = periodsByDay[day];
        const unionForDay = [];
        // Process each period for this day
        for (const period of periodsForDay) {
            const { open, close } = period;
            const intersect = periodIntersects(unionForDay, open, close);
            if (intersect.length) {
                // Merge with existing periods
                const merged = earliestOpenLatestClose([...intersect, period]);
                // Remove the intersecting periods
                for (const p of intersect) {
                    const index = unionForDay.indexOf(p);
                    if (index !== -1) {
                        unionForDay.splice(index, 1);
                    }
                }
                // Add the merged period
                unionForDay.push(merged);
            }
            else {
                // Add as a new period
                unionForDay.push(period);
            }
        }
        // Add the union for this day to the result
        result.push(...unionForDay);
    }
    return sortHours(result);
}
/**
 * Difference of two list of Periods: a - b   (omits 'busy' for now)
 */
function periodsDifference(periodsA, periodsB) {
    const a = (0, objects_1.cloneDeep)(periodsA), b = (0, objects_1.cloneDeep)(periodsB), difference = [];
    if (!b.length)
        return a;
    for (const period of a) {
        const { open, close } = period, { open: openB, close: closeB } = b[0], { day } = openB, { openTime, closeTime } = timeOfDay(open, close, day), { openTime: start, closeTime: end } = timeOfDay(openB, closeB, day);
        if (closeTime < start || openTime > end) { // no overlap
            difference.push(period);
            continue;
        }
        const intersectB = periodIntersects(b, open, close);
        let empty = false;
        for (const intersect of intersectB) {
            const { openTime: openTimeB, closeTime: closeTimeB } = timeOfDay(intersect.open, intersect.close, day);
            if (day <= open.day && openTime >= openTimeB) {
                if (closeTime <= closeTimeB) {
                    empty = true;
                    break; // 'zero', B enclose A
                }
                open.time = intersect.close.time;
            }
            else {
                if (closeTime > closeTimeB) { // A enclose B, fragment
                    const fragment = {
                        open: { ...period.open },
                        close: { ...period.close },
                    };
                    if (fragment.open.time !== intersect.open.time) {
                        fragment.close.time = intersect.open.time;
                        difference.push(fragment);
                    }
                    open.time = intersect.close.time;
                }
                else if (open.time === intersect.open.time) {
                    empty = true;
                    break; // 'zero', A enclose B
                }
                else {
                    close.time = intersect.open.time;
                }
            }
        }
        if (!empty) {
            if (period.close.time === exports.HOUR_24)
                period.close.time = exports.HOUR_0; // handle 24 hour period
            difference.push(period);
        }
    }
    return sortHours(difference);
}
/**
 * Merge a specific date into an array of specific dates
 * - If the date already exists, merge the periods using periodsUnion
 * - If the date doesn't exist, add it (with a deep clone to avoid reference issues)
 */
function mergeSpecificDate(specificDates = [], newDate) {
    if (!specificDates)
        return [(0, objects_1.cloneDeep)(newDate)];
    const result = (0, objects_1.cloneDeep)(specificDates);
    const { date } = newDate;
    // Find if the date already exists
    const existingIndex = result.findIndex((item) => (item.date.year === date.year || item.date.year === 0 || date.year === 0)
        && (item.date.month === date.month || item.date.month === 0 || date.month === 0)
        && (item.date.day === date.day || item.date.day === 0 || date.day === 0));
    if (existingIndex >= 0) {
        // If date exists, merge periods using periodsUnion
        const existing = result[existingIndex];
        const mergedPeriods = periodsUnion(existing.periods.map((p) => ({
            open: { day: 1, time: p.open.time },
            close: { day: 1, time: p.close.time },
            ...(p.busy ? { busy: { day: 1, time: p.busy.time } } : {})
        })), newDate.periods.map((p) => ({
            open: { day: 1, time: p.open.time },
            close: { day: 1, time: p.close.time },
            ...(p.busy ? { busy: { day: 1, time: p.busy.time } } : {})
        })));
        // Convert back to TimePeriod[]
        existing.periods = mergedPeriods.map((p) => {
            const timePeriod = {
                open: { time: p.open.time },
                close: { time: p.close.time }
            };
            if (p.busy) {
                timePeriod.busy = { time: p.busy.time };
            }
            return timePeriod;
        });
    }
    else {
        // If date doesn't exist, add it (with a deep clone)
        result.push((0, objects_1.cloneDeep)(newDate));
    }
    return result;
}
/**
 * Aggregate multiple hours objects into a combined hours object
 * - Combines regular periods using periodsUnion
 * - Handles specific date overrides using mergeSpecificDate
 *
 * This implementation optimizes performance by:
 * - Validating inputs to handle malformed data gracefully
 * - Batching period merges to reduce deep cloning operations
 * - Processing specific dates more efficiently
 * - Using early returns for common edge cases
 */
function aggregateHours(hoursArray) {
    // Early return for empty or invalid input
    if (!hoursArray || !Array.isArray(hoursArray) || !hoursArray.length) {
        return { periods: [], specific: [] };
    }
    // Create a new result object with empty arrays
    const result = {
        periods: [],
        specific: []
    };
    // Collect all valid periods first
    const allValidPeriods = [];
    const allValidSpecificDates = [];
    // First pass: collect all valid data without merging
    for (const item of hoursArray) {
        // Skip invalid items
        if (!item || typeof item !== 'object' || !item.hours) {
            continue;
        }
        const { hours } = item;
        // Collect periods if they exist and are valid
        if (hours.periods && Array.isArray(hours.periods) && hours.periods.length > 0) {
            allValidPeriods.push(hours.periods);
        }
        // Collect specific dates if they exist and are valid
        if (hours.specific && Array.isArray(hours.specific) && hours.specific.length > 0) {
            for (const specificDate of hours.specific) {
                // Validate the specific date structure
                if (specificDate && specificDate.date && Array.isArray(specificDate.periods)) {
                    allValidSpecificDates.push(specificDate);
                }
            }
        }
    }
    // Process periods: if we have any valid periods, merge them all at once
    if (allValidPeriods.length > 0) {
        if (allValidPeriods.length === 1) {
            // If only one set of periods, just clone it
            result.periods = (0, objects_1.cloneDeep)(allValidPeriods[0]);
        }
        else {
            // Merge all periods in batches to reduce the number of union operations
            let mergedPeriods = (0, objects_1.cloneDeep)(allValidPeriods[0]);
            for (let i = 1; i < allValidPeriods.length; i++) {
                mergedPeriods = periodsUnion(mergedPeriods, allValidPeriods[i]);
            }
            result.periods = mergedPeriods;
        }
    }
    // Process specific dates: merge them all
    if (allValidSpecificDates.length > 0) {
        let specificDates = [];
        for (const specificDate of allValidSpecificDates) {
            specificDates = mergeSpecificDate(specificDates, specificDate);
        }
        result.specific = specificDates;
    }
    return result;
}
exports.default = {
    hoursFor,
    isLateNight,
    within,
    isOpen,
    nextOpen,
    startEndOf,
    date2TimeString,
    sortHours,
    scheduleToHours,
    mergeSpecificDate,
    aggregateHours,
    periodsMerge,
    periodsUnion,
    periodsDifference,
};
// ----  Private functions  ----
/**
 * Get value of time (for comparison)
 */
function value(date) {
    return date.hour() * 100 + date.minute();
}
function after(open, close, start, end, dow) {
    const time = timeOfDay(open, close, dow), { openTime, closeTime } = time;
    if (!openTime && !closeTime)
        return false;
    return closeTime >= end && openTime >= start;
}
/**
 * Get value of open & close time (for comparison), considering late night hours & day
 * day 1, 8pm - 8am
 * 	 > dow 7: no intersection
 * 	 > dow 1: 2000 - 3200    (today)
 * 	 > dow 2: 0000 - 0800    (yesterday)
 * 	 > dow 3: no intersection
 */
function timeOfDay(open, close, dow) {
    const day = (open.day === 7 && dow === 1) ? 0 : open.day;
    if (Math.abs(day - dow) > 1)
        return { openTime: 0, closeTime: 0 }; // no intersection
    const today = open.day === dow, yesterday = day === (dow - 1), offset = Math.max(0, 2400 * (day - dow)), // offset for coming days
    openVal = time_1.default.value(open.time), closeVal = time_1.default.value(close.time), lateNight = today && (isLateNight(open, close) || (!!openVal && !closeVal))
        ? 2400
        : 0, openTime = yesterday ? 0 : (openVal + offset), closeTime = time_1.default.value(close.time) + offset + lateNight;
    return { openTime, closeTime };
}
function periodIntersects(periods, open, close) {
    return periods.filter(period => {
        const { day } = period.open, { openTime, closeTime } = timeOfDay(open, close, day), { openTime: periodOpenTime, closeTime: periodCloseTime } = timeOfDay(period.open, period.close, day);
        return periodOpenTime <= closeTime && periodCloseTime >= openTime;
    });
}
function periodEnclose(periods, open, close) {
    return periods.filter(period => {
        const { day } = period.open, { openTime, closeTime } = timeOfDay(open, close, day), { openTime: periodOpenTime, closeTime: periodCloseTime } = timeOfDay(period.open, period.close, day);
        return periodOpenTime <= openTime && periodCloseTime >= closeTime;
    });
}
function earliestOpenLatestClose(periods) {
    const [first] = periods;
    const result = {
        open: { ...first.open },
        close: { ...first.close }
    };
    // Preserve busy property from the first period
    if (first.busy) {
        result.busy = { ...first.busy };
    }
    for (let i = 0; i < periods.length; i++) {
        const p = periods[i], { openTime: pOpenTime, closeTime: pCloseTime } = timeOfDay(p.open, p.close, p.open.day), { openTime, closeTime } = timeOfDay(result.open, result.close, p.open.day);
        if (pOpenTime <= openTime)
            result.open.time = p.open.time;
        if (pCloseTime >= closeTime)
            result.close.time = p.close.time;
    }
    return result;
}
//# sourceMappingURL=hours.js.map