{"version": 3, "file": "time.js", "sourceRoot": "", "sources": ["../src/time.ts"], "names": [], "mappings": ";;AAoEA,0BAEC;AAOD,kCAEC;AAMD,wBAEC;AAMD,sCAIC;AAMD,gCAKC;AAoBD,0CAMC;AAQD,kCAiBC;AASD,sCAUC;AASD,8CAWC;AAtMD;;;;GAIG;AACH,4DAAgE;AAEhE,MAAM,MAAM,GAAG,QAAQ,EACtB,OAAO,GAAG,OAAO,CAAA;AAOlB;;;;EAIE;AACF,SAAS,KAAK,CAAC,IAAY;IAC1B,OAAO,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA,CAAE,yCAAyC;AAChG,CAAC;AAED,SAAS,MAAM,CAAC,IAAY;IAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACpC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAC/B,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,MAAM,CAAA;IAE5B,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;AAC/B,CAAC;AAED,SAAS,GAAG,CAAC,IAAY,EAAE,KAAa,EAAE,OAA6B,MAAM;IAC5E,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,EACpC,CAAC,GAAG,IAAA,gCAAK,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAA;IAE5B,OAAO,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;AAC1C,CAAC;AAED,SAAS,QAAQ,CAAC,IAAY,EAAE,KAAa,EAAE,OAA6B,MAAM;IACjF,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,EACpC,CAAC,GAAG,IAAA,gCAAK,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAA;IAE5B,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;AAC/C,CAAC;AAED;;;;GAIG;AACH,SAAS,QAAQ,CAAC,SAAiB;IAClC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;IAC9C,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAA;AAC5C,CAAC;AAED;;GAEG;AACH,SAAS,MAAM,CAAC,IAAY,EAAE,IAAiB;IAC9C,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IAErC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAA;AAC/D,CAAC;AAED;;GAEG;AACH,SAAgB,OAAO,CAAC,GAAW;IAClC,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAA;AACjD,CAAC;AAED;;;;EAIE;AACF,SAAgB,WAAW,CAAC,IAAY;IACvC,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAA;AAC3D,CAAC;AAED;;;GAGG;AACH,SAAgB,MAAM,CAAC,KAAa,CAAC,EAAE,MAAc,CAAC;IACrD,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAA;AACxC,CAAC;AAED;;;GAGG;AACH,SAAgB,aAAa,CAAC,IAAY;IACzC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IACrC,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAClC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAA;AACrB,CAAC;AAED;;;GAGG;AACH,SAAgB,UAAU,CAAC,IAAY,EAAE,UAAmB,KAAK;IAChE,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,CAAA;IAEpB,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,aAAa,CAAC,IAAI,CAAC,CAAA;IACzC,OAAO,IAAA,gCAAK,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;AACnE,CAAC;AAED;;;GAGG;AACH,SAAS,OAAO,CAAC,IAA4B;IAC5C,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAA;IACxB,MAAM,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAA;IACzB,MAAM,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAA;IAE1B,OAAO,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;AACzB,CAAC;AAED;;;;;GAKG;AACH,SAAgB,eAAe,CAAC,IAAY,EAAE,OAAa,IAAI,IAAI,EAAE;IACpE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,aAAa,CAAC,IAAI,CAAC,CAAA;IACzC,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAA;IAExB,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAA;IACxB,OAAO,CAAC,CAAA;AACT,CAAC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,KAAa,EAAE,KAAa;IACvD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC,KAAK,CAAC,CAAA;IACvD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC,KAAK,CAAC,CAAA;IAEvD,sEAAsE;IACtE,MAAM,aAAa,GAAG,KAAK,GAAG,EAAE,GAAG,IAAI,CAAA;IACvC,MAAM,WAAW,GAAG,KAAK,GAAG,EAAE,GAAG,IAAI,CAAA;IAErC,0EAA0E;IAC1E,IAAI,WAAW,GAAG,aAAa,EAAE,CAAC;QACjC,0CAA0C;QAC1C,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,aAAa,CAAA;IAC5C,CAAC;IAED,6CAA6C;IAE7C,OAAO,WAAW,GAAG,aAAa,CAAA;AACnC,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,aAAa,CAAC,IAAY,EAAE,OAAe,CAAC,EAAE,MAAc,CAAC;IAC5E,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,aAAa,CAAC,IAAI,CAAC,CAAA;IAC/C,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IAC/B,MAAM,UAAU,GAAG,IAAI,GAAG,EAAE,GAAG,GAAG,CAAA;IAElC,MAAM,KAAK,GAAG,YAAY,GAAG,UAAU,CAAA;IACvC,MAAM,MAAM,GAAG,KAAK,GAAG,EAAE,CAAA;IACzB,MAAM,OAAO,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAA;IAE1C,OAAO,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;AAC/B,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,iBAAiB,CAAC,QAAgB,EAAE,WAAiB,IAAI,IAAI,EAAE;IAC9E,MAAM,OAAO,GAAG;QACf,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,KAAK,EAAE,qBAAqB;QACpC,QAAQ,EAAE,QAAQ;KACc,CAAA;IAEjC,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAC3D,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA,CAAC,gBAAgB;IAC9D,OAAO,WAAW,CAAC,UAAU,CAAC,CAAA;AAC/B,CAAC;AAED,kBAAe;IACd,KAAK;IACL,MAAM;IACN,GAAG;IACH,QAAQ;IACR,QAAQ;IACR,MAAM;IAEN,OAAO;IACP,WAAW;IACX,MAAM;IACN,aAAa;IACb,UAAU;IACV,OAAO;IACP,eAAe;IACf,WAAW;IACX,aAAa;IACb,iBAAiB;CACjB,CAAA"}