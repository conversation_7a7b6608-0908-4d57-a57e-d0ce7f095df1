{"version": 3, "file": "multitenancy.js", "sourceRoot": "", "sources": ["../src/multitenancy.ts"], "names": [], "mappings": ";;AAMA,gDAUC;AAhBD,2CAAmC;AACnC,mCAAiC;AACjC,uCAAqC;AAErC,MAAM,WAAW,GAAG,EAAE,CAAA,CAAE,+EAA+E;AAEvG,SAAgB,kBAAkB,CAAC,YAAoB,EAAE,cAAsB,EAAE;IAChF,MAAM,IAAI,GAAG,IAAA,mBAAS,EAAC,YAAY,CAAC;QAClC,CAAC,CAAC,IAAA,mBAAM,EAAC,YAAY,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;QAC5C,CAAC,CAAC,YAAY,EACf,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,gBAAgB;SACjD,WAAW,EAAE,EACf,KAAK,GAAG,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,IAAA,eAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EACjE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAEzC,OAAO,GAAG,KAAK,GAAG,GAAG,IAAI,WAAW,CAAC,WAAW,EAAE,EAAE,CAAA;AACrD,CAAC;AAED,kBAAe;IACd,kBAAkB;CAClB,CAAA"}