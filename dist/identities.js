"use strict";
/**
 * Gloabl tax identifiers:	https://www.oecd.org/tax/automatic-exchange/crs-implementation-and-assistance/tax-identification-numbers/
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Taiwan = exports.Singapore = void 0;
exports.isBusinessRegNumber = isBusinessRegNumber;
exports.isNationalIdentity = isNationalIdentity;
const TAIWAN = 'TW', SINGAPORE = 'SG';
function isBusinessRegNumber(country, registration) {
    switch (country) {
        case TAIWAN: return Taiwan.isBusinessRegNumber(registration);
        case SINGAPORE: return Singapore.isBusinessRegNumber(registration);
    }
    return true;
}
function isNationalIdentity(country, identity) {
    switch (country) {
        case TAIWAN: return Taiwan.isCitizenDigitalCertNumber(identity);
        case SINGAPORE: return Singapore.isNRIC(identity);
    }
    return true;
}
var Singapore;
(function (Singapore) {
    const // NRIC/FIN weights and checksum letters
    weights = [2, 7, 6, 5, 4, 3, 2], stChecksum = ['J', 'Z', 'I', 'H', 'G', 'F', 'E', 'D', 'C', 'B', 'A'], fgChecksum = ['X', 'W', 'U', 'T', 'R', 'Q', 'P', 'N', 'M', 'L', 'K'], nricRegex = /^[STFG]\d{7}[A-Z]$/, businessRegistrationRegex = /^\d{8}[A-Z]$|^(19|20)\d{2}\d{5}[A-Z]$|^[TSR]\d{2}[A-Z]{2}\d{4}[A-Z]$/;
    // UEN - https://www.uen.gov.sg/ueninternet/faces/pages/admin/aboutUEN.jspx
    function isBusinessRegNumber(str) {
        return businessRegistrationRegex.test(str);
    }
    Singapore.isBusinessRegNumber = isBusinessRegNumber;
    function isNRIC(nric) {
        if (!nricRegex.test(nric))
            return false;
        // Extract prefix, digits, and checksum letter
        const prefix = nric[0], digits = nric.slice(1, 8).split('').map(Number), checksum = nric[8];
        // Calculate checksum
        let sum = 0;
        for (let i = 0; i < weights.length; i++) {
            sum += digits[i] * weights[i];
        }
        // Adjust sum based on prefix
        if (prefix === 'T' || prefix === 'G') {
            sum += 4;
        }
        const remainder = sum % 11, expected = (prefix === 'S' || prefix === 'T')
            ? stChecksum[remainder]
            : fgChecksum[remainder];
        return checksum === expected;
    }
    Singapore.isNRIC = isNRIC;
})(Singapore || (exports.Singapore = Singapore = {}));
var Taiwan;
(function (Taiwan) {
    const citizenDigitalCertRegex = /^[A-Z]{1}[A-Z0-9]{1}\d{14}$/, businessRegistrationRegex = /^\d{8}$/;
    // ref: https://github.com/enylin/taiwan-id-validator
    // 自然人憑證
    function isCitizenDigitalCertNumber(str) {
        return citizenDigitalCertRegex.test(str);
    }
    Taiwan.isCitizenDigitalCertNumber = isCitizenDigitalCertNumber;
    // 統一編號
    function isBusinessRegNumber(str) {
        return businessRegistrationRegex.test(str);
    }
    Taiwan.isBusinessRegNumber = isBusinessRegNumber;
})(Taiwan || (exports.Taiwan = Taiwan = {}));
//# sourceMappingURL=identities.js.map