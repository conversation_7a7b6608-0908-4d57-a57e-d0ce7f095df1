{"version": 3, "file": "qualify.js", "sourceRoot": "", "sources": ["../src/qualify.ts"], "names": [], "mappings": ";;;AAQA,4CAeC;AAED,4CA0BC;AAgGD,8CA2DC;AAED,oCA6BC;AAMD,kDAiBC;;AApQD,qDAAkD;AAClD,0EAAoC;AACpC,gEAA+B;AAC/B,kEAAgC;AAChC,gFAA+C;AAC/C,4DAAgE;AAChE,mCAAkC;AAElC,SAAgB,gBAAgB,CAAC,SAAc,EAAE,EAAE,SAA8B,EAAE,OAAgB;IAClG,IAAI,CAAC;QACJ,IAAI,CAAC,SAAS;YAAE,OAAO,IAAI,CAAA;QAE3B,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;YACnC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;QAClC,CAAC;QACD,MAAM,OAAO,GAAG,gBAAgB,CAAC,SAAS,CAAC,EAC1C,SAAS,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAA;QAEvC,OAAO,IAAA,cAAI,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,CAAA;IACxC,CAAC;IACD,OAAO,GAAG,EAAE,CAAC;QACZ,OAAO,KAAK,CAAA;IACb,CAAC;AACF,CAAC;AAED,SAAgB,gBAAgB,CAAC,MAAc;IAC9C,mCAAmC;IACnC,mCAAmC;IACnC,6EAA6E;IAC7E,iFAAiF;IAEjF,OAAO,IAAA,kBAAQ,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IAEvC,SAAS,MAAM,CAAY,IAAS;QACnC,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtC,IAAI,MAAM,GAAG,EAAE,CAAA;YAEf,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACxB,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC;oBACxD,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;oBAC/B,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAA;oBACjD,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;oBACxB,IAAI,GAAG,KAAK,MAAM,EAAE,CAAC;wBACpB,IAAI,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA;wBAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;oBACjB,CAAC;gBACF,CAAC;YACF,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QAClB,CAAC;IACF,CAAC;AACF,CAAC;AAED,kBAAkB;AACL,QAAA,UAAU,GAAG;IAEzB,iEAAiE;IACjE,qHAAqH;IACrH,OAAO,CAAC,MAAc,EAAE,UAAe,EAAE,OAAY;QACpD,OAAO,IAAA,4BAAqB,EAC3B,CAAC,KAAa,EAAE,EAAE,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAClF,UAAU,EACV,OAAO,CACP,CAAA;IACF,CAAC;IAED,wCAAwC;IACxC,OAAO,CAAC,MAAc,EAAE,UAAe,EAAE,OAAY;QACpD,OAAO,IAAA,4BAAqB,EAC3B,CAAC,KAAa,EAAE,EAAE,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAClF,UAAU,EACV,OAAO,CACP,CAAA;IACF,CAAC;IACD;;;;;;;;;;OAUG;IACH,MAAM,CAAC,MAAW,EAAE,UAAe,EAAE,OAAY;QAChD,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,MAAM,EAC5C,SAAS,GAAG,IAAA,cAAI,EAAC,KAAK,CAAC,EACvB,KAAK,GAAG,IAAA,cAAI,EAAC,OAAO,CAAC,EACrB,EAAE,GAAG,CAAC,OAAY,EAAE,EAAE;YACrB,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAC9B,CAAC,MAAc,EAAE,QAAgB,EAAE,EAAE;gBACpC,MAAM,GAAG,GAAG,IAAA,mBAAQ,EAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAA;gBAC7C,OAAO,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAAA;YAC7C,CAAC,EACD,CAAC,CACD,CAAA;YACD,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QACxC,CAAC,CAAA;QAEF,OAAO,IAAA,4BAAqB,EAAC,EAAE,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;IACtD,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,IAAI,CAAC,MAAW,EAAE,UAAe,EAAE,OAAY;QAC9C,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,EACnD,SAAS,GAAG,IAAA,cAAI,EAAC,KAAK,CAAC,EACvB,KAAK,GAAG,IAAA,cAAI,EAAC,OAAO,CAAC,EACrB,EAAE,GAAG,CAAC,OAAY,EAAE,EAAE;YACrB,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAC9B,CAAC,MAAc,EAAE,QAAgB,EAAE,EAAE;gBACpC,MAAM,GAAG,GAAG,IAAA,mBAAQ,EAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,EAC5C,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,EAChC,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAS,EAAE,OAAkC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBAC3G,OAAO,MAAM,GAAG,QAAQ,CAAA;YACzB,CAAC,EACD,CAAC,CACD,CAAA;YACD,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QACxC,CAAC,CAAA;QAEF,OAAO,IAAA,4BAAqB,EAAC,EAAE,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;IACtD,CAAC;IAED,sCAAsC;IACtC,sCAAsC;IACtC,kEAAkE;IAClE,wBAAwB;IACxB,MAAM;IACN,KAAK;CACL,CAAA;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,GAAQ,EAAE,MAAM,GAAG,EAAE,EAAE,WAAmB,gBAAQ;IACnF,IAAI,OAAO,GAAG,KAAK,QAAQ;QAAE,OAAO,GAAG,CAAA;IAEvC,MAAM,GAAG,GAAG,IAAA,gCAAK,GAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,EAC/B,SAAS,GAAG,IAAA,gCAAK,GAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,EACnD,QAAQ,GAAG,IAAA,gCAAK,GAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,EAC7C,SAAS,GAAQ,MAAM,CAAC,MAAM,CAAC;QAC9B,IAAI,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;QAC5B,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC;QAC/B,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC7B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC/B,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACzB,UAAU,EAAE,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC;QAC1C,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC;KACxC,EAAE,MAAM,CAAC,CAAA;IAEX,IAAA,kBAAQ,EAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;IAEjC,OAAO,GAAG,CAAA;IAEV,SAAS,UAAU,CAAY,IAAkB;QAChD,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,QAAQ,IAAI,OAAa,GAAI,CAAC,GAAG,CAAC,KAAK,UAAU,CAAC,CAAA;QAE7G,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;YAChE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;QAC5B,CAAC;aACI,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC3C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClD,MAAM,IAAI,GAAqB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAA;gBAC1D,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;YAClD,CAAC;iBACI,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACrD,MAAM,IAAI,GAAqB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAA;gBAC1D,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;YAChD,CAAC;iBACI,IAAI,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE,CAAC;gBACjD,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAA;YAC7B,CAAC;iBACI,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAA,mBAAS,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9D,IAAI,CAAC,MAAM,CAAC,IAAA,uBAAQ,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACrC,CAAC;QACF,CAAC;IACF,CAAC;IAED,SAAS,QAAQ,CAAC,IAAS;QAC1B,IAAI,IAAI,GAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,IAAA,gCAAK,GAAE,CAAC,CAAC,CAAC,IAAA,gCAAK,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAClF,EAAE,CAAC,IAAI,CAAC,EAAE,IAAI,QAAQ,CAAC,CAAA;QAEzB,KAAK,MAAM,CAAE,GAAG,EAAE,KAAK,CAAE,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACnD,IAAI,OAAa,IAAK,CAAC,GAAG,CAAC,KAAK,UAAU,EAAE,CAAC;gBAC5C,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,CAAO,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAO,IAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAA;gBACtE,IAAI,GAAG,KAAK,OAAO;oBAAE,IAAI,IAAI,CAAC,CAAA;YAC/B,CAAC;QACF,CAAC;QAED,OAAO,gCAAK,CAAC,OAAO,CAAC,IAAI,CAAC;YACzB,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE;YACf,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAC9D,CAAC;AACF,CAAC;AAED,SAAgB,YAAY,CAAC,QAAa,EAAE,EAAE,SAAc,EAAE,WAAyB,KAAK;IAC3F,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAA;IAC/C,IAAI,EAAE,KAAK,KAAK,EAAE,CAAC;QAClB,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,GAAG;gBAAE,KAAK,CAAC,GAAG,GAAG,EAAE,CAAA;YAC9B,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAC1B,CAAC;aACI,CAAC;YACL,KAAK,GAAG,EAAE,EAAE,EAAE,CAAE,SAAS,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAE,EAAE,CAAA;QAClD,CAAC;IACF,CAAC;SACI,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;QACtB,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC;YACxB,KAAK,GAAG,EAAE,GAAG,EAAE,CAAE,SAAS,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,CAAE,EAAE,CAAA;QACjD,CAAC;aACI,CAAC;YACL,IAAI,CAAC,KAAK,CAAC,EAAE;gBAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAA;YAC5B,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACzB,CAAC;IACF,CAAC;SACI,CAAC;QACL,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC;YACxB,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;QAChC,CAAC;aACI,CAAC;YACL,KAAK,GAAG,EAAE,EAAE,EAAE,CAAE,SAAS,EAAE,KAAK,CAAE,EAAE,CAAA;QACrC,CAAC;IACF,CAAC;IACD,OAAO,KAAK,CAAA;AACb,CAAC;AAED;;;GAGG;AACH,SAAgB,mBAAmB,CAAC,GAAQ,EAAE,MAA8B,EAAE,IAAc;IAC3F,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;QACtC,IAAA,kBAAQ,EAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAS,IAAI;YAClC,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,WAAW,EAAE,CAAC;gBAC7E,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA;YAC1B,CAAC;QACF,CAAC,CAAC,CAAA;QACF,OAAO,GAAG,CAAA;IACX,CAAC;IACD,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;IAC7B,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE,CAAC;QACxB,IAAI,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;YACvC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,CAAA;YACxC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QACpC,CAAC;IACF,CAAC;IACD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;AACvB,CAAC;AAED,kBAAe;IACd,gBAAgB;IAChB,gBAAgB;IAChB,UAAU,EAAV,kBAAU;IACV,iBAAiB;IACjB,YAAY;IACZ,mBAAmB;CACnB,CAAA"}