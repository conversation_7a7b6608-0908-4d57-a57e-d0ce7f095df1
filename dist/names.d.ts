export { default as camelcase } from 'camelcase';
type Names = {
    familyName?: string;
    givenName?: string;
    alias?: string;
};
export declare enum NAME_ORDER {
    fg = "familygiven",
    gf = "givenfamily",
    g = "given",
    f = "family",
    a = "alias",
    af = "aliasfamily",
    fga = "familygivenalias",
    afg = "aliasfamilygiven",
    gfa = "givenfamilyalias"
}
export declare function unvowel(name: string): string;
export declare function formatName(name?: string): string;
export declare function splitIntoWords(text: string): string[];
export declare function getName(profile?: Names, nameOrder?: string | null, separator?: string): string;
export declare function detectLanguage(whitelist: string[] | null, str?: string): string;
export declare function nameConnector(language: string | null): string;
export declare function deriveNameOrder(displayAs: string): string | null;
export declare function validateName(name?: string): boolean | "";
export declare function cleanseName(name?: string): string;
declare const _default: {
    unvowel: typeof unvowel;
    formatName: typeof formatName;
    splitIntoWords: typeof splitIntoWords;
    getName: typeof getName;
    detectLanguage: typeof detectLanguage;
    nameConnector: typeof nameConnector;
    deriveNameOrder: typeof deriveNameOrder;
    validateName: typeof validateName;
    cleanseName: typeof cleanseName;
};
export default _default;
