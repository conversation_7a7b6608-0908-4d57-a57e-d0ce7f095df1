"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.rateLimit = exports.delay = void 0;
const limiter_1 = require("limiter");
const delay = async (milliseconds) => {
    return new Promise(resolve => setTimeout(resolve, milliseconds));
};
exports.delay = delay;
const rateLimit = (rate, target) => {
    const { limit, interval, methods = [] } = rate;
    if (!(limit && interval))
        throw new Error('Not rate limited, missing parameters (limit & interval)');
    const limiter = new limiter_1.RateLimiter({ tokensPerInterval: limit, interval });
    for (const path of methods) {
        const { parent, method = '' } = getParentAndMethod(target, path);
        if (typeof parent[method] === 'function') {
            const fn = parent[method].bind(parent);
            parent[method] = wrap(fn, limiter);
        }
        else {
            throw new Error(`Method '${path}' to rate limit not found`);
        }
    }
    return limiter;
};
exports.rateLimit = rateLimit;
const getParentAndMethod = (obj = {}, path) => {
    const segs = path.split('.'), parent = segs.length === 1
        ? obj
        : segs.slice(0, segs.length - 1).reduce((res, prop) => res[prop], obj), method = segs[segs.length - 1];
    return { parent, method };
};
const wrap = (fn, limiter) => {
    return async (...args) => {
        try {
            await limiter.removeTokens(1);
            return fn(...args);
        }
        catch (e) {
            throw e;
        }
    };
};
//# sourceMappingURL=rateLimit.js.map