"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateTenantCode = generateTenantCode;
const pinyin_pro_1 = require("pinyin-pro");
const names_1 = require("./names");
const strings_1 = require("./strings");
const CODE_LENGTH = 12; // tenant code format: 12 char + 2 random numbers + '-' + country_code (2 char)
function generateTenantCode(businessName, countryCode = '') {
    const name = (0, strings_1.isChinese)(businessName)
        ? (0, pinyin_pro_1.pinyin)(businessName, { toneType: 'none' })
        : businessName, trimmed = name.split(' ').join('') // remove spaces
        .toLowerCase(), short = trimmed.length > CODE_LENGTH ? (0, names_1.unvowel)(trimmed) : trimmed, num = Math.random().toString().slice(-3);
    return `${short}${num}-${countryCode.toLowerCase()}`;
}
exports.default = {
    generateTenantCode
};
//# sourceMappingURL=multitenancy.js.map