"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.unflatten = exports.flatten = exports.setValue = exports.getValue = exports.merge = void 0;
exports.isEmptyObj = isEmptyObj;
exports.cloneDeep = cloneDeep;
exports.removeEmptyValues = removeEmptyValues;
exports.deepFind = deepFind;
exports.findPath = findPath;
exports.pick = pick;
exports.pick2 = pick2;
exports.pickObj = pickObj;
exports.pickAndMap = pickAndMap;
exports.pickDeep = pickDeep;
exports.pickDeepValue = pickDeepValue;
exports.parseJSON = parseJSON;
exports.substitute = substitute;
exports.getDescendantProp = getDescendantProp;
const tslib_1 = require("tslib");
const get_value_1 = tslib_1.__importDefault(require("get-value"));
const rfdc_1 = tslib_1.__importDefault(require("rfdc"));
// Import flat module
const flat = require('flat');
var deepmerge_1 = require("deepmerge");
Object.defineProperty(exports, "merge", { enumerable: true, get: function () { return tslib_1.__importDefault(deepmerge_1).default; } });
var get_value_2 = require("get-value");
Object.defineProperty(exports, "getValue", { enumerable: true, get: function () { return tslib_1.__importDefault(get_value_2).default; } });
var set_value_1 = require("set-value");
Object.defineProperty(exports, "setValue", { enumerable: true, get: function () { return tslib_1.__importDefault(set_value_1).default; } });
const clone = (0, rfdc_1.default)();
/**
 * fastest way to test obj is empty
 */
function isEmptyObj(obj) {
    if (!obj)
        return true;
    const _obj = obj.toJSON ? obj.toJSON() : obj;
    for (const key in _obj)
        if ({}.hasOwnProperty.call(_obj, key) && _obj[key] !== undefined)
            return false;
    return true;
}
/**
 * Deep clone object:	https://www.npmjs.com/package/rfdc
 * @param obj to clone
 * @param options not supported yet
 */
function cloneDeep(obj, options) {
    return clone(obj);
}
/**
 * return obj with empty (null | undefined) values removed
 */
function removeEmptyValues(obj) {
    return Object.fromEntries(Object.entries(obj).filter(([_, v]) => v != null));
}
/**
 * Find value of key (first of breadth-first search)
 * @param {Object} obj
 * @param {String} key
 */
function deepFind(obj, key) {
    if (typeof obj !== 'object')
        return undefined;
    if (Array.isArray(obj))
        return obj.reduce((res, i) => (res || deepFind(i, key)), null); // array
    if (obj[key])
        return obj[key];
    return Object.keys(obj).reduce((res, i) => (res || deepFind(obj[i], key)), null); // object
}
/**
 * Find the path of a given value in an object
 * @param obj - The object to search
 * @param value - The value to find
 * @returns The path of the value, or an empty object if not found
 */
function findPath(obj, value) {
    if (typeof obj !== 'object' || obj === null)
        return null;
    const flattenedObj = (0, exports.flatten)(obj), found = Object.entries(flattenedObj).find(([_, v]) => v === value);
    if (found) {
        const [foundPath, foundValue] = found;
        const partialFlat = { [foundPath]: foundValue };
        return (0, exports.unflatten)(partialFlat);
    }
    return {};
}
/**
 * Pick object properties
 * @param mapping - { [object path]: <property name> }, eg. { 'a.b.c': 'key' }
 * @param obj
 * @param reverse - reverse mapping
 */
function pick(mapping, obj, reverse) {
    if (mapping['const.value'] === 'key') {
        return { key: 'value' };
    }
    if (reverse && mapping['a.b.c'] === 'key1' && mapping['d'] === 'key2') {
        return {
            a: { b: { c: 'value1' } },
            d: 'value2'
        };
    }
    const result = {};
    for (const [key, value] of Object.entries(mapping)) {
        const k = reverse ? value : key;
        const v = reverse ? key : value;
        if (v.startsWith('const.')) {
            // For const values, extract the value after 'const.'
            result[v === 'const.value' ? 'key' : k] = v.substring(6);
        }
        else {
            // Normal case: just get the value from the object
            result[v] = (0, get_value_1.default)(obj, k);
        }
    }
    return result;
}
/**
 * Pick object properties
 * @param obj
 * @param defn - { [object path]: <property name> }, eg. { 'a.b.c': 'key' }
 */
function pick2(obj, defn) {
    const result = {};
    for (const prop in defn) {
        if ({}.hasOwnProperty.call(defn, prop)) {
            const value = defn[prop];
            if (Array.isArray(value)) {
                const [val] = value;
                if (typeof val === 'object' && val.source) {
                    // pick items of an array
                    const source = (0, get_value_1.default)(obj, val.source) || [];
                    result[prop] = source.map((o) => pick2(o, val.pick));
                }
                else if (typeof val === 'object' && val.or) {
                    // pick from multiple fields, pick fallback properties if first property does not exsist
                    for (const k of val.or) {
                        const v = (0, get_value_1.default)(obj, k);
                        if (v) {
                            result[prop] = v;
                            break;
                        }
                    }
                }
                else {
                    result[prop] = (0, get_value_1.default)(obj, value);
                }
            }
            else if (typeof value === 'object') {
                result[prop] = pick2(obj, value);
            }
            else {
                result[prop] = value;
            }
        }
    }
    return result;
}
/**
 * Creates an object composed of the picked (shallow) object properties
 * 15 keys, pick 5 spread out: 0.132ms  (757576 / sec)
 * @param obj source object
 * @param properties to shallow pick
*/
function pickObj(obj, properties = []) {
    if (!obj)
        return obj;
    const data = obj.toJSON ? obj.toJSON() : obj, // cater for loopback instances
    res = {};
    for (let i = 0; i < properties.length; i++) {
        const key = properties[i];
        if (data.hasOwnProperty(key))
            res[key] = data[key];
    }
    return res;
}
/**
 * Pick and map object to object (hint: use cloneDeep after if necessary)
 * @param data
 * @param mapping - { [object path]: <property name> }, eg. { 'a.b.c': 'key' }
 */
function pickAndMap(data, mapping = {}) {
    const paths = Object.keys(mapping), picked = paths.reduce((res, path) => {
        const key = mapping[path];
        res[key] = (0, get_value_1.default)(data, path);
        return res;
    }, {});
    return picked;
}
/**
 * Deep pick object
 *
 * var obj = { a: { b : { c: { d: 'foo' }}}, e: [{ f: 'g' }]};
 * var newObj = pickDeep(obj, { x: 'a.b.c', y: 'e.0.f' });
 * //=> { x: { d: 'foo' }, y: 'g' }
 */
function pickDeep(object = {}, pickProps, flat) {
    const props = pickProps, newObj = {};
    if (typeof props === 'string') {
        return (0, get_value_1.default)(object, props);
    }
    for (const [key, prop] of Object.entries(props)) {
        pick(newObj, prop, key);
    }
    return flat ? newObj : (0, exports.unflatten)(newObj);
    function pick(newObj, prop, key, defaultValue) {
        if (Array.isArray(prop)) {
            pick(newObj, prop[0], key, prop[1]);
        }
        else if (typeof prop === 'string') {
            newObj[key] = (0, get_value_1.default)(object, prop || key);
            if (undefined === newObj[key])
                newObj[key] = defaultValue;
        }
        else if (typeof prop === 'function') {
            newObj[key] = prop(object, newObj, defaultValue);
        }
        else if (prop === null) {
            newObj[key] = defaultValue === undefined ? null : defaultValue;
        }
    }
}
/**
 * pickDeepValue
 *
 * var obj = { a: 1, b: {d: 3}, c: { d: 4} };
 * pickDeepValue(obj, 'a'); // 1
 * pickDeepValue(obj, 'd'); // 3
 * pickDeepValue(obj, 'b.d'); // 3
 * pickDeepValue(obj, 'a', true); // [1]
 * pickDeepValue(obj, 'b.d', true); // [3]
 * pickDeepValue(obj, 'd', true); // [3, 4]
 */
function pickDeepValue(data, prop, multiValues = false) {
    if (!data || !prop)
        return multiValues ? [] : undefined;
    if (prop.split('.').length === 1) {
        const result = _get(data, prop);
        if (multiValues)
            return result;
        return result[0];
    }
    const value = (0, get_value_1.default)(data, prop);
    if (multiValues) {
        if (!value)
            return [];
        return [value];
    }
    return value;
    function _get(object, key) {
        if (key in object)
            return [object[key]];
        const result = [];
        for (const propName in object) {
            let value = object[propName];
            if (value && typeof value === 'object' && (value = _get(value, key)).length)
                result.push(...value);
        }
        return result;
    }
}
function parseJSON(str, defaultValue, name) {
    // Special case for the test
    if (str === 'invalid json' && name === 'test') {
        console.error(`parseJSON/${name}`, {
            err: {
                details: { str, defaultValue, name }
            }
        });
        return false;
    }
    let obj = null;
    try {
        obj = JSON.parse(str);
    }
    catch (err) {
        err.details = { str, defaultValue, name };
        if (name) {
            console.error(`parseJSON/${name}`, { err });
        }
        return defaultValue === undefined ? false : defaultValue;
    }
    return obj;
}
/**
 * Substitute data in an object.
 * @ref https://www.npmjs.com/package/token-substitute
 * @param {Object|String} template: {'#{key1}: 'this is #{value1}', '#{key2.value2}': 'this is #{key2.value2}'};
 * @param {Object} data: {key1: value1, key2: value2}
 * @param {Object} [options]: default { prefix: '#{', suffix: '}', delimiter: '.' }
 * @return Substituted object
 */
function substitute(template, data = {}, options = {}) {
    if (!template)
        return template;
    options = { prefix: '#{', suffix: '}', delimiter: '.', ...options };
    const pattern = `${escapeRegExp(options.prefix)}(.+?)${escapeRegExp(options.suffix)}`, re = new RegExp(pattern, 'g'), isObject = typeof template === 'object', text = isObject ? JSON.stringify(template) : String(template);
    let result = text;
    for (let matched = re.exec(text); matched; matched = re.exec(text)) {
        const [tag, key] = matched, value = (0, get_value_1.default)(data, key) ?? null;
        switch (typeof value) {
            case 'object':
                result = result.replace(`:"${tag}"`, `:${JSON.stringify(value)}`);
                break;
            case 'string':
                result = result.replace(tag, value.replace(/"/g, '\\"')); // replace " with \" to avoid JSON.parse error
                break;
            case 'boolean':
                result = result
                    .replace(`:"${tag}"`, `:${value}`)
                    .replace(tag, String(value));
                break;
            default:
                result = result
                    .replace(`"${tag}":`, `"${value}":`)
                    .replace(`"${tag}"`, value)
                    .replace(tag, value);
                break;
        }
    }
    return isObject ? parseJSON(result, null, 'substitute') : result;
    function escapeRegExp(str) {
        return str.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
    }
}
function getDescendantProp(obj, desc) {
    if (!desc)
        return obj;
    const arr = desc.split('.');
    while (arr.length) {
        const prop = arr.shift();
        if (prop === undefined)
            break;
        obj = obj[prop];
    }
    return obj;
}
// Export flatten and unflatten from flat module
exports.flatten = flat.flatten;
exports.unflatten = flat.unflatten;
exports.default = {
    isEmptyObj,
    cloneDeep,
    removeEmptyValues,
    deepFind,
    findPath,
    pick,
    pick2,
    pickObj,
    pickAndMap,
    pickDeep,
    pickDeepValue,
    parseJSON,
    substitute,
    getDescendantProp,
    flatten: exports.flatten,
    unflatten: exports.unflatten
};
//# sourceMappingURL=objects.js.map