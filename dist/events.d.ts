/**
 * Used by Eventbus & related packages
 */
export declare function domainOf(eventName?: string): string;
/**
 * Match str with pattern, wildcards supported (domain MUST be present):
 * 		domain.*
 * 		domain.actor*
 * 		domain.*.update
 * ILLEGAL: *, *.update, *.action*
 * @param str
 * @param pattern
 */
export declare function match(str: string, pattern: string): boolean;
declare const _default: {
    domainOf: typeof domainOf;
    match: typeof match;
};
export default _default;
