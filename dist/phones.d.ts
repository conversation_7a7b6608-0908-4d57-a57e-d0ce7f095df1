import { CountryCode, CountryCallingCode, NationalNumber, E164Number } from 'libphonenumber-js/max';
export declare enum LineType {
    FIXED_LINE = "fixedLine",
    MOBILE = "mobile",
    FIXED_LINE_OR_MOBILE = "fixedLine_OR_mobile",
    TOLL_FREE = "tollFree",
    PREMIUM_RATE = "premiumRate",
    SHARED_COST = "sharedCost",
    VOIP = "voip",
    PERSONAL_NUMBER = "personalNumber",
    PAGER = "pager",
    UAN = "UAN",
    VOICEMAIL = "voicemail",
    UNKNOWN = "unknown"
}
type LineKeys = keyof typeof LineType;
export type ParsedPhone = {
    lineType?: typeof LineType[LineKeys];
    valid: boolean;
    possible: boolean;
    regionCode?: CountryCode;
    countryCode?: CountryCallingCode | string;
    nationalNumber?: NationalNumber;
    formattedNational?: string;
    number?: E164Number;
    fullNumber: string;
    isMobile?: boolean;
    isValidMobile?: boolean;
    isPossibleMobile?: boolean;
    international?: string;
};
/**
 * @param phoneNumber
 * @param country - either country calling code (65) OR ISO 3166-1 alpha-2 (SG)
 * @param clean
 */
export declare function parsePhoneNumber(phoneNumber: number | string, country?: string | number, clean?: boolean): ParsedPhone;
export declare function countryCode(country?: string): string;
declare function parse(cleanedNumber: number | string): ParsedPhone;
declare const _default: {
    parsePhoneNumber: typeof parsePhoneNumber;
    countryCode: typeof countryCode;
    parseFullNumber: typeof parse;
};
export default _default;
