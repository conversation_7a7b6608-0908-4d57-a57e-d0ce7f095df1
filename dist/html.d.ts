type IMAGE_REF = {
    original: string;
};
type IMAGES = {
    unique: IMAGE_REF[];
    external: IMAGE_REF[];
    local: IMAGE_REF[];
};
type URL_REF = {
    original: string;
    url: string;
};
/**
 * Extract image references in HTML (for substitution with substituteUrls())
 * @param html
 * @return image references: { unique: [{original: <url>}], external: [{original: <url>}], local: [{original: <url>}] }
 */
export declare function imageReferences(html: string): IMAGES;
/**
 * Substitute URLs in html (replace 'original' with 'url')
 * @param src html
 * @param list of urls to replace: [{ original: <old>, url: <new> }]
 * @return html
 */
export declare function substituteUrls(src: string, list: URL_REF[]): string;
declare const _default: {
    imageReferences: typeof imageReferences;
    substituteUrls: typeof substituteUrls;
};
export default _default;
