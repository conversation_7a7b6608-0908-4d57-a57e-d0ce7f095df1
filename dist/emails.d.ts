import { parseOneAddress } from 'email-addresses';
export { default as isEmail } from 'validator/lib/isEmail';
export declare enum EmailType {
    HOME = "home",
    WORK = "work",
    OTHERS = "others"
}
export declare function emailAddressType(email: string): EmailType;
export declare function isPersonalEmail(email: string): boolean;
declare const _default: {
    isPersonalEmail: typeof isPersonalEmail;
    emailAddressType: typeof emailAddressType;
    parseEmailAddress: typeof parseOneAddress;
};
export default _default;
