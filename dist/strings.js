"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mustache = mustache;
exports.hasUnicode = hasUnicode;
exports.isChinese = isChinese;
exports.similarity = similarity;
exports.parseUrl = parseUrl;
exports.isUrl = isUrl;
exports.isIP = isIP;
const tslib_1 = require("tslib");
const similarity_1 = tslib_1.__importDefault(require("similarity"));
const validator_1 = require("validator");
/**
 * Substitute placeholder in string with data
 * @param {String} str - ie. 'example: {text} high {label}'
 * @param {Object} data - { text: 'foo', label: 'bar' }
 * @returns {String} - 'example: foo high bar'
 */
function mustache(str = '', data) {
    return str.replace(/\{(\w+?)\}/gi, (match, group) => data[group] || '');
}
function hasUnicode(str) {
    return /[^\u0000-\u00ff]/.test(str);
}
function isChinese(str) {
    return /[\u4e00-\u9fff]/.test(str);
}
function similarity(a, b) {
    const convertType = (C) => {
        const Ctype = typeof C;
        switch (Ctype) {
            case 'object':
                if (!isNaN(Date.parse(C)))
                    return C.toISOString();
                break;
            case 'boolean': return C ? '1' : '0';
            default: return C;
        }
    }, A = convertType(a), B = convertType(b), Atype = typeof A, Btype = typeof B;
    if (Atype === Btype) {
        if ('object' === Atype || 'number' === Btype)
            return (A === B) ? 1 : 0;
    }
    return (0, similarity_1.default)(A, B);
}
function parseUrl(url) {
    // @Young
    // see:   http://stackoverflow.com/questions/27745/getting-parts-of-a-url-regex
    const parsed = url.match(/^((http[s]?|ftp):\/)?\/?([^:\/\s]+)(:([^\/]*))?((\/\w+)*\/)([\w\-\.]+[^#?\s]+)(\?([^#]*))?(#(.*))?$/);
    if (!parsed)
        return;
    let apiRoot = parsed[6], apiPath = parsed[8];
    const fullpath = apiRoot + apiPath, versionStr = fullpath.match(/v[0-9]+\//);
    let version = 0;
    if (versionStr) {
        const paths = fullpath.split(versionStr[0]);
        apiRoot = paths[0];
        apiPath = paths[1];
        version = parseInt(versionStr[0].substring(1).replace(/\/$/, ''));
    }
    return {
        protocol: parsed[2],
        hostname: parsed[3],
        port: parsed[5],
        apiRoot: apiRoot.replace(/\/$/, ''), // remove trailing '/'
        apiVersion: version,
        apiPath: '/' + apiPath,
        pathname: fullpath,
        file: parsed[8],
        query: parsed[9],
        hash: parsed[12],
    };
}
function isUrl(str) {
    return str ? !!str.match(/^\w+:\/\//gi) : false;
}
function isIP(str) {
    return (0, validator_1.isIP)(str);
}
exports.default = {
    mustache,
    hasUnicode,
    isChinese,
    similarity,
    parseUrl,
    isUrl,
    isIP
};
//# sourceMappingURL=strings.js.map