export declare function currencySymbol(code?: string, short?: boolean): string;
export declare function currency(code?: string, short?: boolean): {
    decimals: number;
    symbol: string;
    name: string;
    country: string | undefined;
    locale: string | undefined;
};
export declare function isValid(code?: string): boolean;
/**
 * Convert native amount to zeroDecimal
 * 	- Multiply by 10^decimals and truncate to handle floating point precision issues
 * 	- Use a string-based approach to avoid floating point precision issues
 * @param amount
 * @param currencyCode
 */
export declare function zeroDecimal(amount: number, currencyCode?: string): number;
/**
 * Convert zeroDecimal amount to native amount
 * @param amount
 * @param currencyCode
 */
export declare function nativeAmount(amount: number, currencyCode?: string): number;
export declare function roundAmount(amount: number, currencyCode?: string): number;
export declare function currencyFormat(amount: number, currencyCode?: string): string;
declare const _default: {
    currency: typeof currency;
    currencySymbol: typeof currencySymbol;
    isValid: typeof isValid;
    zeroDecimal: typeof zeroDecimal;
    nativeAmount: typeof nativeAmount;
    roundAmount: typeof roundAmount;
    currencyFormat: typeof currencyFormat;
};
export default _default;
