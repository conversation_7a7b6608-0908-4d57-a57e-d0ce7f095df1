{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../src/security.ts"], "names": [], "mappings": ";;;AAyCA,0CASC;AAGD,kCAMC;AAKD,oCAEC;AAED,oCAUC;;AA9ED,sDAAoC;AACpC,4EAA2C;AAE3C,MAAM,GAAG,GAAG,KAAK,EAChB,KAAK,GAAG,OAAO,EACf,WAAW,GAAG,gEAAgE,CAAA;AAE/E,MAAa,GAAG;IACP,MAAM,CAAQ;IAEtB,YAAY,MAAc;QACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACrB,CAAC;IAED,MAAM,CAAC,MAAM,CAAC,KAAa;QAC1B,OAAO,aAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IACzB,CAAC;IAED,MAAM,CAAC,OAAY,EAAE,MAAe;QACnC,OAAO,aAAG,CAAC,IAAI,CAAC;YACf,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,EAAG,kBAAkB;YAChE,OAAO;YACP,MAAM,EAAE,IAAI,CAAC,MAAM;SACnB,CAAC,CAAA;IACH,CAAC;IAED,MAAM,CAAC,KAAa;QACnB,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IACzB,CAAC;IAED,MAAM,CAAC,KAAa,EAAE,MAAiB,KAAK;QAC3C,IAAI,CAAC;YACJ,OAAO,aAAG,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QAC3C,CAAC;QACD,OAAO,CAAC,EAAE,CAAC;YACV,OAAO,KAAK,CAAA;QACb,CAAC;IACF,CAAC;CACD;AA/BD,kBA+BC;AAED,iDAAiD;AACjD,SAAgB,eAAe,CAAC,cAAmB,EAAE;IACpD,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,WAAW,CAAA;IAEzD,OAAO,CACN,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;QAChD,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,iBAAO,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9D,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5C,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAChD,CAAA;AACF,CAAC;AAED,uDAAuD;AACvD,SAAgB,WAAW,CAAC,IAAY,EAAE,YAAoB,EAAE;IAC/D,MAAM,KAAK,GAAG,IAAI;QACjB,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC;QACtE,CAAC,CAAC,EAAE,CAAA;IAEL,OAAO,KAAK,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;AAC1G,CAAC;AAED;;GAEG;AACH,SAAgB,YAAY,CAAC,MAAc;IAC1C,OAAO,YAAY,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;AACzC,CAAC;AAED,SAAgB,YAAY,CAAC,OAAe,CAAC,EAAE,aAAqB,WAAW;IAC9E,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAA;IAEhC,IAAI,OAAO,GAAG,EAAE,CAAA;IAEhB,OAAO,IAAI,GAAG,CAAC,EAAE,CAAC;QACjB,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAA;QAChE,IAAI,EAAE,CAAA;IACP,CAAC;IACD,OAAO,OAAO,CAAA;AACf,CAAC;AAED,kBAAe;IACd,GAAG;IACH,eAAe;IACf,WAAW;IACX,YAAY;IACZ,YAAY;CACZ,CAAA"}