import { formatDateTime as dayjs } from '@perkd/format-datetime';
export declare const TIMEZONE = "Asia/Singapore";
type Rule = {
    base?: string;
    duration?: string;
    startOf?: string;
    endOf?: string;
};
type Period = {
    startTime: Date;
    endTime: Date;
};
type DateDetail = {
    year: number;
    month: number;
    day: number;
};
export declare function sameDay(d1: Date, d2: Date, timeZone?: string): boolean;
/**
 * Check if given 2 dates are at the same day		// FIXME @zhangli
 */
export declare function isSameDay(d1: Date, d2: Date): boolean;
/**
 * Get difference days of given 2 dates
 */
export declare function diffDays(d1: Date, d2: Date): number | null;
/**
 * https://day.js.org/docs/en/manipulate/start-of
 * @param unit - hour, day, week, month, quarter, year...
 * @param date - numeric => relative days (+/-) to now, default now
 */
export declare function startOf(unit: dayjs.OpUnitType, date?: Date | string | number, timeZone?: string): Date;
/**
 * https://day.js.org/docs/en/manipulate/end-of
 * @param unit - hour, day, week, month, quarter, year...
 * @param date - numeric => relative days (+/-) to now, default now
 */
export declare function endOf(unit: dayjs.OpUnitType, date?: Date | string | number, timeZone?: string): Date;
/**
 * Get day of week (1 = Monday, 7 = Sunday)
 */
export declare function dayOfWeek(date: Date | dayjs.Dayjs | {
    year: number;
    month: number;
    day: number;
}, timeZone?: string): number;
export declare function timestamp(time: string): number;
/**
 * Convert various date strings or dates to Date
 */
export declare function parseTime(time: Date | string): Date;
/**
 * ceil time by duration
 * @param {Date} time
 * @param {String} from opening time, format: HH:mm
 * @param {Number} duration in minutes
 * @returns {Date}
 * @example
 * 	ceil(new Date('2021-01-01 10:00'), '10:00', 30) // 2021-01-01 10:00
 * 	ceil(new Date('2021-01-01 10:29'), '10:00', 30) // 2021-01-01 10:30
 * 	ceil(new Date('2021-01-01 10:30'), '10:00', 30) // 2021-01-01 10:30
 * 	ceil(new Date('2021-01-01 10:31'), '10:00', 30) // 2021-01-01 11:00
 */
export declare function ceil(time: Date, from: string, duration: number): Date;
/**
 * floor time by duration
 * @param {Date} time
 * @param {String} from opening time, format: HH:mm
 * @param {Number} duration in minutes
 * @returns {Date}
 * @example
 * 	floor(new Date('2021-01-01 10:29'), '10:00', 30) // 2021-01-01 10:00
 * 	floor(new Date('2021-01-01 10:00'), '10:00', 30) // 2021-01-01 10:00
 * 	floor(new Date('2021-01-01 10:30'), '10:00', 30) // 2021-01-01 10:30
 * 	floor(new Date('2021-01-01 10:31'), '10:00', 30) // 2021-01-01 10:30
 */
export declare function floor(time: Date, from: string, duration: number): Date;
/**
 * Returns list of day-of-week between the given dates
 * @param  {Date} d1
 * @param  {Date} d2
 * @return {Array}    day-of-week  (Sunday = 0, Saturday = 6)
 */
/**
 * Get start time by rule & active period
 * @param [rule]
 * 		{string} base - 'membership.startTime' - depend on reference, default is based on current time
 * 		{string} duration - 'PnYnMnDTnHnMnS' - https://momentjs.com/docs/#/durations/as-iso-string/
 *		{string} startOf  - 'year,month,day...' - https://momentjs.com/docs/#/manipulating/start-of/
 *		{string} endOf - 'year,month,day...' - https://momentjs.com/docs/#/manipulating/end-of/
 * @param activePeriod
 * @param [reference] - reference data for picking base time - {membership}
 */
export declare function getStartTime(rule: Rule | undefined, activePeriod: Period, reference?: object, timeZone?: string): Date | void;
/**
 * Get end time by rule & active period
 * @param [rule]
 * 		{string} base - 'membership.startTime' - depend on reference, default is based on current time
 * 		{string} duration - 'PnYnMnDTnHnMnS' - https://momentjs.com/docs/#/durations/as-iso-string/
 * 		{string} startOf - 'year,month,day...' - https://momentjs.com/docs/#/manipulating/start-of/
 * 		{string} endOf - 'year,month,day...' - https://momentjs.com/docs/#/manipulating/end-of/
 * @param activePeriod
 * @param [reference] - reference data for picking base time - {membership}
 */
export declare function getEndTime(rule: Rule | undefined, activePeriod: Period, reference?: object, timeZone?: string): Date | void;
/**
 * Get start & end (Dates, both inclusive), rounded to minutes  (used by booking)
 */
export declare function getStartEndTime(from: Date, duration: number, timeZone?: string, unit?: dayjs.OpUnitType): {
    start: Date;
    end: Date;
};
/**
 * Number of years since the specified date
 */
export declare function yearsSince(date: Date): number;
/**
 * Number of days since the specified date (inclusive)
 */
export declare function daysSince(date: Date): number;
/**
 * Number of days to the NEXT anniversary of the specified date (inclusive), eg. 0 = today, 1 = tomorrow
 */
export declare function daysToAnniversary(date: Date): number;
/**
 * Number of days from the LAST anniversary of the specified date (inclusive)
 */
export declare function daysSinceAnniversary(date: Date): number;
/**
 * Get Date
 * @param [rule]
 * 			{string} base - 'membership.startTime' - depend on reference, default is based on current time
 * 			{string} duration - 'PnYnMnDTnHnMnS' - https://momentjs.com/docs/#/durations/as-iso-string/
 * 			{string} startOf - 'year,month,day...' - https://momentjs.com/docs/#/manipulating/start-of/
 * 			{string} endOf - 'year,month,day...' - https://momentjs.com/docs/#/manipulating/end-of/
 * @param [reference] - reference data for picking base time - {membership}
 * @param [timeZone]
 */
export declare function getDate(rule?: Rule, reference?: object, timeZone?: string): Date | void;
/**
 * replace isValid() for app
 * check the given date is valid date
*/
export declare function isValidDate(date: any): boolean;
export declare function isDateObject(date: any): boolean;
export declare function isDateDetail(date: any): date is DateDetail;
/**
 * replace getDate() for app
 * given any valid date value, convert it to a Date format
*/
export declare function toDate(date?: string | number | Date | DateDetail, serverDate?: Date): Date;
/**
 * replace getDateDetail() for app
 * convert date format to dateDetail
 */
export declare function toDateDetail(date?: string | number | Date): DateDetail | undefined;
/**
 * return day of week, Monday - Sunday: 1 - 7
*/
export declare function getDay(date?: string | number | Date | DateDetail, serverDate?: Date): number;
/**
 * replace dateByDay()
 * calculate the next date for a specified weekday from a given date
 * targetWeekday: 1 - 7
*/
export declare function getNextDateByWeekday(targetWeekday: number, date?: string | number | Date): Date;
/**
 * get ISO 8601 date, YYYY-MM-DD
 */
export declare function getISODate(date?: string | number | Date | DateDetail): string;
export declare function lastDayOfMonth(date?: string | number | Date | DateDetail, serverDate?: Date): number;
/**
 * Check if time is between start & end (non-inclusive)
 * @param {string} time - Time in HH:mm format
 * @param {string} start - Start time HH:mm
 * @param {string} end - End time HH:mm
 * @returns {boolean}
 */
export declare function timeIsBetween(time: string, start: string, end: string): boolean;
declare const _default: {
    sameDay: typeof sameDay;
    isSameDay: typeof isSameDay;
    diffDays: typeof diffDays;
    ceil: typeof ceil;
    floor: typeof floor;
    parseTime: typeof parseTime;
    timestamp: typeof timestamp;
    startOf: typeof startOf;
    endOf: typeof endOf;
    dayOfWeek: typeof dayOfWeek;
    yearsSince: typeof yearsSince;
    daysSince: typeof daysSince;
    daysToAnniversary: typeof daysToAnniversary;
    daysSinceAnniversary: typeof daysSinceAnniversary;
    getStartTime: typeof getStartTime;
    getEndTime: typeof getEndTime;
    getStartEndTime: typeof getStartEndTime;
    getDate: typeof getDate;
    isValidDate: typeof isValidDate;
    isDateObject: typeof isDateObject;
    isDateDetail: typeof isDateDetail;
    toDate: typeof toDate;
    toDateDetail: typeof toDateDetail;
    getDay: typeof getDay;
    getNextDateByWeekday: typeof getNextDateByWeekday;
    getISODate: typeof getISODate;
    lastDayOfMonth: typeof lastDayOfMonth;
    timeIsBetween: typeof timeIsBetween;
};
export default _default;
