export declare enum AddressType {
    HOME = "home",
    WORK = "work",
    OTHERS = "others"
}
export type Address = {
    full?: string;
    street?: string;
    floor?: string;
    unit?: string;
    level?: string;
    formatted?: string;
    short?: string;
    house?: string;
    premise?: string;
    unitNo?: string;
    city?: string;
    state?: string;
    country?: string;
    postCode?: string;
    countryCode?: string;
};
type AddressComponent = {
    long_name: string;
    short_name: string;
    types: string[];
    index?: number;
};
type LatLng = {
    lat: number;
    lng: number;
};
type Geometry = {
    location: LatLng;
    location_type: string;
    viewport: {
        northeast: LatLng;
        southwest: LatLng;
    };
};
export type GeoCoding = {
    address_components: AddressComponent[];
    formatted_address: string;
    geometry: Geometry;
    place_id: string;
    plus_code: {
        compound_code: string;
        global_code: string;
    };
    types: string[];
};
export declare function formatAddress(address: Address): Address;
export declare function addressComponents2Address(addressComponents: AddressComponent[], formattedAddr: string): Address;
export declare function completeAddress(address: any, geocoding: GeoCoding): Address;
declare const _default: {
    formatAddress: typeof formatAddress;
    completeAddress: typeof completeAddress;
    addressComponents2Address: typeof addressComponents2Address;
};
export default _default;
