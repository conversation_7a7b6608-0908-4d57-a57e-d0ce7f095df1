"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.round = round;
exports.growthRate = growthRate;
function round(number, decimals = 0) {
    return Number(number.toFixed(decimals));
}
function growthRate(current, previous) {
    return (previous === 0)
        ? undefined
        : Number(((current - previous) / previous).toFixed(3));
}
exports.default = {
    round,
    growthRate
};
//# sourceMappingURL=numbers.js.map