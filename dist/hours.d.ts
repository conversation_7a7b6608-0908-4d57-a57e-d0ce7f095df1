/**
 * Opening Hours & Time of day related
 */
import { formatDateTime as dayjs } from '@perkd/format-datetime';
import Time from './time';
type Time = {
    time: string;
};
type DayTime = Time & {
    day: number;
};
type Period = {
    open: DayTime;
    close: DayTime;
    busy?: DayTime;
};
type TimePeriod = {
    open: Time;
    close: Time;
    busy?: Time;
};
type YearMonthDay = {
    year: number;
    month: number;
    day: number;
};
type Hours = {
    specific?: {
        date: YearMonthDay;
        periods: TimePeriod[];
    }[];
    periods: Period[];
};
type Schedule = {
    date: string | number | Date;
    open: {
        start: string;
        end: string;
    }[];
};
export type OpeningHours = Hours;
export declare const DAY = "day", MINUTE = "minute", HOUR_0 = "00:00", HOUR_24 = "24:00", HOURS_247: {
    specific: never[];
    periods: {
        open: {
            day: number;
            time: string;
        };
        close: {
            day: number;
            time: string;
        };
    }[];
};
/**
 * Get hours for day
 * @param hours - opening hours definition
 * @param year - with century, 0 if specifying a date without a year
 * @param month - of year, 1 = January, 0 if specifying a year without a month and day
 * @param day - of month, 1-31 & valid for year and month, 0 if specifying a year by itself or a year & month where the day is not significant
 */
export declare function hoursFor(hours: Hours, year: number, month: number, day: number): Period[];
export declare function isLateNight(open: Time, close: Time): boolean;
export declare function within(open: DayTime, close: DayTime, start: number, end: number, dow: number): boolean;
/**
 * Check if Open for time period
 */
export declare function isOpen(hours: Hours, from: Date | dayjs.Dayjs, to?: Date | dayjs.Dayjs, timeZone?: string): boolean;
export declare function nextOpen(hours: Hours, from?: Date | dayjs.Dayjs, timeZone?: string): any;
/**
 * Get opening time on date
 */
export declare function openFrom(hours: Hours, from: Date | dayjs.Dayjs, timeZone?: string): Date;
/**
 * Get closing time on date
 */
export declare function openUntil(hours: Hours, date: Date | dayjs.Dayjs, timeZone?: string): Date;
/**
 * Get startTime and endTime of Hours (when no 'specific' or has 'periods', return {})
 */
export declare function startEndOf(hours: Hours, timeZone?: string): {
    startTime?: undefined;
    endTime?: undefined;
} | {
    startTime: Date | undefined;
    endTime: Date | undefined;
};
/**
 * Convert Time portion of date to time string HH:MM
 */
export declare function date2TimeString(dateOrStr: Date | string, timeZone?: string): string;
/**
 * Sort hours by ascending order of open (Sunday first)
 */
export declare function sortHours(periods: Period[]): Period[];
/**
 * Convert a specific date schedule to Hours specific
 */
export declare function scheduleToHours(schedule: Schedule[]): Hours;
/**
 * Combine list of Periods (same day) into one
 * 	- dropping periods entirely enclosed by another, result sorted by open.time
 */
export declare function periodsMerge(list: Period[][]): Period[];
/**
 * Union of two list of Periods (omits 'busy' for now), result sorted by open.time
 */
export declare function periodsUnion(a: Period[], b: Period[]): Period[];
/**
 * Difference of two list of Periods: a - b   (omits 'busy' for now)
 */
export declare function periodsDifference(periodsA: Period[], periodsB: Period[]): Period[];
/**
 * Merge a specific date into an array of specific dates
 * - If the date already exists, merge the periods using periodsUnion
 * - If the date doesn't exist, add it (with a deep clone to avoid reference issues)
 */
export declare function mergeSpecificDate(specificDates: Hours['specific'], newDate: NonNullable<Hours['specific']>[0]): NonNullable<Hours['specific']>;
/**
 * Aggregate multiple hours objects into a combined hours object
 * - Combines regular periods using periodsUnion
 * - Handles specific date overrides using mergeSpecificDate
 *
 * This implementation optimizes performance by:
 * - Validating inputs to handle malformed data gracefully
 * - Batching period merges to reduce deep cloning operations
 * - Processing specific dates more efficiently
 * - Using early returns for common edge cases
 */
export declare function aggregateHours(hoursArray: {
    hours: Hours;
}[]): Hours;
declare const _default: {
    hoursFor: typeof hoursFor;
    isLateNight: typeof isLateNight;
    within: typeof within;
    isOpen: typeof isOpen;
    nextOpen: typeof nextOpen;
    startEndOf: typeof startEndOf;
    date2TimeString: typeof date2TimeString;
    sortHours: typeof sortHours;
    scheduleToHours: typeof scheduleToHours;
    mergeSpecificDate: typeof mergeSpecificDate;
    aggregateHours: typeof aggregateHours;
    periodsMerge: typeof periodsMerge;
    periodsUnion: typeof periodsUnion;
    periodsDifference: typeof periodsDifference;
};
export default _default;
