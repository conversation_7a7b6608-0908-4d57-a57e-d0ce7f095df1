"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.currencySymbol = currencySymbol;
exports.currency = currency;
exports.isValid = isValid;
exports.zeroDecimal = zeroDecimal;
exports.nativeAmount = nativeAmount;
exports.roundAmount = roundAmount;
exports.currencyFormat = currencyFormat;
const tslib_1 = require("tslib");
const currency_symbol_map_1 = tslib_1.__importDefault(require("currency-symbol-map"));
const numbers_1 = require("./numbers");
const currencies_json_1 = tslib_1.__importDefault(require("./currencies.json")); // from country-data with TWD decimals fixed (2 in package)
// based on ISO 4217
function currencySymbol(code = '', short) {
    const symbol = (0, currency_symbol_map_1.default)(code.toUpperCase()) || '$';
    return symbol.endsWith('$') && short ? '$' : symbol;
}
// country = ISO 3166-1 alpha-2 (uppercase)
// locale: used for i18Next native format
function currency(code = '', short) {
    const currencyCode = code.toUpperCase(), symbol = currencySymbol(currencyCode, short), { decimals = 2, name = '', country, locale } = currencies_json_1.default.find(c => c.code === currencyCode) || {};
    return { decimals, symbol, name, country, locale };
}
function isValid(code = '') {
    const currencyCode = code.toUpperCase();
    return currencies_json_1.default.some(c => c.code === currencyCode);
}
/**
 * Convert native amount to zeroDecimal
 * 	- Multiply by 10^decimals and truncate to handle floating point precision issues
 * 	- Use a string-based approach to avoid floating point precision issues
 * @param amount
 * @param currencyCode
 */
function zeroDecimal(amount, currencyCode = '') {
    const { decimals } = currency(currencyCode), multiplier = Math.pow(10, decimals), amountStr = (amount * multiplier).toFixed(10), decimalIndex = amountStr.indexOf('.');
    return parseInt(decimalIndex > 0
        ? amountStr.substring(0, decimalIndex)
        : amountStr);
}
/**
 * Convert zeroDecimal amount to native amount
 * @param amount
 * @param currencyCode
 */
function nativeAmount(amount, currencyCode = '') {
    const { decimals } = currency(currencyCode);
    return Number((amount / Math.pow(10, decimals)).toFixed(decimals));
}
function roundAmount(amount, currencyCode = '') {
    const { decimals } = currency(currencyCode);
    return (0, numbers_1.round)(amount, decimals);
}
function currencyFormat(amount, currencyCode = '') {
    const { locale, decimals, symbol } = currency(currencyCode);
    if (!currencyCode) {
        return `${symbol}${amount.toFixed(decimals)}`;
    }
    try {
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currencyCode,
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(amount);
    }
    catch (error) {
        return `${symbol}${amount.toFixed(decimals)}`;
    }
}
exports.default = {
    currency,
    currencySymbol,
    isValid,
    zeroDecimal,
    nativeAmount,
    roundAmount,
    currencyFormat
};
//# sourceMappingURL=currencies.js.map