"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.imageReferences = imageReferences;
exports.substituteUrls = substituteUrls;
const htmlparser2_1 = require("htmlparser2");
const IMAGE = 'img'; // image tag
/**
 * Extract image references in HTML (for substitution with substituteUrls())
 * @param html
 * @return image references: { unique: [{original: <url>}], external: [{original: <url>}], local: [{original: <url>}] }
 */
function imageReferences(html) {
    const parser = new htmlparser2_1.Parser({ onopentag }, { decodeEntities: true }), images = {
        unique: [],
        external: [],
        local: [],
    };
    parser.write(html);
    parser.end();
    return images;
    function onopentag(name, attribs) {
        if (name === IMAGE) {
            const src = attribs.src.toLowerCase();
            if (!images.unique.includes(src)) {
                const type = src.startsWith('http') ? 'external' : 'local';
                images.unique.push(src);
                images[type].push({ original: attribs.src });
            }
        }
    }
}
/**
 * Substitute URLs in html (replace 'original' with 'url')
 * @param src html
 * @param list of urls to replace: [{ original: <old>, url: <new> }]
 * @return html
 */
function substituteUrls(src, list) {
    const copy = src;
    return list.reduce((html, { original, url }) => {
        const reg = new RegExp(original, 'gi');
        return html.replace(reg, url);
    }, copy);
}
exports.default = {
    imageReferences,
    substituteUrls,
};
//# sourceMappingURL=html.js.map