/**
 * Flows Module - defines a Flow as a sequence of steps and provides utility
 *		functions to navigate through these steps.
 *
 * The Flow type represents a multi-step process using:
 *  - `at`: the current step index.
 *  - `steps`: an array of step names (strings) that defines the sequence.
 *
 * These functions are useful for managing the progression and transition logic
 * in multi-step processes such as registration, onboarding, or wizard interfaces.
 */
export type Flow = {
    at: number;
    steps: string[];
};
/**
 * Get the position of next step for the flow
 * @param step one of the step names in the flow, eg. request, register, share, done...
 * @param flow
 */
export declare function nextStepOf(step: string, flow: Flow): number;
/**
 * Get the position of previous step for the flow
 * @param step name
 * @param flow
 */
export declare function preStepOf(step: string, flow: Flow): number;
/**
 * Get the position of given step in the flow
 * @param step name
 * @param flow
 */
export declare function stepOf(step: string, flow: Flow): number;
/**
 * Check if the step is in the flow
 * @param step name
 * @param flow
 */
export declare function hasStep(step: string, flow: Flow): boolean;
/**
 * Check if the step is at step in the flow
 * @param step name
 * @param flow
 */
export declare function atStep(step: string, flow: Flow): boolean;
/**
 * Check if the step is the last step in the flow
 * @param flow
 * @param step name
 */
export declare function isLastStep(flow: Flow, step?: string): boolean;
declare const _default: {
    nextStepOf: typeof nextStepOf;
    preStepOf: typeof preStepOf;
    stepOf: typeof stepOf;
    atStep: typeof atStep;
    hasStep: typeof hasStep;
    isLastStep: typeof isLastStep;
};
export default _default;
