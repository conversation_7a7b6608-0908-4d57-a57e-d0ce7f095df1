/**
 * Gloabl tax identifiers:	https://www.oecd.org/tax/automatic-exchange/crs-implementation-and-assistance/tax-identification-numbers/
 */
export declare function isBusinessRegNumber(country: string, registration: string): boolean;
export declare function isNationalIdentity(country: string, identity: string): boolean;
export declare namespace Singapore {
    function isBusinessRegNumber(str: string): boolean;
    function isNRIC(nric: string): boolean;
}
export declare namespace Taiwan {
    function isCitizenDigitalCertNumber(str: string): boolean;
    function isBusinessRegNumber(str: string): boolean;
}
