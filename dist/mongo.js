"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjectId = void 0;
exports.getDatabaseUrl = getDatabaseUrl;
exports.shortId = shortId;
const tslib_1 = require("tslib");
const bson_objectid_1 = tslib_1.__importDefault(require("bson-objectid"));
exports.ObjectId = bson_objectid_1.default;
const nanoid_1 = require("nanoid");
const isMongoId_1 = tslib_1.__importDefault(require("validator/lib/isMongoId"));
const MAX_POOL_SIZE = 5, SHORT_ID_LENGTH = 10;
function getDatabaseUrl(options) {
    const { name, host, username, password, authDb, dbSet, maxPoolSize = MAX_POOL_SIZE } = options;
    return 'mongodb://'
        + ((username)
            ? `${username}:${password}@`
            : '')
        + `${host}/${name}?authSource=${authDb}`
        + ((dbSet)
            ? `&replicaSet=${dbSet}&readPreference=primaryPreferred&slaveOk=true`
            : '')
        + `&useNewUrlParser=true&useUnifiedTopology=true&keepAlive=false&maxPoolSize=${maxPoolSize}&socketTimeoutMS=360000&connectTimeoutMS=10000&maxIdleTimeMS=300000`;
}
function shortId(len = SHORT_ID_LENGTH) {
    return (0, nanoid_1.nanoid)(len);
}
exports.default = {
    isMongoId: isMongoId_1.default,
    getDatabaseUrl,
    shortId
};
//# sourceMappingURL=mongo.js.map