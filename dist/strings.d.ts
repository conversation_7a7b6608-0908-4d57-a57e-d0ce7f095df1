/**
 * Substitute placeholder in string with data
 * @param {String} str - ie. 'example: {text} high {label}'
 * @param {Object} data - { text: 'foo', label: 'bar' }
 * @returns {String} - 'example: foo high bar'
 */
export declare function mustache(str: string | undefined, data: any): string;
export declare function hasUnicode(str: string): boolean;
export declare function isChinese(str: string): boolean;
export declare function similarity(a: any, b: any): number;
export declare function parseUrl(url: string): {
    protocol: string;
    hostname: string;
    port: string;
    apiRoot: string;
    apiVersion: number;
    apiPath: string;
    pathname: string;
    file: string;
    query: string;
    hash: string;
} | undefined;
export declare function isUrl(str: string): boolean;
export declare function isIP(str: string): boolean;
declare const _default: {
    mustache: typeof mustache;
    hasUnicode: typeof hasUnicode;
    isChinese: typeof isChinese;
    similarity: typeof similarity;
    parseUrl: typeof parseUrl;
    isUrl: typeof isUrl;
    isIP: typeof isIP;
};
export default _default;
