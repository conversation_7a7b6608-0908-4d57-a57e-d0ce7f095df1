{"version": 3, "file": "rateLimit.js", "sourceRoot": "", "sources": ["../src/rateLimit.ts"], "names": [], "mappings": ";;;AAAA,qCAA+C;AAUxC,MAAM,KAAK,GAAG,KAAK,EAAC,YAAoB,EAAkB,EAAE;IAClE,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAA;AACjE,CAAC,CAAA;AAFY,QAAA,KAAK,SAEjB;AAEM,MAAM,SAAS,GAAG,CAAC,IAAU,EAAE,MAAY,EAAW,EAAE;IAC9D,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,IAAI,CAAA;IAE9C,IAAI,CAAC,CAAC,KAAK,IAAI,QAAQ,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAA;IAEpG,MAAM,OAAO,GAAG,IAAI,qBAAW,CAAC,EAAE,iBAAiB,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAA;IAEvE,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;QAC5B,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAEhE,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,UAAU,EAAE,CAAC;YAC1C,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YACtC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;QACnC,CAAC;aACI,CAAC;YACL,MAAM,IAAI,KAAK,CAAC,WAAW,IAAI,2BAA2B,CAAC,CAAA;QAC5D,CAAC;IACF,CAAC;IAED,OAAO,OAAO,CAAA;AACf,CAAC,CAAA;AApBY,QAAA,SAAS,aAoBrB;AAED,MAAM,kBAAkB,GAAG,CAAC,MAAW,EAAE,EAAE,IAAY,EAAE,EAAE;IAC1D,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAC3B,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,CAAC;QACzB,CAAC,CAAC,GAAG;QACL,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAyB,EAAE,IAAY,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EACrG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IAE/B,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAA;AAC1B,CAAC,CAAA;AAED,MAAM,IAAI,GAAG,CAAC,EAAY,EAAE,OAAoB,EAAE,EAAE;IAChD,OAAO,KAAK,EAAE,GAAG,IAAW,EAAE,EAAE;QAC5B,IAAI,CAAC;YACD,MAAM,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;YAC7B,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,CAAA;QACtB,CAAC;QACP,OAAM,CAAC,EAAE,CAAC;YACA,MAAM,CAAC,CAAA;QACX,CAAC;IACL,CAAC,CAAA;AACL,CAAC,CAAA"}