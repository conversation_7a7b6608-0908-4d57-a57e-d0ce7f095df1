{"version": 3, "file": "languages.js", "sourceRoot": "", "sources": ["../src/languages.ts"], "names": [], "mappings": ";;AAgBA,oCAiBC;AAQD,0CAMC;AAKD,gCAUC;AAED,wCAMC;;AAtED,4DAA2B;AAE3B,MAAM,WAAW,GAA+B;IAC/C,OAAO,EAAE,YAAY;IACrB,OAAO,EAAE,SAAS;IAClB,OAAO,EAAE,SAAS;IAClB,UAAU,EAAE,SAAS;IACrB,UAAU,EAAE,YAAY;CACxB,CAAA;AAED;;;;;GAKG;AACH,SAAgB,YAAY,CAAC,YAAsB,EAAE,EAAE,SAAmB,EAAE,QAAiB;IAC5F,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAQ,EAAE,QAAgB,EAAE,EAAE;QAC1D,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAA;QAEpD,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAClB,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACnB,WAAW,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAA;QACxD,WAAW,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAA;QAC1D,OAAO,GAAG,CAAA;IACX,CAAC,EAAE,EAAE,CAAC,EACN,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,QAAgB,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAA;IAErE,IAAI,KAAK;QAAE,OAAO,KAAK,CAAA;IAEvB,MAAM,UAAU,GAAG,IAAI,gBAAM,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;IAE1D,OAAO,IAAI,gBAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;AAC3D,CAAC;AAED;;;;;GAKG;AACH,SAAgB,eAAe,CAAC,SAAmB,EAAE,CAAM,EAAE,QAAgB;IAC5E,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EACxC,CAAE,KAAK,CAAE,GAAG,kBAAkB,EAC9B,GAAG,GAAG,YAAY,CAAC,SAAS,EAAE,kBAAkB,EAAE,QAAQ,IAAI,KAAK,CAAC,CAAA;IAErE,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAA;AACjC,CAAC;AAED;;GAEG;AACH,SAAgB,UAAU,CAAC,SAAc,EAAE;IAC1C,MAAM,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,MAAM,EAChC,CAAE,KAAK,GAAG,EAAE,CAAE,GAAG,SAAS,EAC1B,CAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAE,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAE9C,IAAI,CAAC,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,CAAC,IAAI,OAAO,KAAK,MAAM,EAAE,CAAC;QAClE,OAAO,YAAY,CAAA;IACpB,CAAC;IAED,OAAO,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAA;AAC7C,CAAC;AAED,SAAgB,cAAc,CAAC,GAAQ,EAAE,QAAgB;IACxD,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;QACnB,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,SAAS,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,GAAG,CAAC,CAAA;QACjE,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;IAC1D,CAAC;IACD,OAAO,GAAG,CAAA;AACX,CAAC;AAED,kBAAe;IACd,YAAY;IACZ,eAAe;IACf,UAAU;IACV,cAAc;CACd,CAAA"}