{"name": "@perkd/utils", "version": "2.0.5", "description": "Utilities for JavaScript", "private": true, "engines": {"node": ">=20"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "npx tsc", "prestart": "yarn build", "start": "node dist/index.js", "test": "tsc && node --test --import tsx tests/**/*.test.ts", "test-one": "tsc && node --test --import tsx", "coverage": "c8 yarn test", "coverage:report": "c8 report", "coverage:view": "open coverage/index.html", "coverage:badge": "node scripts/update-coverage-badge.js", "update": "ncu -u -x camelcase -x flat -x franc -x nanoid -x @types/franc", "reinstall": "rm -rf node_modules/ yarn.lock; yarn; rm -rf dist/ tsconfig.tsbuildinfo; tsc", "release": "rm -rf dist/ tsconfig.tsbuildinfo; tsc; rm -rf node_modules/ yarn.lock; yarn workspaces focus --production"}, "repository": {"type": "git", "url": "git+https://github.com/perkd/utils.git"}, "bugs": {"url": "https://github.com/perkd/utils/issues"}, "homepage": "https://github.com/perkd/utils#readme", "files": ["README.md", "dist", "!*/__tests__"], "dependencies": {"@perkd/format-datetime": "github:perkd/format-datetime#semver:^1.3.3", "bson-objectid": "^2.0.4", "camelcase": "6.3.0", "country-data": "^0.0.31", "currency-symbol-map": "^5.1.0", "deepmerge": "^4.3.1", "email-addresses": "^5.0.0", "flat": "5.0.2", "franc": "5.0.0", "get-value": "^4.0.1", "htmlparser2": "^10.0.0", "jws": "^4.0.0", "libphonenumber-js": "^1.12.8", "limiter": "^3.0.0", "locale": "^0.1.0", "nanoid": "3.3.8", "pinyin-pro": "^3.26.0", "referer-parser": "^0.0.3", "rfdc": "^1.4.1", "set-value": "^4.1.0", "sift": "^17.1.3", "similarity": "^1.2.1", "traverse": "^0.6.11", "tslib": "^2.8.1", "validator": "^13.15.0"}, "devDependencies": {"@eslint/js": "^9.27.0", "@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.3", "@stylistic/eslint-plugin-ts": "^4.4.0", "@types/busboy": "^1", "@types/country-data": "^0.0.5", "@types/eslint": "^9.6.1", "@types/flat": "5.0.5", "@types/franc": "5.0.3", "@types/get-value": "^3.0.5", "@types/jws": "^3.2.10", "@types/locale": "^0.1.4", "@types/node": "^22.15.21", "@types/set-value": "^4", "@types/similarity": "^1.2.3", "@types/traverse": "^0.6.37", "@types/validator": "^13.15.1", "c8": "^10.1.3", "eslint": "^9.27.0", "eslint-plugin-jsonc": "^2.20.1", "tsx": "^4.19.4", "typescript-eslint": "^8.32.1"}, "packageManager": "yarn@4.9.1"}