{"version": "2.0", "routeKey": "ANY /shopify-app-test", "rawPath": "/shopify-app-test", "rawQueryString": "", "headers": {"accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", "accept-encoding": "gzip, deflate, br", "accept-language": "en-sg", "content-length": "0", "host": "9kvcndk6aj.execute-api.ap-southeast-1.amazonaws.com", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15", "x-amzn-trace-id": "Root=1-6191ede0-22ab46d9131a56972959cea1", "x-forwarded-for": "*************", "x-forwarded-port": "443", "x-forwarded-proto": "https"}, "requestContext": {"accountId": "************", "apiId": "9kvcndk6aj", "domainName": "9kvcndk6aj.execute-api.ap-southeast-1.amazonaws.com", "domainPrefix": "9kvcndk6aj", "http": {"method": "GET", "path": "/shopify-app-test", "protocol": "HTTP/1.1", "sourceIp": "*************", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15"}, "requestId": "I1IbHi7BSQ0EMfQ=", "routeKey": "ANY /shopify-app-test", "stage": "$default", "time": "15/Nov/2021:05:19:28 +0000", "timeEpoch": *************}, "isBase64Encoded": false}