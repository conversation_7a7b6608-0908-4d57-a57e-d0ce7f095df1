{"resource": "/", "path": "/", "httpMethod": "GET", "headers": {"accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9", "accept-encoding": "gzip, deflate, br", "accept-language": "en-US,en;q=0.9", "cookie": "s_fid=7AAB6XMPLAFD9BBF-0643XMPL09956DE2; regStatus=pre-register", "Host": "70ixmpl4fl.execute-api.us-east-2.amazonaws.com", "sec-fetch-dest": "document", "sec-fetch-mode": "navigate", "sec-fetch-site": "none", "upgrade-insecure-requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36", "X-Amzn-Trace-Id": "Root=1-5e66d96f-7491f09xmpl79d18acf3d050", "X-Forwarded-For": "*************", "X-Forwarded-Port": "443", "X-Forwarded-Proto": "https"}, "multiValueHeaders": {"accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9"], "accept-encoding": ["gzip, deflate, br"], "accept-language": ["en-US,en;q=0.9"], "cookie": ["s_fid=7AABXMPL1AFD9BBF-0643XMPL09956DE2; regStatus=pre-register;"], "Host": ["70ixmpl4fl.execute-api.ca-central-1.amazonaws.com"], "sec-fetch-dest": ["document"], "sec-fetch-mode": ["navigate"], "sec-fetch-site": ["none"], "upgrade-insecure-requests": ["1"], "User-Agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36"], "X-Amzn-Trace-Id": ["Root=1-5e66d96f-7491f09xmpl79d18acf3d050"], "X-Forwarded-For": ["*************"], "X-Forwarded-Port": ["443"], "X-Forwarded-Proto": ["https"]}, "queryStringParameters": null, "multiValueQueryStringParameters": null, "pathParameters": null, "stageVariables": null, "requestContext": {"resourceId": "2gxmpl", "resourcePath": "/", "httpMethod": "GET", "extendedRequestId": "JJbxmplHYosFVYQ=", "requestTime": "10/Mar/2020:00:03:59 +0000", "path": "/Prod/", "accountId": "************", "protocol": "HTTP/1.1", "stage": "Prod", "domainPrefix": "70ixmpl4fl", "requestTimeEpoch": *************, "requestId": "********-xmpl-4b79-853a-f982474efe18", "identity": {"cognitoIdentityPoolId": null, "accountId": null, "cognitoIdentityId": null, "caller": null, "sourceIp": "*************", "principalOrgId": null, "accessKey": null, "cognitoAuthenticationType": null, "cognitoAuthenticationProvider": null, "userArn": null, "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36", "user": null}, "domainName": "70ixmpl4fl.execute-api.us-east-2.amazonaws.com", "apiId": "70ixmpl4fl"}, "body": null, "isBase64Encoded": false}