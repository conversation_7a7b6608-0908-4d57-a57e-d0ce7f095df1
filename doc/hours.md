# Hours Module Documentation

## Table of Contents
- [Overview](#overview)
- [Types](#types)
- [Functions](#functions)
  - [Core Functions](#core-functions)
  - [Period Operations](#period-operations)
    - [periodsMerge](#periodsmerge)
    - [periodsUnion](#periodsunion)
    - [periodsDifference](#periodsdifference)
    - [mergeSpecificDate](#mergespecificdate)
    - [aggregateHours](#aggregatehours)
- [Time Zone Handling](#time-zone-handling)
- [Usage Examples](#usage-examples)
  - [Check if a business is open](#check-if-a-business-is-open)
  - [Get next opening period](#get-next-opening-period)
  - [Convert schedule to hours format](#convert-schedule-to-hours-format)
  - [Aggregate hours from multiple sources](#aggregate-hours-from-multiple-sources)
  - [Merge a specific date into existing specific dates](#merge-a-specific-date-into-existing-specific-dates)

## Overview
The hours module provides utilities for working with business opening hours, time periods, and scheduling.

## Types

### Time
```typescript
type Time = {
  time: string  // 24hr ISO 8601 format (hh:mm), 00:00-24:00
}
```

### DayTime
```typescript
type DayTime = Time & {
  day: number  // day of week, 1-7, 7 = Sunday
}
```

### Period
```typescript
type Period = {
  open: DayTime
  close: DayTime
  busy?: DayTime
}
```

### TimePeriod
```typescript
type TimePeriod = {
  open: Time
  close: Time
  busy?: Time
}
```

### YearMonthDay
```typescript
type YearMonthDay = {
  year: number   // with century, 0 if not specified
  month: number  // 1-12 (1 = January), 0 if not specified
  day: number    // 1-31, 0 if not specified
}
```

### Hours
```typescript
type Hours = {
  specific?: {
    date: YearMonthDay
    periods: TimePeriod[]
  }[]
  periods: Period[]
}
```

### Schedule
```typescript
type Schedule = {
  date: string | number | Date,
  open: {
    start: string,  // 24hr format (hh:mm)
    end: string     // 24hr format (hh:mm)
  }[]
}
```

## Functions

### Core Functions

#### `hoursFor(hours, year, month, day)`
Get hours for a specific date.

**Parameters:**
- `hours: Hours` - Opening hours definition
- `year: number` - Year with century
- `month: number` - Month of year (1-12, where 1 = January)
- `day: number` - Day of month (1-31)

**Returns:** `Period[]` - Array of periods applicable for the specified date

#### `isLateNight(open, close)`
Check if a period extends past midnight.

**Parameters:**
- `open: Time` - Opening time
- `close: Time` - Closing time

**Returns:** `boolean` - True if the closing time is earlier than the opening time (indicating it's past midnight)

#### `within(open, close, start, end, dow)`
Check if a time range falls within opening hours.

**Parameters:**
- `open: DayTime` - Opening day and time
- `close: DayTime` - Closing day and time
- `start: number` - Start time in numeric format (HHMM)
- `end: number` - End time in numeric format (HHMM)
- `dow: number` - Day of week (1-7, where 7 = Sunday)

**Returns:** `boolean` - True if the specified time range falls within the opening hours

#### `isOpen(hours, from, to, timeZone)`
Check if a business is open during a specified time period.

**Parameters:**
- `hours: Hours` - Opening hours definition
- `from: Date | dayjs.Dayjs` - Start date/time
- `to?: Date | dayjs.Dayjs` - Optional end date/time (defaults to `from` if not provided)
- `timeZone?: string` - Optional time zone (defaults to system time zone)

**Returns:** `boolean` - True if the business is open during the entire specified period

#### `nextOpen(hours, from, timeZone)`
Get the next opening period from a given time.

**Parameters:**
- `hours: Hours` - Opening hours definition
- `from?: Date | dayjs.Dayjs` - Reference date/time (defaults to current time)
- `timeZone?: string` - Optional time zone (defaults to system time zone)

**Returns:** `{ start: Date, end: Date } | undefined` - The next opening period or undefined if none found

#### `openFrom(hours, from, timeZone)`
Get the opening time on a specific date.

**Parameters:**
- `hours: Hours` - Opening hours definition
- `from: Date | dayjs.Dayjs` - Reference date
- `timeZone?: string` - Optional time zone (defaults to system time zone)

**Returns:** `Date` - The opening time on the specified date

#### `openUntil(hours, date, timeZone)`
Get the closing time on a specific date.

**Parameters:**
- `hours: Hours` - Opening hours definition
- `date: Date | dayjs.Dayjs` - Reference date
- `timeZone?: string` - Optional time zone (defaults to system time zone)

**Returns:** `Date` - The closing time on the specified date

#### `startEndOf(hours, timeZone)`
Get the start and end times of hours (for specific dates).

**Parameters:**
- `hours: Hours` - Opening hours definition
- `timeZone?: string` - Optional time zone (defaults to system time zone)

**Returns:** `{ startTime?: Date, endTime?: Date }` - The start and end times, or empty object if no specific dates

#### `date2TimeString(dateOrStr, timeZone)`
Convert a Date to a time string in "HH:MM" format.

**Parameters:**
- `dateOrStr: Date | string` - Date object or date string
- `timeZone?: string` - Optional time zone (defaults to system time zone)

**Returns:** `string` - Time string in "HH:MM" format

#### `sortHours(periods)`
Sort hours in ascending order by open time (Sunday first).

**Parameters:**
- `periods: Period[]` - Array of periods to sort

**Returns:** `Period[]` - Sorted array of periods

#### `scheduleToHours(schedule)`
Convert a specific date schedule to Hours format.

**Parameters:**
- `schedule: Schedule[]` - Array of schedule objects

**Returns:** `Hours` - Hours object with specific dates

### Period Operations

#### `periodsMerge(list)`
Combine multiple lists of Periods, removing enclosed periods.

**Parameters:**
- `list: Period[][]` - Array of period arrays to merge

**Returns:** `Period[]` - Merged array of periods

#### `periodsUnion(a, b)`
Create a union of two lists of Periods.

**Parameters:**
- `a: Period[]` - First array of periods
- `b: Period[]` - Second array of periods

**Returns:** `Period[]` - Union of the two period arrays

#### `periodsDifference(periodsA, periodsB)`
Calculate the difference between two lists of Periods (A - B).

**Parameters:**
- `periodsA: Period[]` - First array of periods
- `periodsB: Period[]` - Second array of periods to subtract

**Returns:** `Period[]` - Difference of the two period arrays (A - B)

#### `mergeSpecificDate(specificDates, newDate)`
Merge a specific date into an array of specific dates.

**Parameters:**
- `specificDates: Hours['specific']` - Array of specific dates
- `newDate: NonNullable<Hours['specific']>[0]` - New specific date to merge

**Returns:** `NonNullable<Hours['specific']>` - Updated array of specific dates

**Behavior:**
- If the date already exists in the array, merges the periods using `periodsUnion`
- If the date doesn't exist, adds it (with a deep clone to avoid reference issues)
- Handles wildcards in date matching (year=0, month=0, day=0)
- Preserves the busy property when merging periods

#### `aggregateHours(hoursArray)`
Aggregate multiple hours objects into a combined hours object.

**Parameters:**
- `hoursArray: { hours: Hours }[]` - Array of objects with hours properties

**Returns:** `Hours` - Combined hours object

**Behavior:**
- Combines regular periods using `periodsUnion`
- Handles specific date overrides using `mergeSpecificDate`
- Preserves the busy property when merging periods
- Handles cases where periods or specific dates might be missing

## Usage Examples

### Check if a business is open
```typescript
import { isOpen } from './hours';

const businessHours = {
  periods: [
    { open: { day: 1, time: '09:00' }, close: { day: 1, time: '17:00' } },
    // ... other days
  ]
};

const now = new Date();
const isBusinessOpen = isOpen(businessHours, now);
```

### Get next opening period
```typescript
import { nextOpen } from './hours';

const nextOpeningPeriod = nextOpen(businessHours, now);
// Returns { start: Date, end: Date } or undefined
```

### Convert schedule to hours format
```typescript
import { scheduleToHours } from './hours';

const schedule = [
  {
    date: '2023-05-01',
    open: [{ start: '09:00', end: '17:00' }]
  }
];

const hours = scheduleToHours(schedule);
```

### Aggregate hours from multiple sources
```typescript
import { aggregateHours } from './hours';

const resource1 = {
  hours: {
    periods: [
      { open: { day: 1, time: '09:00' }, close: { day: 1, time: '17:00' } },
      { open: { day: 2, time: '09:00' }, close: { day: 2, time: '17:00' } }
    ],
    specific: [
      {
        date: { year: 2023, month: 5, day: 1 },
        periods: [{ open: { time: '10:00' }, close: { time: '15:00' } }]
      }
    ]
  }
};

const resource2 = {
  hours: {
    periods: [
      { open: { day: 3, time: '09:00' }, close: { day: 3, time: '17:00' } },
      { open: { day: 4, time: '09:00' }, close: { day: 4, time: '17:00' } }
    ],
    specific: [
      {
        date: { year: 2023, month: 5, day: 2 },
        periods: [{ open: { time: '10:00' }, close: { time: '15:00' } }]
      }
    ]
  }
};

// Combine hours from both resources
const combinedHours = aggregateHours([resource1, resource2]);
// Result will contain periods for days 1-4 and specific dates for May 1-2
```

### Merge a specific date into existing specific dates
```typescript
import { mergeSpecificDate } from './hours';

const existingDates = [
  {
    date: { year: 2023, month: 5, day: 1 },
    periods: [{ open: { time: '09:00' }, close: { time: '12:00' } }]
  }
];

const newDate = {
  date: { year: 2023, month: 5, day: 1 },
  periods: [{ open: { time: '14:00' }, close: { time: '18:00' } }]
};

// Merge the new date into existing dates
const updatedDates = mergeSpecificDate(existingDates, newDate);
// Result will contain one date (May 1) with two periods (9:00-12:00 and 14:00-18:00)
```