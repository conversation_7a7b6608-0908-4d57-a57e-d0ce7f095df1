# @perkd/utils

A comprehensive utility library for JavaScript/TypeScript applications.

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/perkd/sync)
[![Node](https://img.shields.io/badge/node->=20-green.svg)](https://nodejs.org/)
[![Coverage](https://img.shields.io/badge/coverage-93%25-brightgreen.svg)]()

## Installation

```sh
yarn add @perkd/utils
```

## Features

This library provides various utility functions organized into the following categories:

- **Addresses** - Address formatting and manipulation utilities
- **Card Numbers** - Credit card number validation and formatting
- **Currencies** - Currency formatting and conversion
- **Dates** - Date manipulation and formatting
- **Dev** - Development utilities including benchmarking
- **Emails** - Email validation and parsing
- **Events** - Event handling utilities
- **Flows** - Flow control utilities
- **Hours** - Opening hours and time period manipulation
- **Html** - HTML parsing and manipulation
- **Identities** - Identity-related utilities
- **Languages** - Language detection and localization
- **Lists** - List/Array manipulation utilities
- **Mongo** - MongoDB ObjectId utilities
- **Multitenancy** - Multi-tenant application utilities
- **Names** - Name formatting and parsing
- **Numbers** - Number formatting and manipulation
- **Objects** - Object manipulation utilities
- **Phones** - Phone number validation and formatting
- **Qualify** - Qualification utilities
- **RateLimit** - Rate limiting utilities
- **Scripts** - Script utilities
- **Security** - Security-related utilities
- **Sets** - Set manipulation utilities
- **Strings** - String manipulation utilities
- **Time** - Time manipulation utilities

## Usage

```typescript
import { Objects, Strings, Dates, Dev } from '@perkd/utils'

// Object utilities
const obj = { a: 1, b: { c: 2 } }
const cloned = Objects.cloneDeep(obj)

// String utilities
const formatted = Strings.capitalize('john doe') // 'John doe'

// Date utilities
const date = new Date()
const formattedDate = Dates.format(date)

// Benchmark utilities
const { bm } = Dev

// Mark start time
const start = bm.mark('operation')

// ... code to benchmark ...

// Get elapsed time in milliseconds
const elapsed = bm.elapsedTime('operation', true) // Logs elapsed time
// Or use diff directly
const diff = bm.diff(start) // Time since start in ms
```

## Examples

### Object Utilities

```typescript
import { Objects } from '@perkd/utils'

// Deep clone
const original = { a: 1, b: { c: 2 } }
const clone = Objects.cloneDeep(original)

// Pick properties
const obj = { a: 1, b: 2, c: 3 }
const picked = Objects.pick(obj, ['a', 'b']) // { a: 1, b: 2 }
```

### Time Utilities

```typescript
import { Time } from '@perkd/utils'

// Format to HH:mm
const time = Time.toHhmm(14, 30) // '14:30'

// Convert to AM/PM
const ampm = Time.hhmmToAmPm('14:30') // '2:30pm'
```

### Date Formatting

```typescript
import { dayjs } from '@perkd/utils'

// Format date (uses Asia/Singapore timezone by default)
const formatted = dayjs(new Date()).format('YYYY-MM-DD')
```

## Development

```bash
# Install dependencies
yarn

# Build
yarn build

# Run tests
yarn test

# Run specific test
yarn test-one tests/file.test.ts

# Run tests with coverage
yarn coverage

# View coverage report in browser
yarn coverage:view
```

## Test Coverage

This project uses [c8](https://github.com/bcoe/c8) for test coverage. Coverage reports are generated in HTML, LCOV, and text formats.

Current coverage status:
- Lines: 90%
- Statements: 90%
- Functions: 71.67%
- Branches: 85.68%

The coverage configuration is defined in `.c8rc.json`. To view the coverage report, run:

```bash
yarn coverage         # Run tests with coverage
yarn coverage:view    # Open the HTML coverage report in your browser
yarn coverage:badge   # Update the coverage badge in this README
```

Coverage reports are stored in the `coverage/` directory. The coverage badge at the top of this README can be automatically updated after running tests with the `yarn coverage:badge` command.
