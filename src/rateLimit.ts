import { RateLimiter, Interval } from 'limiter'

export type Limiter = RateLimiter

export type Rate = {
	limit: number
	interval: Interval
	methods?: string[]
}

export const delay = async(milliseconds: number): Promise<void>  => {
	return new Promise(resolve => setTimeout(resolve, milliseconds))
}

export const rateLimit = (rate: Rate, target?: any): Limiter => {
	const { limit, interval, methods = [] } = rate

	if (!(limit && interval)) throw new Error('Not rate limited, missing parameters (limit & interval)')

	const limiter = new RateLimiter({ tokensPerInterval: limit, interval })

	for (const path of methods) {
		const { parent, method = '' } = getParentAndMethod(target, path)

		if (typeof parent[method] === 'function') {
			const fn = parent[method].bind(parent)
			parent[method] = wrap(fn, limiter)
		}
		else {
			throw new Error(`Method '${path}' to rate limit not found`)
		}
	}

	return limiter
}

const getParentAndMethod = (obj: any = {}, path: string) => {
	const segs = path.split('.'),
		parent = segs.length === 1
			? obj 
			: segs.slice(0, segs.length - 1).reduce((res: { [x: string]: any }, prop: string) => res[prop], obj),
		method = segs[segs.length - 1]

	return { parent, method }
}

const wrap = (fn: Function, limiter: RateLimiter) => {
    return async (...args: any[]) => {
        try {
            await limiter.removeTokens(1)
            return fn(...args)
        }
		catch(e) {
            throw e
        }
    }
}
