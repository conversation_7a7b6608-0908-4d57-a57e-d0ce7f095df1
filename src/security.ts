import JWT, { Algorithm } from 'jws'
import isEmail from 'validator/lib/isEmail'

const TYP = 'JWT',
	HS256 = 'HS256',
	CODE_STRING = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'

export class Jwt {
	private secret: string

	constructor(secret: string) {
		this.secret = secret
	}

	static decode(token: string) {
		return JWT.decode(token)
	}

	encode(payload: any, header?: object) {
		return JWT.sign({
			header: { typ: TYP, ...header, alg: HS256 }, 	// Default to hash
			payload,
			secret: this.secret,
		})
	}

	decode(token: string) {		// alias
		return Jwt.decode(token)
	}

	verify(token: string, alg: Algorithm = HS256) {
		try {
			return JWT.verify(token, alg, this.secret)
		}
		catch (e) {
			return false
		}
	}
}

// strong-type, either undefined or 'string' only
export function safeCredentials(credentials: any = {}) {
	const { username, email, password, tenant } = credentials

	return (
		(username ? typeof username === 'string' : true) &&
		(email ? (typeof email === 'string' && isEmail(email)) : true) &&
		(tenant ? typeof tenant === 'string' : true) &&
		(password ? typeof password === 'string' : true)
	)
}

// remove vowels & non-word, last 6 char for randomness
export function generateKey(name: string, keyLength: number = 16) {
	const short = name
		? name.toLowerCase().replace(/[aeiou\W]/g, '').slice(0, keyLength - 6)
		: ''

	return short + (short.length < keyLength ? Math.random().toString().slice(short.length - keyLength) : '')
}

/**
 * Generate OTP code
 */
export function generateCode(length: number) {
	return randomString(length, '123456789')
}

export function randomString(size: number = 6, codeString: string = CODE_STRING): string {
	const maxNum = codeString.length

	let newCode = ''

	while (size > 0) {
		newCode += codeString.charAt(Math.floor(Math.random() * maxNum))
		size--
	}
	return newCode
}

export default {
	Jwt,
	safeCredentials,
	generateKey,
	generateCode,
	randomString
}
