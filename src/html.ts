import { Parser } from 'htmlparser2'

type IMAGE_REF = {
	original: string
}

type IMAGES = {
	unique: IMAGE_REF[]
	external: IMAGE_REF[]
	local: IMAGE_REF[]
}

type URL_REF = {
	original: string
	url: string
}

const IMAGE = 'img'		// image tag

/**
 * Extract image references in HTML (for substitution with substituteUrls())
 * @param html
 * @return image references: { unique: [{original: <url>}], external: [{original: <url>}], local: [{original: <url>}] }
 */
export function imageReferences(html: string): IMAGES {
	const parser = new Parser({ onopentag }, { decodeEntities: true }),
		images: IMAGES = {
			unique: [],
			external: [],
			local: [],
		}

	parser.write(html)
	parser.end()
	return images

	function onopentag(name: string, attribs: any) {
		if (name === IMAGE) {
			const src = attribs.src.toLowerCase()

			if (!images.unique.includes(src)) {
				const type = src.startsWith('http') ? 'external' : 'local'

				images.unique.push(src)
				images[type].push({ original: attribs.src })
			}
		}
	}
}

/**
 * Substitute URLs in html (replace 'original' with 'url')
 * @param src html
 * @param list of urls to replace: [{ original: <old>, url: <new> }]
 * @return html
 */
export function substituteUrls(src: string, list: URL_REF[]): string {
	const copy = src

	return list.reduce((html, { original, url }) => {
		const reg = new RegExp(original, 'gi')
		return html.replace(reg, url)
	}, copy)
}

export default {
	imageReferences,
	substituteUrls,
}
