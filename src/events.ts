/**
 * Used by Eventbus & related packages
 */

const WILD = '*'

export function domainOf(eventName = ''): string {
	return eventName.split('.')[0]
}

/**
 * Match str with pattern, wildcards supported (domain MUST be present):
 * 		domain.*
 * 		domain.actor*
 * 		domain.*.update
 * ILLEGAL: *, *.update, *.action*
 * @param str
 * @param pattern
 */
export function match(str: string, pattern: string): boolean {
	if (!pattern.includes(WILD)) return (pattern === str)

	const domain = domainOf(pattern),
		[before, after] = pattern.split(WILD)

	// Reject patterns that are just a domain or start with a wildcard
	if (domain === pattern || pattern.startsWith(WILD)) return false

	return (!before || (before && str.startsWith(before))) &&
		(!after || (after && str.endsWith(after))) ? true : false
}

export default {
	domainOf,
	match,
}
