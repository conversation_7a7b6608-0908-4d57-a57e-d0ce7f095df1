import { resolve } from 'node:path'

const SCRIPT_ROOT = '/lib/providers'

export function loadPackage(name: string) {
	try {
		return require(name)
	}
	catch (err) {
		return err
	}
}

export function loadModule(name: string, path: string) {
	try {
		return require(scriptPath(name, path))
	}
	catch (err) {
		return err
	}
}

export function scriptPath(filename?: string, pathStr: string = SCRIPT_ROOT) {
	return `${resolve(__dirname)}${pathStr}${filename ? '/' + filename : ''}`
}

export default {
	loadPackage,
	loadModule,
	scriptPath
}
