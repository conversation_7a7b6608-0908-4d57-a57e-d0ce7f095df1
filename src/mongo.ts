import ObjectId from 'bson-objectid'
import { nanoid } from 'nanoid'
import isMongoId from 'validator/lib/isMongoId'

export type DatasourceOptions = {
	name: string
	host: string
	username?: string
	password?: string
	authDb?: string
	dbSet?: string
	maxPoolSize?: string
}

const MAX_POOL_SIZE = 5,
	SHORT_ID_LENGTH = 10

export function getDatabaseUrl(options: DatasourceOptions) {
	const { name, host, username, password, authDb, dbSet, maxPoolSize = MAX_POOL_SIZE } = options

	return 'mongodb://'
		+		((username)
			? `${username}:${password}@`
			: ''
		)
		+		`${host}/${name}?authSource=${authDb}`
		+		((dbSet)
			? `&replicaSet=${dbSet}&readPreference=primaryPreferred&slaveOk=true`
			: ''
		)
		+		`&useNewUrlParser=true&useUnifiedTopology=true&keepAlive=false&maxPoolSize=${maxPoolSize}&socketTimeoutMS=360000&connectTimeoutMS=10000&maxIdleTimeMS=300000`
}

export function shortId(len = SHORT_ID_LENGTH) {
	return nanoid(len)
}

export { ObjectId }

export default {
	isMongoId,
	getDatabaseUrl,
	shortId
}
