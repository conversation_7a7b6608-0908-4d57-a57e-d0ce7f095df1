import getSymbol from 'currency-symbol-map'
import { round } from './numbers'
import Currencies from './currencies.json'	// from country-data with TWD decimals fixed (2 in package)

// based on ISO 4217
export function currencySymbol(code: string = '', short?: boolean): string {
	const symbol = getSymbol(code.toUpperCase()) || '$'
	return symbol.endsWith('$') && short ? '$' : symbol
}

// country = ISO 3166-1 alpha-2 (uppercase)
// locale: used for i18Next native format
export function currency(code: string = '', short?: boolean) {
	const currencyCode = code.toUpperCase(),
		symbol = currencySymbol(currencyCode, short),
		{ decimals = 2, name = '', country, locale } = Currencies.find(c => c.code === currencyCode) || {}

	return { decimals, symbol, name, country, locale }
}

export function isValid(code: string = ''): boolean {
	const currencyCode = code.toUpperCase()
	return Currencies.some(c => c.code === currencyCode)
}

/**
 * Convert native amount to zeroDecimal
 * 	- Multiply by 10^decimals and truncate to handle floating point precision issues
 * 	- Use a string-based approach to avoid floating point precision issues
 * @param amount
 * @param currencyCode
 */
export function zeroDecimal(amount: number, currencyCode: string = ''): number {
	const { decimals } = currency(currencyCode),
		multiplier = Math.pow(10, decimals),
		amountStr = (amount * multiplier).toFixed(10),
		decimalIndex = amountStr.indexOf('.')

	return parseInt(decimalIndex > 0
		? amountStr.substring(0, decimalIndex)
		: amountStr)
}

/**
 * Convert zeroDecimal amount to native amount
 * @param amount 
 * @param currencyCode 
 */
export function nativeAmount(amount: number, currencyCode: string = ''): number {
	const { decimals } = currency(currencyCode)
	return Number((amount / Math.pow(10, decimals)).toFixed(decimals))
}

export function roundAmount(amount: number, currencyCode: string = ''): number {
	const { decimals } = currency(currencyCode)
	return round(amount, decimals)
}

export function currencyFormat(amount: number, currencyCode: string = ''): string {
	const { locale, decimals, symbol } = currency(currencyCode)

	if (!currencyCode) {
		return `${symbol}${amount.toFixed(decimals)}`
	}

	try {
		return new Intl.NumberFormat(locale, {
			style: 'currency',
			currency: currencyCode,
			minimumFractionDigits: decimals,
			maximumFractionDigits: decimals
		}).format(amount)
	}
	catch (error) {
		return `${symbol}${amount.toFixed(decimals)}`
	}
}

export default {
	currency,
	currencySymbol,
	isValid,
	zeroDecimal,
	nativeAmount,
	roundAmount,
	currencyFormat
}
