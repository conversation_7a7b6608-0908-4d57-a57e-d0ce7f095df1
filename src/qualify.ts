import sift, { createEqualsOperation } from 'sift'
import ObjectId from 'bson-objectid'
import traverse from 'traverse'
import getValue from 'get-value'
import isMongoId from 'validator/lib/isMongoId'
import { formatDateTime as dayjs } from '@perkd/format-datetime'
import { TIMEZONE } from './dates'

export function satisfyCondition(object: any = {}, condition: null | string | any, options?: object): boolean {
	try {
		if (!condition) return true

		if (typeof condition === 'string') {
			condition = JSON.parse(condition)
		}
		const decoded = decodeFilterKeys(condition),
			formatted = substituteMoments(decoded)

		return sift(formatted, options)(object)
	}
	catch (err) {
		return false
	}
}

export function decodeFilterKeys(filter: object) {
	// replace "_" to "." in filter key
	// replace "#" to "$" in filter key
	// example: {"where":{"gender":2,"dateList_month":3,"dateList_name":"birth"}}
	// replace to : {"where":{"gender":2,"dateList.month":3,"dateList.name":"birth"}}

	return traverse(filter).forEach(decode)

	function decode(this: any, node: any) {
		if (node && typeof node === 'object') {
			let newKey = ''

			for (const key in node) {
				if (key !== '_gId' && key !== '_data' && key !== '_id') {
					newKey = key.replace(/#/i, '$')
					newKey = newKey.replace(/(?<!(\.|\b|_))_/ig, '.')
					node[newKey] = node[key]
					if (key !== newKey) {
						node[newKey] = decodeFilterKeys(node[newKey])
						delete node[key]
					}
				}
			}
			this.update(node)
		}
	}
}

// sift operations
export const operations = {

	// usage: {'member.behaviors.purchase.last.at': { $ltDays: 540 }}
	// means the days between current date and value of 'member.behaviors.purchase.last.at', should be less than 540 days
	$ltDays(params: number, ownerQuery: any, options: any): any {
		return createEqualsOperation(
			(value: number) => (new Date().getTime() - value) < (params * 24 * 60 * 60 * 1000),
			ownerQuery,
			options
		)
	},

	// usage: { "endTime":{"$gtDays":-364} }
	$gtDays(params: number, ownerQuery: any, options: any): any {
		return createEqualsOperation(
			(value: number) => (new Date().getTime() - value) > (params * 24 * 60 * 60 * 1000),
			ownerQuery,
			options
		)
	},
	/*
	 * Apply quaifiers for max count of memberships of same program, for example: Gift Card
	 * @param	{Object} params - {
	 * 				properties: ['memberships.current'],
	 * 				query: { 'programId': { $eq: '62dc1aa50e4b5d001d9492da' } },
	 * 				compare: { $gte: 3 }
	 * 			}
	 *			{Object} properties[] - get the arrays to filter
	 *			{Object} query - apply to previous result
	 *			{Object} compare - check count of previous result
	 */
	$count(params: any, ownerQuery: any, options: any) {
		const { properties, query, compare } = params,
			qualifier = sift(query),
			check = sift(compare),
			fn = (profile: any) => {
				const count = properties.reduce(
					(result: number, property: string) => {
						const arr = getValue(profile || {}, property)
						return result + arr.filter(qualifier).length
					},
					0
				)
				return (profile ? check(count) : false)
			}

		return createEqualsOperation(fn, ownerQuery, options)
	},

	/*
	 * Apply quaifiers for total amount of selected items within an order, for example: different spend on selected products triggers different offers
	 * @param	{Object} params - {
	 * 				properties: ['itemList'],
	 * 				query: { 'product.external.shopify.productId': { $in: ['7938905702569','7938905669801'] } },
	 * 				compare: { $gte: 50 }
	 * 				field: 'price'
	 * 			}
	 *			{Object} properties[] - get the arrays to filter
	 *			{Object} query - apply to previous result
	 *			{Object} compare - check total amount of previous result
	 *			{String} field - which field to sum
	 */
	$sum(params: any, ownerQuery: any, options: any) {
		const { properties, query, compare, field } = params,
			qualifier = sift(query),
			check = sift(compare),
			fn = (profile: any) => {
				const total = properties.reduce(
					(result: number, property: string) => {
						const arr = getValue(profile || {}, property),
							filtered = arr.filter(qualifier),
							subTotal = filtered.reduce((t: number, item: { [key: string]: number } = {}) => t + (item[field] ?? 0), 0)
						return result + subTotal
					},
					0
				)
				return (profile ? check(total) : false)
			}

		return createEqualsOperation(fn, ownerQuery, options)
	},

	// $cmp(params, ownerQuery, options) {
	// 	return sift.createEqualsOperation(
	// 		value => _.get(value, params[0]) === _.get(value, params[1]),
	// 		ownerQuery, options
	// 	);
	// },
}

/**
 * Customize datetime in JSON Object
 */
export function substituteMoments(obj: any, values = {}, timeZone: string = TIMEZONE) {
	if (typeof obj !== 'object') return obj

	const now = dayjs().tz(timeZone),
		yesterday = dayjs().tz(timeZone).subtract(1, 'day'),
		tomorrow = dayjs().tz(timeZone).add(1, 'day'),
		variables: any = Object.assign({
			$now: new Date(now.format()),
			$date: now.format('YYYY-MM-DD'),
			$day: Number(now.format('D')),
			$month: Number(now.format('M')),
			$year: Number(now.year()),
			$yesterday: yesterday.format('YYYY-MM-DD'),
			$tomorrow: tomorrow.format('YYYY-MM-DD'),
		}, values)

	traverse(obj).forEach(substitute)

	return obj

	function substitute(this: any, node: any | string) {
		const result = Object.keys(node || {}).find(key => key !== 'moment' && typeof (<any>now)[key] !== 'function')

		if (node && typeof node === 'object' && node.moment && !result) {
			this.update(calcTime(node))
		}
		else if (node && typeof node === 'string') {
			if (node.toLowerCase().indexOf('$startof') === 0) {
				const unit = <dayjs.OpUnitType>node.slice(8).toLowerCase()
				this.update(new Date(now.startOf(unit).format()))
			}
			else if (node.toLowerCase().indexOf('$endof') === 0) {
				const unit = <dayjs.OpUnitType>node.slice(6).toLowerCase()
				this.update(new Date(now.endOf(unit).format()))
			}
			else if (typeof variables[node] !== 'undefined') {
				this.update(variables[node])
			}
			else if (node.indexOf('$') === 0 && isMongoId(node.slice(1))) {
				this.update(ObjectId(node.slice(1)))
			}
		}
	}

	function calcTime(node: any): Date | number | dayjs.Dayjs {
		let time: any = (node.moment.toLowerCase() === 'now' ? dayjs() : dayjs(node.moment))
			.tz(node.tz || timeZone)

		for (const [ key, value ] of Object.entries(node)) {
			if (typeof (<any>time)[key] === 'function') {
				time = (value === null) ? (<any>time)[key]() : (<any>time)[key](value)
				if (key === 'month') time += 1
			}
		}

		return dayjs.isDayjs(time)
			? time.toDate()
			: (node.format?.toLowerCase() === 'x') ? Number(time) : time
	}
}

export function addCondition(where: any = {}, condition: any, operator: 'and' | 'or' = 'and') {
	const op = Object.keys(where)[0]?.toLowerCase()
	if (op === 'and') {
		if (operator === 'and') {
			if (!where.and) where.and = []
			where.and.push(condition)
		}
		else {
			where = { or: [ condition, { and: where.and } ] }
		}
	}
	else if (op === 'or') {
		if (operator === 'and') {
			where = { and: [ condition, { or: where.or } ] }
		}
		else {
			if (!where.or) where.or = []
			where.or.push(condition)
		}
	}
	else {
		if (operator === 'and') {
			Object.assign(where, condition)
		}
		else {
			where = { or: [ condition, where ] }
		}
	}
	return where
}

/**
 * Replace $xxxx in JSON Object
 * $xxxx can be any type
 */
export function substituteVariables(obj: any, values: { [key: string]: any }, json?: boolean): any {
	if (typeof obj === 'object' && !json) {
		traverse(obj).forEach(function(node) {
			if (node && typeof node === 'string' && typeof values[node] !== 'undefined') {
				this.update(values[node])
			}
		})
		return obj
	}
	let str = JSON.stringify(obj)
	for (const v in values) {
		if ({}.hasOwnProperty.call(values, v)) {
			const regex = new RegExp('\\$' + v, 'g')
			str = str.replace(regex, values[v])
		}
	}
	return JSON.parse(str)
}

export default {
	satisfyCondition,
	decodeFilterKeys,
	operations,
	substituteMoments,
	addCondition,
	substituteVariables
}
