import similar from 'similarity'
import {isIP as isIPValidator} from 'validator'

/**
 * Substitute placeholder in string with data
 * @param {String} str - ie. 'example: {text} high {label}'
 * @param {Object} data - { text: 'foo', label: 'bar' }
 * @returns {String} - 'example: foo high bar'
 */
export function mustache(str = '', data: any): string {
	return str.replace(/\{(\w+?)\}/gi, (match, group) => data[group] || '')
}

export function hasUnicode(str: string): boolean {
	return /[^\u0000-\u00ff]/.test(str)
}

export function isChinese(str: string): boolean {
	return /[\u4e00-\u9fff]/.test(str)
}

export function similarity(a: any, b: any) {
	const convertType = (C: any) => {
			const Ctype = typeof C
			switch (Ctype) {
				case 'object':
					if (!isNaN(Date.parse(C))) return C.toISOString()
					break
				case 'boolean': return C ? '1' : '0'
				default: return C
			}
		},
		A = convertType(a),
		B = convertType(b),
		Atype = typeof A,
		Btype = typeof B

	if (Atype === Btype) {
		if ('object' === Atype || 'number' === Btype) return (A === B) ? 1 : 0
	}

	return similar(A, B)
}

export function parseUrl(url: string) {
	// @Young
	// see:   http://stackoverflow.com/questions/27745/getting-parts-of-a-url-regex
	const parsed = url.match(/^((http[s]?|ftp):\/)?\/?([^:\/\s]+)(:([^\/]*))?((\/\w+)*\/)([\w\-\.]+[^#?\s]+)(\?([^#]*))?(#(.*))?$/)

	if (!parsed) return

	let apiRoot = parsed[6],
		apiPath = parsed[8]

	const fullpath = apiRoot + apiPath,
		versionStr = fullpath.match(/v[0-9]+\//)

	let version = 0

	if (versionStr) {
		const paths = fullpath.split(versionStr[0])
		apiRoot = paths[0]
		apiPath = paths[1]
		version = parseInt(versionStr[0].substring(1).replace(/\/$/, ''))
	}

	return {
		protocol: parsed[2],
		hostname: parsed[3],
		port: parsed[5],
		apiRoot: apiRoot.replace(/\/$/, ''), // remove trailing '/'
		apiVersion: version,
		apiPath: '/' + apiPath,
		pathname: fullpath,
		file: parsed[8],
		query: parsed[9],
		hash: parsed[12],
	}
}

export function isUrl(str: string) {
	return str ? !!str.match(/^\w+:\/\//gi) : false
}

export function isIP(str: string): boolean {
	return isIPValidator(str);
}

export default {
	mustache,
	hasUnicode,
	isChinese,
	similarity,
	parseUrl,
	isUrl,
	isIP
}
