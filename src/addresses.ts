import { cloneDeep } from './objects'

// TODO: import from lookup

enum GeoType {
	POINT = 'Point'
}

export enum AddressType {
	HOME = 'home',
	WORK = 'work',
	OTHERS = 'others'
}

export type Address = {
	full?: string
	street?: string
	floor?: string
	unit?: string
	level?: string
	formatted?: string
	short?: string
	house?: string
	premise?: string
	unitNo?: string
	city?: string
	state?: string
	country?: string
	postCode?: string
	countryCode?: string
}

// from Google Map, partial (https://developers.google.com/maps/documentation/geocoding/start)
type AddressComponent = {
	long_name: string,
	short_name: string,
	types: string[],
	index?: number
}

type LatLng = {
	lat: number
	lng: number
}

type Geometry = {
	location: LatLng
	location_type: string
	viewport: {
		northeast: LatLng
		southwest: LatLng
	}
}

export type GeoCoding = {
	address_components: AddressComponent[]
	formatted_address: string
	geometry: Geometry
	place_id: string
	plus_code: {
		compound_code: string
		global_code: string
	}
	types: string[]
}

const STORE = 'store'

const COUNTRY_NAME: { [code: string]: string } = {
	TW: '台灣',
	SG: 'Singapore',
	MY: 'Malaysia',
	HK: 'Hong Kong',
	JP: 'Japan',
	KR: 'Korea',
},
	FORMATS: { [ key: string ]: any } = {
		Default: {
			full: 'No.{unit}, {floor}/F',
			floor: '{floor}/F',
			unit: 'No.{unit}',
			formatted: '{unitNo} {address}', // TODO:
			short: '{unitNo} {address}', // TODO:
		},
		SG: {
			full: '#{floor}-{unit}',
			floor: '#{floor}',
			unit: '#{unit}',
			formatted: '{house} {street}, {unitNo} {premise}, {country} {postCode}',
			short: '{house} {street}, {unitNo} {premise}',
		},
		TW: {
			full: '{floor}樓之{unit}',
			floor: '{floor}樓',
			unit: '之{unit}號',
			formatted: '{postCode}{country}{state}{street}{house}{premise}{unitNo}',
			short: '{state}{street}{house}{premise}{unitNo}',
		},
		MY: {
			full: 'Lot {unit}, {floor}/F',
			floor: '{floor}/F',
			unit: 'Lot {unit}',
			formatted: '{house} {unitNo}, {street}, {postCode} {city} {state}, {country}',
			short: '{house} {unitNo}, {street}',
		},
		CN: {
			full: '{floor}楼{unit}号',
			floor: '{floor}楼',
			unit: '{unit}号',
			formatted: '{address} {unitNo}', // TODO: improve format, replace {address} with combination of {country}{state}{city}... etc.
			short: '{unitNo} {address}', // TODO:
		},
		JP: {
			full: '{floor}F {unit}',
			floor: '{floor}F',
			unit: '{unit}',
			formatted: '{address} {unitNo}', // TODO:
			short: '{unitNo} {address}', // TODO:
		},
		US: {
			full: 'Level {floor}, Suite {unit}',
			floor: 'Level {floor}',
			unit: 'Suite {unit}',
			formatted: '{address} {unitNo}', // TODO:
			short: '{unitNo} {address}', // TODO:
		},
	},
	ComponentsToRemove = [ 'country', 'state', 'postCode', 'house', 'premise', 'unitNo' ]		// for getting street from formatted or short

export function formatAddress(address: Address): Address {
	address.short = format('short', address)
	address.formatted = format('formatted', address)

	return address
}

export function addressComponents2Address(addressComponents: AddressComponent[], formattedAddr: string): Address {
	const SHORT_CONFIG: { [ key: string ]: any } = {
		DEFAULT: { include: true, types: [ 'route', 'street_number' ] },
		TW: { include: true, types: [ 'route', 'street_number', 'administrative_area_level_3' ] },
		HK: { include: true, types: [ 'route', 'street_number', 'neighborhood' ] },
		MY: { include: true, types: [ 'route', 'street_number', 'administrative_area_level_1' ] },
		AU: { include: true, types: [ 'route', 'street_number', 'administrative_area_level_2' ] },
	}

	const postCode = addressComponents.find(comp => comp.types
			&& comp.types.indexOf('postal_code') >= 0),
		country = addressComponents.find(comp => comp.types
			&& comp.types.indexOf('country') >= 0),
		state = addressComponents.find(comp => comp.types
			&& comp.types.indexOf('neighborhood') >= 0 || comp.types.indexOf('administrative_area_level_1') >= 0),
		city = addressComponents.find(comp => comp.types
			&& (comp.types.indexOf('locality') >= 0 || comp.types.indexOf('administrative_area_level_2') >= 0)),
		street1 = addressComponents.find(comp => comp.types
			&& (comp.types.indexOf('route') >= 0 || comp.types.indexOf('administrative_area_level_3') >= 0)),
		street2 = addressComponents.find(comp => comp.types
			&& (comp.types.indexOf('administrative_area_level_4') >= 0)),
		house = addressComponents.find(comp => comp.types
			&& comp.types.indexOf('street_number') >= 0),
		level = addressComponents.find(comp => comp.types
			&& comp.types.indexOf('floor') >= 0),
		street = street2
			? (street2.long_name + ' ' + street1?.long_name)
			: street1?.long_name

	const { short_name } = country ?? {},
		shortConfig = (short_name && SHORT_CONFIG[short_name])
			? SHORT_CONFIG[short_name]
			: SHORT_CONFIG.DEFAULT,
		address = {
			type: STORE,
			level: level?.short_name,
			house: house?.short_name,
			city: city?.short_name,
			street,
			state: state?.short_name,
			country: country?.short_name,
			postCode: postCode?.short_name,
			formatted: formattedAddr,
			short: getShortAddress(addressComponents, formattedAddr, shortConfig.types, shortConfig.include),
			optIn: true,
		}

	return address

	function getShortAddress(addressComponents: AddressComponent[], formattedAddr:string, types: string[], include = true) {
		const indexedComp = indexComponents(formattedAddr, addressComponents),
			sortedComp = indexedComp.sort((a, b) => (a.index || 0) - (b.index || 0)),
			shortNames = []

		for (let i = 0; i < sortedComp.length; i++) {
			const comp = sortedComp[i].types.find(type => types.includes(type))

			if (include ? comp : !comp) {
				const { short_name: short, long_name: long } = sortedComp[i],
					hasComma = formattedAddr.includes(short + ',') || formattedAddr.includes(long + ','),
					hasSpace = formattedAddr.includes(short + ' ') || formattedAddr.includes(long + ' ')

				shortNames.push(hasComma ? (short + ', ') : (hasSpace ? (short + ' ') : short))
			}
		}
		const shortAddr = shortNames.join('').trim().replace(/(^,)|(,$)/g, '')
		if (shortAddr) return shortAddr

		const filteredAddr = filterAddress(formattedAddr, addressComponents)
		return filteredAddr
	}

	function indexComponents(formattedAddr:string, addressComponents: AddressComponent[]) {
		const fullAddr = formattedAddr

		for (let i = 0; i < addressComponents.length; i++) {
			const comp = addressComponents[i]
			if (comp.types.includes('street_number')) (formatStreetNumber(comp, '號') || formatStreetNumber(comp, '号'))

			const longIndex = fullAddr.indexOf(comp.long_name)
			if (longIndex !== -1) {
				comp.index = longIndex; continue
			}

			const shortIndex = fullAddr.indexOf(comp.short_name)
			comp.index = shortIndex
		}
		return addressComponents.filter(comp => comp.index !== -1)
	}

	function formatStreetNumber(comp: AddressComponent, str: string) {
		const long = comp.long_name + str,
			short = comp.short_name + str

		if (formattedAddr.includes(long)) comp.long_name = long
		if (formattedAddr.includes(short)) comp.short_name = short

		return long || short
	}

	function filterAddress(formattedAddr: string, addressComponents: AddressComponent[]) {
		let filteredAddr = formattedAddr
		const excludes = addressComponents.filter(comp => comp.types.includes('country') || comp.types.includes('postal_code'))

		for (let i = 0; i < excludes.length; i++) {
			const { long_name: long, short_name: short } = excludes[i]

			filteredAddr = (formattedAddr.includes(long)) ? filteredAddr.replace(long, '') : filteredAddr.replace(short, '')
		}
		return filteredAddr.trim().replace(/(^,)|(,$)/g, '')
	}
}

export function completeAddress(address: any, geocoding: GeoCoding): Address {
	const result = cloneDeep(address.toJSON ? address.toJSON() : address),
		{ address_components, formatted_address, geometry } = geocoding,
		parsed = addressComponents2Address(address_components, formatted_address),
		{ house, level, unit, street, city, state, postCode, country, short } = parsed,
		{ location } = geometry,
		{ lng, lat } = location

	if (!address.house) result.house = house
	if (!address.level) result.level = level
	if (!address.unit) result.unit = unit
	if (!address.street) result.street = street
	if (!address.city) result.city = city
	if (!address.state) result.state = state
	if (!address.postCode) result.postCode = postCode
	if (!address.country) result.country = country
	if (!address.short) result.short = short
	if (!address.geo) result.geo = { type: GeoType.POINT, coordinates: [ lng, lat ] }
	if (address.valid === undefined) result.valid = true

	return result
}

export default {
	formatAddress,
	completeAddress,
	addressComponents2Address
}

// -----  Private Functions  -----

function format(type: 'short' | 'formatted', address: Address) {
	const addr = address[type] || '',
		{ house = '', level = '', unit = '', country: countryCode = '' } = address,
		country = COUNTRY_NAME[countryCode],
		unitNo = formatUnitNo(level, unit, countryCode) || '',
		fallback = type === 'short' ? 'formatted' : 'short',
		fallbackAddr = address[type] || address[fallback] || '',
		street = getStreet(address.street || fallbackAddr, { ...address, unitNo, country, countryCode })	// use when street is missing

	// if (unitNo && addr.includes(unitNo)) return addr; // TODO: use app version

	const FORMAT = (FORMATS[countryCode] || FORMATS.Default)[type],
		data = { ...address, street, unitNo, country },
		components = FORMAT.split(', '),
		trimmed = trim(components.reduce((res: string, component: string) => {
			const str = trim(substitute(component, data))
			return str ? (res ? `${res}, ${str}` : str) : res
		}, ''))

	return trimmed || fallbackAddr
}

function formatUnitNo(floor = '', unit = '', country: string): string {
	if (!unit && !floor) return ''

	const {
			full = FORMATS.Default.full,
			floor: floorFormat = FORMATS.Default.floor,
			unit: unitFormat = FORMATS.Default.unit,
		} = FORMATS[country] || FORMATS.Default,
		symbol = full.replace('{floor}', '').replace('{unit}', '')

	if ((unit && symbol.split('').some((s: any) => unit.includes(s))) && !floor) {
		return unit
	}

	const floorSymbols = floorFormat.replace('{floor}', ''),
		unitSymbols = unitFormat.replace('{unit}', ''),
		cleansedFloor = cleanSymbols(floor, floorSymbols.split('')),
		cleansedUnit = cleanSymbols(unit, unitSymbols.split(''))

	if (unit && floor) return substitute(full, { unit: cleansedUnit, floor: cleansedFloor })
	if (floor) return substitute(floorFormat, { floor: cleansedFloor })
	if (unit) return substitute(unitFormat, { unit: cleansedUnit })

	return ''
}

// -- Utils --

function substitute(str = '', replacements: any): string {
	return str.replace(/\{(.+?)\}/gi, (match, group) => (replacements[group] || ''))
}

function trim(str: string): string {
	return str.trim().replace(/(^,)|(,$)/g, '').trim()
}

function cleanSymbols(str: string, symbols: any): string {
	return symbols.reduce((res: string, symbol: string) => res.replace(symbol, ''), str || '')
}

function getStreet(str: string, address: Address): string {
	const { countryCode = '' } = address,
		formatted = (FORMATS[countryCode] || FORMATS.Default).formatted,
		sections = formatted.split(', ')

	return sections.reduce((res: string, section: string) => {
		let replacement = section

		const match = trim(section.replace(/\{(.+?)\}/gi, (match, group: string) => {
			const component = (<any>address)[group] || '',
				same = component === str

			replacement = trim(replacement.replace(`{${group}}`, same || ComponentsToRemove.includes(group) ? '' : component))

			return (!same && str.includes(component)) ? component : ''
		}))

		return (match && match !== replacement) ? trim(res.replace(match, replacement)) : res
	}, str || '')
}
