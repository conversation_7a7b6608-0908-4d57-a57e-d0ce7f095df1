/**
 * Flows Module - defines a Flow as a sequence of steps and provides utility
 *		functions to navigate through these steps.
 *
 * The Flow type represents a multi-step process using:
 *  - `at`: the current step index.
 *  - `steps`: an array of step names (strings) that defines the sequence.
 *
 * These functions are useful for managing the progression and transition logic
 * in multi-step processes such as registration, onboarding, or wizard interfaces.
 */

export type Flow = {
	at: number
	steps: string[]
}

/**
 * Get the position of next step for the flow
 * @param step one of the step names in the flow, eg. request, register, share, done...
 * @param flow
 */
export function nextStepOf(step: string, flow: Flow): number {
	const currentIndex = stepOf(step, flow)
	// Return -1 if the step doesn't exist in the flow
	if (currentIndex === -1) return -1

	const nextIndex = currentIndex + 1
	return flow.steps[nextIndex] ? nextIndex : currentIndex
}

/**
 * Get the position of previous step for the flow
 * @param step name
 * @param flow
 */
export function preStepOf(step: string, flow: Flow): number {
	const index = stepOf(step, flow) - 1
	return flow.steps[index] ? index : index + 1
}

/**
 * Get the position of given step in the flow
 * @param step name
 * @param flow
 */
export function stepOf(step: string, flow: Flow): number {
	return indexOf(step, flow.steps)
}

/**
 * Check if the step is in the flow
 * @param step name
 * @param flow
 */
export function hasStep(step: string, flow: Flow): boolean {
	return (flow.steps || []).includes(step)
}

/**
 * Check if the step is at step in the flow
 * @param step name
 * @param flow
 */
export function atStep(step: string, flow: Flow): boolean {
	const { at } = flow
	return stepOf(step, flow) === at
}

/**
 * Check if the step is the last step in the flow
 * @param flow
 * @param step name
 */
export function isLastStep(flow: Flow, step?: string): boolean {
	const { at, steps } = flow,
		last = steps.length - 1

	return step ? indexOf(step, steps) === last : at === last
}

function indexOf(step: string, steps: string[] = []): number {
	return steps.findIndex(s => s === step)
}

export default {
	nextStepOf,
	preStepOf,
	stepOf,
	atStep,
	hasStep,
	isLastStep
}
