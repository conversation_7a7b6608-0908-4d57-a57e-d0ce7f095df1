import { formatDateTime as dayjs } from '@perkd/format-datetime'
import { pickDeep } from './objects'

export const TIMEZONE = 'Asia/Singapore'

enum TimeUnit {
	SECOND = 'second',
	MINUTE = 'minute',
	HOUR = 'hour',
	DAY = 'day',
	MONTH = 'month',
	YEAR = 'year'
}

type Rule = {
	base?: string
	duration?: string
	startOf?: string
	endOf?: string
}

type Period = {
	startTime: Date
	endTime: Date
}

type DateDetail = {
	year: number
	month: number
	day: number
}

const { YEAR, MONTH, DAY, HOUR, MINUTE, SECOND } = TimeUnit,
	NOW = 'now'

export function sameDay(d1: Date, d2: Date, timeZone: string = TIMEZONE): boolean {
	const formatter = new Intl.DateTimeFormat('en', { year: 'numeric', month: 'long', day: 'numeric', timeZone })

	return formatter.format(d1) === formatter.format(d2)
}

/**
 * Check if given 2 dates are at the same day		// FIXME @zhangli
 */
export function isSameDay(d1: Date, d2: Date): boolean {
	// return d1.getFullYear() === d2.getFullYear()
	// 	&& d1.getMonth() === d2.getMonth()
	// 	&& d1.getDate() === d2.getDate()

	return d1.toISOString().split('T')[0] === d2.toISOString().split('T')[0]
}

/**
 * Get difference days of given 2 dates
 */
export function diffDays(d1: Date, d2: Date): number | null {
	if (!(d1 && d2)) return null

	const diffMS = Math.abs(new Date(d2).getTime() - new Date(d1).getTime()),
		day1MS = 1000 * 60 * 60 * 24

	return Math.floor(diffMS / day1MS)
}

/**
 * https://day.js.org/docs/en/manipulate/start-of
 * @param unit - hour, day, week, month, quarter, year...
 * @param date - numeric => relative days (+/-) to now, default now
 */
export function startOf(unit: dayjs.OpUnitType, date?: Date | string | number, timeZone: string = TIMEZONE): Date {
	const NOW = dayjs().tz(timeZone),
		at = date && typeof date === 'number'
			? (date < 0 ? NOW.subtract(Math.abs(date), DAY) : NOW.add(date, DAY))
			: dayjs(date).tz(timeZone)

	return at.startOf(unit).toDate()
}

/**
 * https://day.js.org/docs/en/manipulate/end-of
 * @param unit - hour, day, week, month, quarter, year...
 * @param date - numeric => relative days (+/-) to now, default now
 */
export function endOf(unit: dayjs.OpUnitType, date?: Date | string | number, timeZone: string = TIMEZONE): Date {
	const NOW = dayjs().tz(timeZone),
		at = date && typeof date === 'number'
			? (date < 0 ? NOW.subtract(Math.abs(date), DAY) : NOW.add(date, DAY))
			: dayjs(date).tz(timeZone)

	return at.endOf(unit).toDate()
}

/**
 * Get day of week (1 = Monday, 7 = Sunday)
 */
export function dayOfWeek(date: Date | dayjs.Dayjs | { year: number, month: number, day: number }, timeZone: string = TIMEZONE): number {
	const dow = date instanceof Date || dayjs.isDayjs(date)
		? dayjs(date as any).tz(timeZone).day()
		: dayjs(`${date.year}-${date.month}-${date.day}`).day()

	return dow === 0 ? 7 : dow		// adjust, dayjs: 0 = Sunday, ours 7 = Sunday
}

export function timestamp(time: string): number {
	return Math.floor((time ? new Date(time).getTime() : Date.now()) / 1000)
}

/**
 * Convert various date strings or dates to Date
 */
export function parseTime(time: Date | string): Date {
	if (time instanceof Date) return time

	const isDate = Date.parse(time)
	return isNaN(isDate) ? dayjs(time).toDate() : new Date(isDate)
}

/**
 * ceil time by duration
 * @param {Date} time
 * @param {String} from opening time, format: HH:mm
 * @param {Number} duration in minutes
 * @returns {Date}
 * @example
 * 	ceil(new Date('2021-01-01 10:00'), '10:00', 30) // 2021-01-01 10:00
 * 	ceil(new Date('2021-01-01 10:29'), '10:00', 30) // 2021-01-01 10:30
 * 	ceil(new Date('2021-01-01 10:30'), '10:00', 30) // 2021-01-01 10:30
 * 	ceil(new Date('2021-01-01 10:31'), '10:00', 30) // 2021-01-01 11:00
 */
export function ceil(time: Date, from: string, duration: number): Date {
	const d = dayjs(time),
		start = dayjs(from, 'HH:mm'),
		minutes = d.diff(start, 'minute'),
		ceil = Math.ceil(minutes / duration) * duration,
		ceilDate = start.add(ceil, 'minute')

	return ceilDate.toDate()
}

/**
 * floor time by duration
 * @param {Date} time
 * @param {String} from opening time, format: HH:mm
 * @param {Number} duration in minutes
 * @returns {Date}
 * @example
 * 	floor(new Date('2021-01-01 10:29'), '10:00', 30) // 2021-01-01 10:00
 * 	floor(new Date('2021-01-01 10:00'), '10:00', 30) // 2021-01-01 10:00
 * 	floor(new Date('2021-01-01 10:30'), '10:00', 30) // 2021-01-01 10:30
 * 	floor(new Date('2021-01-01 10:31'), '10:00', 30) // 2021-01-01 10:30
 */
export function floor(time: Date, from: string, duration: number): Date {
	const d = dayjs(time),
		start = dayjs(from, 'HH:mm'),
		minutes = d.diff(start, 'minute'),
		floor = Math.floor(minutes / duration) * duration,
		floorDate = start.add(floor, 'minute')

	return floorDate.toDate()
}

/**
 * Returns list of day-of-week between the given dates
 * @param  {Date} d1
 * @param  {Date} d2
 * @return {Array}    day-of-week  (Sunday = 0, Saturday = 6)
 */
// export function daysOfWeek(d1, d2) {
// 	const week = [0, 1, 2, 3, 4, 5, 6]
// 	const numDays = Dates.day.diff(d1, d2) + 1

// 	if (numDays >= 7) return week

// 	const day1 = d1.getDay()
// 	const day2 = d2.getDay()
// 	if (day1 <= day2) return week.splice(day1, numDays)

// 	const exclude = week.splice(day2 + 1, 7 - numDays)
// 	return week.filter(x => exclude.indexOf(x) < 0)
// }

/**
 * Get start time by rule & active period
 * @param [rule]
 * 		{string} base - 'membership.startTime' - depend on reference, default is based on current time
 * 		{string} duration - 'PnYnMnDTnHnMnS' - https://momentjs.com/docs/#/durations/as-iso-string/
 *		{string} startOf  - 'year,month,day...' - https://momentjs.com/docs/#/manipulating/start-of/
 *		{string} endOf - 'year,month,day...' - https://momentjs.com/docs/#/manipulating/end-of/
 * @param activePeriod
 * @param [reference] - reference data for picking base time - {membership}
 */
export function getStartTime(rule: Rule = {}, activePeriod: Period, reference?: object, timeZone?: string): Date | void {
	const { startTime, endTime } = activePeriod || {}

	if (!rule) {
		return startTime ? new Date(startTime) : undefined
	}

	const start = getDate({ ...rule }, reference, timeZone)

	if (!startTime) return start

	const activeStart = new Date(startTime),
		activeEnd = endTime && new Date(endTime)

	return (start < activeStart) ? activeStart : (activeEnd && start >= activeEnd ? undefined : start)
}

/**
 * Get end time by rule & active period
 * @param [rule]
 * 		{string} base - 'membership.startTime' - depend on reference, default is based on current time
 * 		{string} duration - 'PnYnMnDTnHnMnS' - https://momentjs.com/docs/#/durations/as-iso-string/
 * 		{string} startOf - 'year,month,day...' - https://momentjs.com/docs/#/manipulating/start-of/
 * 		{string} endOf - 'year,month,day...' - https://momentjs.com/docs/#/manipulating/end-of/
 * @param activePeriod
 * @param [reference] - reference data for picking base time - {membership}
 */
export function getEndTime(rule: Rule = {}, activePeriod: Period, reference?: object, timeZone?: string): Date | void {
	const { endTime } = activePeriod || {}

	if (!rule) {
		return endTime ? new Date(endTime) : undefined
	}

	const end = getDate({ ...rule }, reference, timeZone)

	if (!endTime) return end

	const activeEnd = new Date(endTime)
	return (end > activeEnd) ? activeEnd : end
}

/**
 * Get start & end (Dates, both inclusive), rounded to minutes  (used by booking)
 */
export function getStartEndTime(from: Date, duration: number, timeZone: string = TIMEZONE, unit: dayjs.OpUnitType = MINUTE) {
	const start = dayjs(from).tz(timeZone).startOf(unit),
		end = start
			.add(duration, MINUTE)
			.subtract(1, SECOND)
			.startOf(unit)
			.toDate()

	return { start: start.toDate(), end }
}

/**
 * Number of years since the specified date
 */
export function yearsSince(date: Date): number {
	return dayjs().diff(date, YEAR)
}

/**
 * Number of days since the specified date (inclusive)
 */
export function daysSince(date: Date): number {
	return dayjs().diff(date, DAY) + 1
}

/**
 * Number of days to the NEXT anniversary of the specified date (inclusive), eg. 0 = today, 1 = tomorrow
 */
export function daysToAnniversary(date: Date): number {
	const start = dayjs(date),
		nextAnniversary = start.add(yearsSince(date) + 1, YEAR)

	return nextAnniversary.diff(dayjs(), DAY) + 1
}

/**
 * Number of days from the LAST anniversary of the specified date (inclusive)
 */
export function daysSinceAnniversary(date: Date): number {
	const start = dayjs(date),
		lastAnniversary = start.add(yearsSince(date), YEAR)

	return dayjs().diff(lastAnniversary, DAY) + 1
}

/**
 * Get Date
 * @param [rule]
 * 			{string} base - 'membership.startTime' - depend on reference, default is based on current time
 * 			{string} duration - 'PnYnMnDTnHnMnS' - https://momentjs.com/docs/#/durations/as-iso-string/
 * 			{string} startOf - 'year,month,day...' - https://momentjs.com/docs/#/manipulating/start-of/
 * 			{string} endOf - 'year,month,day...' - https://momentjs.com/docs/#/manipulating/end-of/
 * @param [reference] - reference data for picking base time - {membership}
 * @param [timeZone]
 */
export function getDate(rule: Rule = {}, reference?: object, timeZone: string = TIMEZONE): Date | void {
	let base

	rule.base = rule.base || NOW

	if (rule.base === NOW) {
		base = new Date()
	}
	else if (isNaN(Date.parse(rule.base))) {
		base = reference ? pickDeep(reference, rule.base) : null
	}
	else {
		base = rule.base
	}

	if (!base) return

	rule.base && delete rule.base

	let time = dayjs(base).tz(timeZone)

	for (const [ key, value ] of Object.entries(rule)) {
		if (key === 'duration') {
			time = time.add(dayjs.duration(value))
		}
		else if (typeof (<any>time)[key] === 'function') {
			time = (<any>time)[key](value)
		}
	}

	return new Date(time.toISOString())
}

/**
 * replace isValid() for app
 * check the given date is valid date
*/
export function isValidDate(date: any): boolean {
	if(!date) return false

	const dateObject = new Date(date)
	return dateObject instanceof Date && !Number.isNaN(dateObject.getTime())
}

export function isDateObject(date: any): boolean {
	return date instanceof Date && !Number.isNaN(date.getTime())
}

export function isDateDetail(date: any): date is DateDetail {
	return !!date && typeof date === 'object' && 'day' in date	// typeof null is object
}

/**
 * replace getDate() for app
 * given any valid date value, convert it to a Date format
*/
export function toDate(date?: string | number | Date | DateDetail, serverDate: Date = new Date()): Date {
	if (date instanceof Date && isDateObject(date)) return date

	let nDate = serverDate ? new Date(serverDate.getTime()) : new Date()

	if (isDateDetail(date)) {
		const { year = nDate.getFullYear(), month = nDate.getMonth() + 1, day = 1 } = date

		// Reset time part to the start of the day
		nDate.setHours(0, 0, 0, 0)
		// Set year, month, and day based on the provided date details or defaults
		nDate.setFullYear(year, month - 1, day)

	}
	else if (date && isValidDate(date)) {
		nDate = new Date(date)
	}

	return nDate
}

/**
 * replace getDateDetail() for app
 * convert date format to dateDetail
 */
export function toDateDetail(date?: string | number | Date): DateDetail | undefined {
	if (!date || !isValidDate(date)) return undefined

	const dateObject = new Date(date)
	return {
		year: dateObject.getFullYear(),
		month: dateObject.getMonth() + 1,
		day: dateObject.getDate(),
	}
}

/**
 * return day of week, Monday - Sunday: 1 - 7
*/
export function getDay(date?: string | number | Date | DateDetail, serverDate: Date = new Date()): number {
	const nDate = toDate(date, serverDate)
	const day = new Date(nDate).getDay()

	return day || 7
}

/**
 * replace dateByDay()
 * calculate the next date for a specified weekday from a given date
 * targetWeekday: 1 - 7
*/
export function getNextDateByWeekday(targetWeekday: number, date: string | number | Date = new Date()): Date {
	const d = dayjs(date)
	const weekDay = d.day() || 7
	let difference = targetWeekday - weekDay

	if (difference <= 0) {
		difference += 7
	}

	return d.add(difference, 'days').toDate()
}

/**
 * get ISO 8601 date, YYYY-MM-DD
 */
export function getISODate(date?: string | number | Date | DateDetail): string {
	const d = toDate(date)
	const year = d.getFullYear()
	const month = d.getMonth() + 1
	const day = d.getDate()

	const formattedMonth = month < 10 ? `0${month}` : `${month}`
	const formattedDay = day < 10 ? `0${day}` : `${day}`

	return `${year}-${formattedMonth}-${formattedDay}`
}

export function lastDayOfMonth(date?: string | number | Date | DateDetail, serverDate: Date = new Date()): number {
	const d = toDate(date || serverDate)
	const year = d.getFullYear()
	const month = d.getMonth() + 1

	return new Date(year, month, 0).getDate()
}

/**
 * Check if time is between start & end (non-inclusive)
 * @param {string} time - Time in HH:mm format
 * @param {string} start - Start time HH:mm
 * @param {string} end - End time HH:mm
 * @returns {boolean}
 */
export function timeIsBetween(time: string, start: string, end: string): boolean {
	// Convert to minutes since midnight for easier comparison
	const toMinutes = (t: string): number => {
		const [ h, m ] = t.split(':').map(Number)
		return h * 60 + m
	}

	const t = toMinutes(time)
	const s = toMinutes(start)
	const e = toMinutes(end)

	return (e > s)
		? (t > s && t < e)  // Normal range
		: !(t > e && t < s) // Overnight range
}

export default {
	sameDay,
	isSameDay,
	diffDays,
	ceil,
	floor,
	parseTime,
	timestamp,
	startOf,
	endOf,
	dayOfWeek,
	yearsSince,
	daysSince,
	daysToAnniversary,
	daysSinceAnniversary,
	getStartTime,
	getEndTime,
	getStartEndTime,
	getDate,

	isValidDate,
	isDateObject,
	isDateDetail,
	toDate,
	toDateDetail,
	getDay,
	getNextDateByWeekday,
	getISODate,
	lastDayOfMonth,
	timeIsBetween
}
