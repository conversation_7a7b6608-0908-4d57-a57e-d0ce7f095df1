
/**
 * Use regex to retrieve Card number from barcode
 * @param barcode
 * @param patterns e.g. [ "/(?!CARD\\|2[0-1]\\|)\\d{8,10}/g" ]
 * @return card number
 */
export function cardNumberFromBarcode(barcode: string, patterns: string[]): string {
	let cardNumber = barcode

	for (let i = 0; i < patterns.length; i += 1) {
		const regex = getRegex(patterns[i]),
			res = barcode.match(regex)

		if (res && res[0]) {
			[cardNumber] = res
			break
		}
	}

	return cardNumber
}

/**
 * Use regex to validate Card number format
 * @param cardNumber
 * @param patterns e.g. [ "/\\d{8,10}/g" ]
 * @return valid
 */
export function isValidCardNumber(cardNumber: string, patterns: string[]) {
	let valid = false

	for (let i = 0; i < patterns.length; i += 1) {
		const regex = getRegex(patterns[i]),
			res = cardNumber.match(regex)

		if (res?.[0]) {
			valid = true
			break
		}
	}

	return valid
}

/**
 * Remove spaces & symbols in cardNumber
 */
export function cleanseCardNumber(cardNumber: string): string {
	return typeof cardNumber === 'string'
		? cardNumber.replace(/[^\w\d]/gi, '')
		: cardNumber
}

// -----  Private Functions  -----

function getRegex(str: string) {
	const lastSlashIndex = str.lastIndexOf('/')

	// handle regex with flag
	if (str[0] === '/' && lastSlashIndex !== -1 && lastSlashIndex !== 0) {
		const flag = str.substring(lastSlashIndex + 1, str.length),
			regexStr = str.substring(1, lastSlashIndex)

		return flag ? new RegExp(regexStr, flag) : new RegExp(regexStr)
	}

	return new RegExp(str)
}

export default {
	cardNumberFromBarcode,
	isValidCardNumber,
	cleanseCardNumber,
}
