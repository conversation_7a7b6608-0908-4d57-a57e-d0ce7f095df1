import locale from 'locale'

const CHINESE_MAP: { [code: string]: string } = {
	'zh-HK': 'zh-Hant-HK',
	'zh-TW': 'zh-<PERSON><PERSON>',
	'zh-CN': 'zh-<PERSON>',
	'yue-<PERSON>': 'zh-<PERSON>',
	'yue-<PERSON><PERSON>': 'zh-Hant-HK',
}

/**
 * Return the best match language
 * @param preferred languages ['zh', 'en'...]
 * @param supported ['zh-Hant', 'en']
 * @param fallback default language, 'zh-Hans'
 */
export function bestLanguage(preferred: string[] = [], supported: string[], fallback?: string): string | null {
	const all = preferred.reduce((res: any, language: string) => {
			const language2 = language.replace(/-[A-Z]{2}$/, '')

			res.push(language)
			res.push(language2)
			CHINESE_MAP[language] && res.push(CHINESE_MAP[language])
			CHINESE_MAP[language2] && res.push(CHINESE_MAP[language2])
			return res
		}, []),
		found = all.find((language: string) => supported.includes(language))

	if (found) return found

	const candidates = new locale.Locales(supported, fallback)

	return new locale.Locales(all).best(candidates).toString()
}

/**
 * Return the best match content
 * @param preferred languages
 * @param t - globalize.t
 * @param fallback default language
 */
export function localizeContent(preferred: string[], t: any, fallback: string) {
	const supportedLanguages = Object.keys(t),
		[ first ] = supportedLanguages,
		lng = bestLanguage(preferred, supportedLanguages, fallback || first)

	return lng && t[lng] || t[first]
}

/**
 * Maps language code from app (locale in install header) to code used by globalize
 */
export function languageOf(locale: any = {}) {
	const { languages = [] } = locale,
		[ first = '' ] = languages,
		[ lang, dialect, country ] = first.split('-')

	if ((country === 'HK' || country === 'MO') && dialect === 'Hant') {
		return 'zh-Hant-HK'
	}

	return dialect ? `${lang}-${dialect}` : lang
}

export function mergeGlobalize(obj: any, language: string): any {
	if (obj.globalize) {
		const { globalize, ...data } = obj, g = globalize, { t = {} } = g
		return Object.assign({}, data, t[g.default], t[language])
	}
	return obj
}

export default {
	bestLanguage,
	localizeContent,
	languageOf,
	mergeGlobalize
}
