import franc from 'franc'

export { default as camelcase } from 'camelcase'

type Names = {
	familyName?: string
	givenName?: string
	alias?: string
}

export enum NAME_ORDER {
	fg = 'familygiven',
	gf = 'givenfamily',
	g = 'given',
	f = 'family',
	a = 'alias',
	af = 'aliasfamily',
	fga = 'familygivenalias',
	afg = 'aliasfamilygiven',
	gfa = 'givenfamilyalias',
}

const CHINESE = 'cmn',
	JAPANESE = 'jpn',
	KOREAN = 'kor',
	// NON_ALPHABETIC = [CHINESE, JAPANESE, KOREAN],
	SUPPORTED = [ 'eng', 'cmn', 'ind', 'jpn', 'kor', 'mal', 'ben', 'hin', 'tam', 'vie', 'tha', 'mya', 'spa', 'rus', 'por', 'fra', 'deu', 'ita', 'tur', 'pol', 'nld', 'swe', 'fin', 'dan', 'nob', 'arb', 'heb' ],
	NAME = {
		fg: 'familygiven',
		gf: 'givenfamily',
		g: 'given',
		f: 'family',
		a: 'alias',
		af: 'aliasfamily',
		fga: 'familygivenalias',
		afg: 'aliasfamilygiven',
		gfa: 'givenfamilyalias',
	},
	INVALID_NAME_PATTERN = /(^[\s\d!"#$%&'()*+,-./:;<=>?@[\\。，？！\]^_`{|}~]+)|([\s\d!"#$%&'()*+,-./:;<=>?@[\\。，？！\]^_`{|}~]+$)/g // punctuations, numbers and spaces (at the beginning or end)

export function unvowel (name: string) {
	return name.replace(/[aeiou\s]/gi, '')
}

function capitalize (string: string) { // refer to npm package: capitalize
	return string.replace(/(^|[^a-zA-Z\u00C0-\u017F])([a-zA-Z\u00C0-\u017F])/g, m => m.toUpperCase())
}

export function formatName (name: string = '') {
	return capitalize(name.toLowerCase())
}

export function splitIntoWords (text: string) {
	const regex = /\b[^\s]+\b(?:\.)?/g,
		matches = []

	let result
	while ((result = regex.exec(text)) !== null) {
		matches.push(result[0].toLowerCase())
	}
	return matches
}

export function getName (profile: Names = {}, nameOrder: string | null = '', separator = ' ') {
	const { fg, gf, g, f, a, af, fga, afg, gfa } = NAME,
		{ familyName = '', givenName = '', alias = '' } = profile,
		format = (first: string, last: string, sep: string) => first && last ? `${first}${sep}${last}` : first ?? last

	if (!nameOrder) return ''
	if (nameOrder === g) return givenName
	if (nameOrder === f) return familyName
	if (nameOrder === a) return alias

	if (nameOrder.includes(fg)) {
		const familyGiven = format(familyName, givenName, separator)

		if (nameOrder === fga) return format(familyGiven, alias, separator)
		if (nameOrder === afg) return format(alias, familyGiven, separator)

		return familyGiven
	}

	if (nameOrder.includes(gf)) {
		const givenFamily = format(givenName, familyName, separator)

		return (nameOrder === gfa)
			? format(givenFamily, alias, separator)
			: givenFamily
	}

	if (nameOrder === af) return format(alias, familyName, separator)

	return ''
}

export function detectLanguage (whitelist: string[] | null, str: string = '') {
	return franc(str, {
		minLength: Math.min(str.length, 20),
		whitelist: whitelist ?? SUPPORTED,
	})
}

export function nameConnector (language: string | null) {
	let connector = ' '
	switch (language) {
	case CHINESE:
	case JAPANESE:
	case KOREAN:
		connector = ''
	}
	return connector
}

export function deriveNameOrder(displayAs: string) {
	const { fg, gf } = NAME,
		map: Record<string, string> = {
			familygiven: fg,
			familygivenalias: fg,
			aliasfamilygiven: fg,
			givenfamilyalias: gf,
			givenfamily: gf,
		}

	return map[displayAs] || null
}

export function validateName (name?: string) {
	return name ? !INVALID_NAME_PATTERN.test(name) : ''
}

export function cleanseName (name?: string) {
	return name ? name.replaceAll(INVALID_NAME_PATTERN, '') : ''
}

export default {
	unvowel,
	formatName,
	splitIntoWords,
	getName,
	detectLanguage,
	nameConnector,
	deriveNameOrder,
	validateName,
	cleanseName
}
