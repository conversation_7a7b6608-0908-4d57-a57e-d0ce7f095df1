import { pinyin } from 'pinyin-pro'
import { unvowel } from './names'
import { isChinese } from './strings'

const CODE_LENGTH = 12		// tenant code format: 12 char + 2 random numbers + '-' + country_code (2 char)

export function generateTenantCode(businessName: string, countryCode: string = ''): string {
	const name = isChinese(businessName)
			? pinyin(businessName, { toneType: 'none' })
			: businessName,
		trimmed = name.split(' ').join('')	// remove spaces
			.toLowerCase(),
		short = trimmed.length > CODE_LENGTH ? unvowel(trimmed) : trimmed,
		num = Math.random().toString().slice(-3)

	return `${short}${num}-${countryCode.toLowerCase()}`
}

export default {
	generateTenantCode
}
