/**
 *  @module FormHelper
 */

const { emailAddressType } = appRequire('lib/common/helper'),
	PERMISSION = {
		DONOTDISTURB: -2,
		OPTOUT: -1,
		UNKNOWN: 0,
		OPTIN: 1,
	},
	TAGS = ['singaporean', 'christian', 'baptised', 'interested'],
	ALLERGY = ['eczema', 'foodAllergy', 'nasalSensitivity', 'eyeAllergy', 'skinAllergy', 'asthma', 'hives', 'latexAllergy', 'drugAllergy', 'otherAllergy'];

const transform = {
	person: toPerson,
	card: toCard,
};

exports.toModel = function(formData, type) {
	return transform[`${type}`](formData);
};

exports.toProfile = toProfile;

/**
 * convert profile to person
 * @param {Object} data
 * 				{String} familyName
 * 				{String} givenName
 * 				{String} nameOrder
 * 				{String} gender
 * 				{String} locale
 * 						{String[]} languages - ['en', 'zh-Hans', 'zh-Hant'...]
 * 						{String} country - two-letter country codes, https://en.wikipedia.org/wiki/ISO_3166-1
 * 						{String} timeZone - Asia/Singapore, https://gist.github.com/diogocapela/12c6617fc87607d11fd62d2a4f42b02a
 * 				{String} birth
 * 				{String} mobile
 * 				{Boolean} mobileOptIn
 * 				{String} email
 * 				{Boolean} emailOptIn
 * 				{String} address
 * 				{Boolean} postalOptIn
 * 				{String} postCode
 * 				{String} country
 * 				{String} policy
 * 				{String} userId
 * @return {Object} person
*/
function toPerson(data) {
	const person = {
		familyName: data.familyName === undefined ? undefined : _trim(data.familyName),
		givenName: data.givenName === undefined ? undefined : _trim(data.givenName),
		name: _name(data.nameOrder),
		gender: data.gender,
		locale: _locale(data),
		dateList: _dateList(data.birth || {}),
		phoneList: _phoneList(data),
		emailList: _emailList(data),
		addressList: _addressList(data),
		permissionList: _permissionList(data), // force permission
		identityList: _identityList(data),
		_tags: _tags(data),
	};
	return Object.keys(person).reduce((res, key) => {
		if (person[`${key}`] !== undefined) res[`${key}`] = person[`${key}`];
		return res;
	}, {});

	function _name(nameOrder) {
		return nameOrder ? { order: nameOrder } : undefined;
	}

	function _dateList({ year, month, day, name = 'birth' } = {}) {
		return (year && month && day) ? [{ year, month, day, name }] : undefined;
	}

	function _phoneList({ mobile, mobileOptIn } = {}) {
		if (mobile) {
			return [{
				fullNumber: mobile.replace(/[^0-9]/g, ''),
				optIn: mobileOptIn,
				type: 'mobile',
			}];
		}
		return undefined;
	}

	function _emailList({ email, emailOptIn } = {}) {
		if (email) {
			email = email.trim();
			return [{
				address: email,
				optIn: emailOptIn,
				type: emailAddressType(email),
			}];
		}
		return undefined;
	}

	function _addressList({ address, postalOptIn, postCode, country }) {
		if (address || postCode || country) {
			return [{
				address: address || null,
				formatted: address || null,
				postCode: postCode || null,
				optIn: postalOptIn,
				country: country || null,
				type: 'home',
			}];
		}
		return undefined;
	}

	function _permissionList({ mobileOptIn, emailOptIn, postalOptIn, policy }) {
		const R = [];
		(mobileOptIn !== undefined) && R.push(_permission('mobile', mobileOptIn));
		(emailOptIn !== undefined) && R.push(_permission('email', emailOptIn));
		(postalOptIn !== undefined) && R.push(_permission('postal', postalOptIn));
		(policy !== undefined) && R.push(_permission('serviceTerms', policy));
		(policy !== undefined) && R.push(_permission('privacyPolicy', policy));

		return R.length ? R : undefined;

		function _permission(channel, optIn) {
			const status = (optIn === true) ? PERMISSION.OPTIN : (optIn === false ? PERMISSION.OPTOUT : PERMISSION.UNKNOWN);
			return { channel, status };
		}
	}

	function _identityList({ userId }) {
		if (userId) {
			return [{
				provider: 'perkd',
				externalId: userId,
				type: 'user',
			}];
		}
		return undefined;
	}

	function _locale({ locale }) {
		if (locale) {
			return {
				languages: locale.languages.map(lang => lang.toLowerCase() === 'zh-hant_tw' ? 'zh-Hant' : lang),
				country: locale.country,
				timeZone: locale.timeZone,
			};
		}

		return undefined;
	}

	function _tags(data = {}) {
		const userAdd = { method: 'addTag', list: [], kind: 'user' },
			userRemove = { method: 'removeTag', list: [], kind: 'user' }

		TAGS.forEach(tag => {
			const nonTag = `non-${tag}`;
			switch (data[`${tag}`]) {
			case true:
				userAdd.list.push(tag);
				userRemove.list.push(nonTag);
				break;
			case false:
				userAdd.list.push(nonTag);
				userRemove.list.push(tag);
				break;
			case null:
				userRemove.list.push(tag);
				userRemove.list.push(nonTag);
			}
		});
		if (Array.isArray(data.allergy)) { // compat for allergyhk formData, allergy details
			ALLERGY.forEach(alg => {
				data.allergy.includes(alg) ? userAdd.list.push(alg) : userRemove.list.push(alg);
			});
		}
		return [userAdd, userRemove];
	}
}

/**
 * convert data to card (a3)
 * @param {Object} data
 *				{String} cardNumber
 *				{String} barcode
 *				{String} display
 *				{String} startTime
 *				{String} endTime
 * @return {Object} card
 * 					{Object} custom
 * 							{String} name
 * 					{String} number
 * 					{String} barcode
 * 					{Number} barcodeType
 * 					{String} startTime
 * 					{String} endTime
*/
function toCard(data) {
	const cardNumber = data.cardNumber || {},
		barcode = cardNumber.barcode || {},
		card = {
			custom: {
				name: _trim(data.cardName),
			},
			number: _trim(cardNumber && cardNumber.display || ''),
			barcode: barcode && barcode.value || '',
			barcodeType: barcode && barcode.type || 0,
			displayName: _trim(data.display),
			startTime: data.startTime || null,
			endTime: data.endTime || null,
		};
	return card;
}

function toProfile(person) {
	const mobile = _mobile(person),
		email = _email(person),
		profile = {
			nameOrder: person.name && person.name.order,
			familyName: _trim(person.familyName),
			givenName: _trim(person.givenName),
			gender: _gender(person.gender),
			birth: _birth(person),
			mobile: mobile ? mobile.fullNumber : null,
			mobileOptIn: _permission(person, 'mobile'),
			email: email ? email.address : null,
			emailOptIn: _permission(person, 'email'),
		};
	return profile;

	function _gender(gender) {
		return (gender === 'f' || gender === 'm') && gender || null;
	}

	function _birth({ dateList = [] } = {}) {
		const birth = dateList.find(d => d.name === 'birth');
		if (birth) return _.pick(birth, ['year', 'month', 'day']);
		return null;
	}

	function _mobile({ phoneList = [] } = {}) {
		return phoneList.find(p => p.type === 'mobile');
	}

	function _email({ emailList = [] } = {}) {
		return emailList[0];
	}

	function _permission({ permissionList = [] } = {}, channel) {
		const permission = permissionList.find(permission => permission.channel === channel);
		if (permission) { return permission.status === 0 ? null : permission.status === 1; }

		return undefined;
	}
}

function _trim(str) {
	return (str || '').trim();
}

/**
 * End of script
*/
