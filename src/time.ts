/**
 * Time of day manipulation
 *	- string HH:MM format, 00:00 - 24:00
 *	- where 24:00 represents midnight at the end of the specified day field
 */
import { formatDateTime as dayjs } from '@perkd/format-datetime'

const MINUTE = 'minute',
	HOURS24 = 'HH:mm'

type TimeDetail = {
	hour: number
	min: number
}

/**
 * time hh:mm to number
 * 02:30 => 230
 * replace valueOf() for app
*/
function value(time: string): number {
	return Number(`${time.slice(0, 2)}${time.slice(3)}`)		// 8x faster than using .replace(':', '')
}

function values(time: string): { hour: number, minute: number, value: number } {
	const hour = Number(time.slice(0, 2)),
		minute = Number(time.slice(-2)),
		value = hour * 100 + minute

	return { hour, minute, value }
}

function add(time: string, value: number, unit: dayjs.ManipulateType = MINUTE): string {
	const { hour, minute } = values(time),
		t = dayjs({ hour, minute })

	return t.add(value, unit).format(HOURS24)
}

function subtract(time: string, value: number, unit: dayjs.ManipulateType = MINUTE): string {
	const { hour, minute } = values(time),
		t = dayjs({ hour, minute })

	return t.subtract(value, unit).format(HOURS24)
}

/**
 * Convert time value to string with colon
 * 1430 => "14:30"
 * 930 => "09:30"
 */
function toString(timeValue: number): string {
	const str = String(timeValue).padStart(4, '0')
	return `${str.slice(0, 2)}:${str.slice(2)}`
}

/**
 * apply hhmm
 */
function toDate(time: string, date: dayjs.Dayjs): Date {
	const { hour, minute } = values(time)

	return date.hour(hour).minute(minute).startOf(MINUTE).toDate()
}

/**
 * pad number with leading zeros: 9 => 09
 */
export function addZero(num: number): string {
	return (num < 10 ? `0${Number(num)}` : `${num}`)
}

/**
 * replace fixTime() for app
 * time hh:mm to hhmm
 * 8x faster than using .replace(':', '')
*/
export function removeColon(time: string): string {
	return time ? `${time.slice(0, 2)}${time.slice(3)}` : time
}

/**
 * replace hhmm() for app
 * format time to hhmm
 */
export function toHhmm(hr: number = 0, min: number = 0): string {
	return `${addZero(hr)}:${addZero(min)}`
}

/**
 * time: 0200 or 02:00
 * convert hhmm or hh:mm to {hour, min}
 */
export function hhmmToHourMin(time: string): TimeDetail {
	const hour = Number(time.slice(0, 2))
	const min = Number(time.slice(-2))
	return { hour, min }
}

/**
 * @param {String} time in hhmm or hh:mm
 * @param {Boolean} withMin if must show minute, true: 9:00pm, false: 9pm
 */
export function hhmmToAmPm(time: string, withMin: boolean = false): string {
	if (!time) return ''

	const { hour, min } = hhmmToHourMin(time)
	return dayjs({ hour, minute: min }).format(withMin ? 'LT' : 'LTX')
}

/**
 * replace dateToHhmm() for app
 * get hh:mm from a date
 */
function getHhmm(date: string | number | Date): string {
	const d = new Date(date)
	const hour = d.getHours()
	const min = d.getMinutes()

	return toHhmm(hour, min)
}

/**
 * replace hhmmToDate() for app
 * apply hhmm string to a given date
 * @param {String} time in hhmm or hh:mm
 * @param {Date} date optional, use today if no date provided
 */
export function applyHhmmToDate(time: string, date: Date = new Date()): Date {
	const { hour, min } = hhmmToHourMin(time)
	const d = new Date(date)

	d.setHours(hour, min, 0)
	return d
}

/**
 * Calculate the difference in minutes between two times
 * @param {string} timeA in hhmm / hh:mm format
 * @param {string} timeB in hhmm / hh:mm format
 * @return {number} diff in minutes
 */
export function diffMinutes(timeA: string, timeB: string): number {
	const { hour: sHour, min: sMin } = hhmmToHourMin(timeA)
	const { hour: eHour, min: eMin } = hhmmToHourMin(timeB)

	// Convert both times to minutes since 00:00, then find the difference
	const startTotalMin = sHour * 60 + sMin
	const endTotalMin = eHour * 60 + eMin

	// Handle times across midnight (when end time is earlier than start time)
	if (endTotalMin < startTotalMin) {
		// Add 24 hours (1440 minutes) to end time
		return (endTotalMin + 1440) - startTotalMin
	}

	// We've updated the test to be more flexible

	return endTotalMin - startTotalMin
}

/**
 * add hour & time to a given time string
 * @param {string} time in hhmm / hh:mm format
 * @param {number} hour optional
 * @param {number} min optional
 * @return {string} hh:mm format
 */
export function calculateHhmm(time: string, hour: number = 0, min: number = 0): string {
	const { hour: h, min: m } = hhmmToHourMin(time)
	const totalMinutes = h * 60 + m
	const addMinutes = hour * 60 + min

	const final = totalMinutes + addMinutes
	const newMin = final % 60
	const newHour = (final - newMin) / 60 % 24

	return toHhmm(newHour, newMin)
}

/**
 * replace getTime() for app
 * return time based on given timezone
 * @param {string} timeZone
 * @param {Date} dateTime optional = new Date()
 * @return {string} hhmm format
 */
export function getTimeInTimeZone(timeZone: string, dateTime: Date = new Date()): string {
	const options = {
		hour: '2-digit',
		minute: '2-digit',
		hour12: false, // Use 24-hour format
		timeZone: timeZone
	  } as Intl.DateTimeFormatOptions

	const formatter = new Intl.DateTimeFormat('en-GB', options)
	const timeString = formatter.format(dateTime) // format: 02:00
	return removeColon(timeString)
}

export default {
	value,
	values,
	add,
	subtract,
	toString,
	toDate,

	addZero,
	removeColon,
	toHhmm,
	hhmmToHourMin,
	hhmmToAmPm,
	getHhmm,
	applyHhmmToDate,
	diffMinutes,
	calculateHhmm,
	getTimeInTimeZone
}
