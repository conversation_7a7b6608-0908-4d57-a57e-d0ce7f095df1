
type TIMESTAMPS = { [marker: string]: bigint }
type DIFF = { [marker: string]: number }

let log: TIMESTAMPS = {},		// stores history of all markers, identical named markers will override previous value
	maximum: DIFF = {},
	minimum: DIFF = {}

function mark(marker?: string): bigint {
	const ts = process.hrtime.bigint()		// current high-resolution real time in a [seconds, nanoseconds]
	if (marker) log[marker] = ts
	return ts
}

function get(marker: string): bigint {
	return log[marker]
}

function elapsedTime (start: string, end: string, verbose: boolean): number | undefined {
	if (typeof end === 'boolean' || (end === undefined && verbose === undefined)) {
		verbose = (typeof end === 'boolean') ? end : verbose
		end = start + '.end'
		start += '.start'
	}

	if (log[start] && log[end]) {
		const elapsed = diff(log[start], log[end])

		maximum[start] = maximum[start] ? Math.max(maximum[start], elapsed) : elapsed
		minimum[start] = minimum[start] ? Math.min(minimum[start], elapsed) : elapsed

		if (verbose) {
			console.log('>>> Benchmark: ' + start + '->' + end + ':  \t' + elapsed + ' ms')
		}
		return elapsed
	}
	return -1
}

function diff(start: bigint, end?: bigint): number {
	end = end || process.hrtime.bigint()
	return Math.floor((Number(end - start) / 1000000) * 1000) / 1000	// return in milliseconds
}

function time(date: Date) {
	date = date || new Date()
	return date.toTimeString().substring(0, 8)
}

function now() {
	return Date.now()
}

function max(marker: string): number {
	return maximum[marker]
}

function min(marker: string): number {
	return minimum[marker]
}

function reset() {
	log = {}
	maximum = {}
	minimum = {}
}

export const bm = {
	mark, get, elapsedTime, diff, time, now, max, min, reset
}
