/**
 * Opening Hours & Time of day related
 */
import { formatDateTime as dayjs } from '@perkd/format-datetime'
import { dayOfWeek, TIMEZONE } from './dates'
import { cloneDeep } from './objects'
import Time from './time'

type Time = {
	time: string		// 24hr ISO 8601 basic format (hh:mm), 00:00-24:00, where 24:00 represents midnight at the end of the specified day field
}

type DayTime = Time & {
	day: number			// day of week, 1-7, 7 = Sunday
}

type Period = {
	open: DayTime
	close: DayTime
	busy?: DayTime
}

type TimePeriod = {
	open: Time
	close: Time
	busy?: Time
}

type YearMonthDay = {
	year: number		// with century, 0 if specifying a date without a year
	month: number		// of year, 1 = January, 0 if specifying a year without a month and day
	day: number			// of month, 1-31 & valid for year and month, 0 if specifying a year by itself or a year & month where the day is not significant
}

type Hours = {
	specific?: {
		date: YearMonthDay
		periods: TimePeriod[]
	}[]
	periods: Period[]
}

type Schedule = {
	date: string | number | Date,
	open: {
		start: string,	// 24hr ISO 8601 basic format (hh:mm)
		end: string 	// 24hr ISO 8601 basic format (hh:mm)
	}[]
}

export type OpeningHours = Hours

export const
	DAY = 'day',
	MINUTE = 'minute',
	HOUR_0 = '00:00',
	HOUR_24 = '24:00',
	HOURS_247 = {
		specific: [],
		periods: [
			{ open: { day: 1, time: HOUR_0 }, close: { day: 1, time: HOUR_24 } },
			{ open: { day: 2, time: HOUR_0 }, close: { day: 2, time: HOUR_24 } },
			{ open: { day: 3, time: HOUR_0 }, close: { day: 3, time: HOUR_24 } },
			{ open: { day: 4, time: HOUR_0 }, close: { day: 4, time: HOUR_24 } },
			{ open: { day: 5, time: HOUR_0 }, close: { day: 5, time: HOUR_24 } },
			{ open: { day: 6, time: HOUR_0 }, close: { day: 6, time: HOUR_24 } },
			{ open: { day: 7, time: HOUR_0 }, close: { day: 7, time: HOUR_24 } },
		]
	}

/**
 * Get hours for day
 * @param hours - opening hours definition
 * @param year - with century, 0 if specifying a date without a year
 * @param month - of year, 1 = January, 0 if specifying a year without a month and day
 * @param day - of month, 1-31 & valid for year and month, 0 if specifying a year by itself or a year & month where the day is not significant
 */
export function hoursFor(hours: Hours, year: number, month: number, day: number): Period[] {
	const { specific = [], periods = [] } = hours || {},
		dow = dayOfWeek({ year, month, day })

	for (const { date: d, periods = [] } of specific) {
		if ((d.year === year || d.year === 0)
			&& (d.month === month || d.month === 0)
			&& (d.day === day || d.day === 0)) {

			return periods.map(({ open, close, busy }) => {
				const period: Period = {
					open: { time: open.time, day: dow },
					close: { time: close.time, day: dow }
				}
				if (busy) period.busy = { time: busy.time, day: dow }

				return period
			})
		}
	}

	return periods.filter(({ open, close }) => {
		const yesterday = (dow - 1) || 7
		return open.day === dow
			|| (open.day === yesterday && isLateNight(open, close))
	})
}

export function isLateNight(open: Time, close: Time): boolean {
	return !!Time.value(close.time) && Time.value(close.time) < Time.value(open.time)
}

export function within(open: DayTime, close: DayTime, start: number, end: number, dow: number): boolean {
	const time = timeOfDay(open, close, dow),
		{ openTime, closeTime } = time

	if (!openTime && !closeTime) return false

	return openTime <= start && closeTime > end
}

/**
 * Check if Open for time period
 */
export function isOpen(hours: Hours, from: Date | dayjs.Dayjs, to?: Date | dayjs.Dayjs, timeZone: string = TIMEZONE): boolean {
	const first = dayjs(from).tz(timeZone),
		last = to ? dayjs(to).tz(timeZone) : first,
		numDays = last.startOf(DAY)
			.diff(first.startOf(DAY), DAY) + 1

	for (let d = 0; d < numDays; d++) {
		const startHr = (d === 0) ? first.hour() : 0,	// midnight after 1st day
			startMin = (d === 0) ? first.minute() : 0,
			endHr = (d === numDays - 1) ? last.hour() : 23,
			endMin = (d === numDays - 1) ? last.minute() : 59,
			startTime = startHr * 100 + startMin,
			endTime = endHr * 100 + endMin,
			start = first.add(d, DAY)
				.hour(startHr).minute(startMin)
				.startOf(MINUTE),
			periods = hoursFor(hours, start.year(), start.month() + 1, start.date()),
			day = start.day() || 7

		if (!periods.some(({ open, close }) => within(open, close, startTime, endTime, day))) {
			return false
		}
	}

	return true
}

export function nextOpen(hours: Hours, from: Date | dayjs.Dayjs = new Date(), timeZone: string = TIMEZONE): any {
	sortHours(hours.periods)	// ensure sorted ascending by open

	const date = dayjs(from).tz(timeZone),
		fromDay = date.day() || 7,
		time = value(date),
		periods = hoursFor(hours, date.year(), date.month() + 1, date.date())

	for (const { open, close } of periods) {
		if (within(open, close, time, time, fromDay)) {
			const start = date.toDate(),
				end = Time.toDate(close.time === HOUR_0 ? HOUR_24 : close.time, date)

			return { start, end }
		}
		else if (after(open, close, time, time, fromDay)) {
			const start = Time.toDate(open.time, date),
				end = Time.toDate(close.time === HOUR_0 ? HOUR_24 : close.time, date)

			return { start, end }
		}
	}

	return undefined
}

/**
 * Get opening time on date
 */
export function openFrom(hours: Hours, from: Date | dayjs.Dayjs, timeZone: string = TIMEZONE): Date {
	const start = dayjs(from).tz(timeZone),
		periods = hoursFor(hours, start.year(), start.month() + 1, start.date())

	if (!periods.length) return start.toDate()

	const dow = start.day() || 7,
		time = value(start),
		period = periods.find(({ open, close }) => within(open, close, time, time, dow)),
		d = period?.open.day || dow,
		{ open, close } = earliestOpenLatestClose(periods.filter(({ open }) => open.day === d)),
		day = isLateNight(open, close) ? d % 7 : d,
		{ hour, minute } = Time.values(open.time)

	if (day === 7) {
		return start.hour(hour).minute(minute)
			.startOf(MINUTE)
			.toDate()
	}

	return start.day(day)
		.hour(hour).minute(minute)
		.startOf(MINUTE)
		.toDate()
}

/**
 * Get closing time on date
 */
export function openUntil(hours: Hours, date: Date | dayjs.Dayjs, timeZone: string = TIMEZONE): Date {
	const start = dayjs(date).tz(timeZone),
		periods = hoursFor(hours, start.year(), start.month() + 1, start.date())

	if (!periods.length) return start.toDate()

	const dow = start.day() || 7,
		time = value(start),
		period = periods.find(({ open, close }) => within(open, close, time, time, dow)),
		d = period?.open.day || dow,
		{ open, close } = earliestOpenLatestClose(periods.filter(({ open }) => open.day === d)),
		day = (close.day + (isLateNight(open, close) ? 1 : 0)) % 7 || 7,
		{ hour, minute } = Time.values(close.time === HOUR_0 ? HOUR_24 : close.time)

	return start.day(day)
		.hour(hour).minute(minute)
		.subtract(1, MINUTE)
		.startOf(MINUTE)
		.toDate()
}

/**
 * Get startTime and endTime of Hours (when no 'specific' or has 'periods', return {})
 */
export function startEndOf(hours: Hours, timeZone: string = TIMEZONE) {
	const { specific = [], periods = [] } = hours || {}

	if (!specific.length || periods.length) return {}

	let startTime: dayjs.Dayjs | null = null,
		endTime: dayjs.Dayjs | null = null

	for (const s of specific) {
		const { year, month, day } = s.date

		for (const period of s.periods) {
			const openTime = dayjs.tz(
				`${year}-${month}-${day}T${period.open.time}`,
				timeZone
			)

			let closeTime = dayjs.tz(
				`${year}-${month}-${day}T${period.close.time}`,
				timeZone
			)

			// Adjust closeTime by subtracting 1 minute and 1 day (if after midnight)
			closeTime = closeTime.subtract(1, 'minute')
			if (closeTime.isBefore(openTime)) {
				closeTime = closeTime.add(1, 'day')
			}

			if (!startTime || openTime.isBefore(startTime)) {
				startTime = openTime
			}
			if (!endTime || closeTime.isAfter(endTime)) {
				endTime = closeTime
			}
		}
	}

	return {
		startTime: startTime?.toDate(),
		endTime: endTime?.toDate()
	}
}

/**
 * Convert Time portion of date to time string HH:MM
 */
export function date2TimeString(dateOrStr: Date | string, timeZone: string = TIMEZONE): string {
	const d = typeof dateOrStr === 'string' ? new Date(dateOrStr) : dateOrStr,
		date = dayjs(d).tz(timeZone),
		hour = date.hour().toString().padStart(2, '0'),
		minute = date.minute().toString().padStart(2, '0')

	return `${hour}:${minute}`
}

/**
 * Sort hours by ascending order of open (Sunday first)
 */
export function sortHours(periods: Period[]): Period[] {
	return periods.sort((a, b) =>
		(a.open.day - b.open.day === -6 ? 0 : a.open.day - b.open.day) 	// Sort Sunday before Monday
		|| Time.value(a.open.time) - Time.value(b.open.time))
}

/**
 * Convert a specific date schedule to Hours specific
 */
export function scheduleToHours(schedule: Schedule[]): Hours {
	return schedule.reduce((hours, { date, open: time }) => {
		const d = new Date(date),
			year = d.getFullYear(),
			month = d.getMonth() + 1,
			day = d.getDate(),
			dow = d.getDay() || 7,
			periods = time.map(({ start, end }) => ({
				open: { day: dow, time: start },
				close: { day: dow, time: end }
			}))

		hours.specific?.push({
			date: { year, month, day },
			periods,
		})

		return hours
	}, { specific: [], periods: [] } as Hours)
}

// ---- Periods

/**
 * Combine list of Periods (same day) into one
 * 	- dropping periods entirely enclosed by another, result sorted by open.time
 */
export function periodsMerge(list: Period[][]): Period[] {
	const merged = list.flat(),
		trimmed: Period[] = []

	for (let i = 0; i < merged.length; i++) {
		const { open, close } = merged[i],
			{ day } = open,
			sameDay = (period: Period) => period.open.day === day,
			enclosed = periodEnclose([
				...merged.slice(0, i).filter(sameDay),
				...merged.slice(i + 1).filter(sameDay)
			], open, close)

		if ((!enclosed.length || exists(enclosed, merged[i])) && !exists(trimmed, merged[i])) {
			trimmed.push(merged[i])
		}
	}

	return sortHours(trimmed)

	function exists(list: Period[], period: Period): boolean {
		const { open: { day, time: openTime }, close: { time: closeTime } } = period

		return list.some(({ open, close }) => open.day === day && open.time === openTime && close.time === closeTime)
	}
}

/**
 * Union of two list of Periods (omits 'busy' for now), result sorted by open.time
 */
export function periodsUnion(a: Period[], b: Period[]): Period[] {
	// Group periods by day
	const periodsByDay: Record<number, Period[]> = {}

	// Add all periods from a to the groups
	for (const period of a) {
		const day = period.open.day
		if (!periodsByDay[day]) {
			periodsByDay[day] = []
		}
		periodsByDay[day].push(cloneDeep(period))
	}

	// Add all periods from b to the groups
	for (const period of b) {
		const day = period.open.day
		if (!periodsByDay[day]) {
			periodsByDay[day] = []
		}
		periodsByDay[day].push(cloneDeep(period))
	}

	// Process each day separately
	const result: Period[] = []

	for (const day in periodsByDay) {
		const periodsForDay = periodsByDay[day]
		const unionForDay: Period[] = []

		// Process each period for this day
		for (const period of periodsForDay) {
			const { open, close } = period
			const intersect = periodIntersects(unionForDay, open, close)

			if (intersect.length) {
				// Merge with existing periods
				const merged = earliestOpenLatestClose([ ...intersect, period ])

				// Remove the intersecting periods
				for (const p of intersect) {
					const index = unionForDay.indexOf(p)
					if (index !== -1) {
						unionForDay.splice(index, 1)
					}
				}

				// Add the merged period
				unionForDay.push(merged)
			}
			else {
				// Add as a new period
				unionForDay.push(period)
			}
		}

		// Add the union for this day to the result
		result.push(...unionForDay)
	}

	return sortHours(result)
}

/**
 * Difference of two list of Periods: a - b   (omits 'busy' for now)
 */
export function periodsDifference(periodsA: Period[], periodsB: Period[]): Period[] {
	const a = cloneDeep(periodsA),
		b = cloneDeep(periodsB),
		difference: Period[] = []

	if (!b.length) return a

	for (const period of a) {
		const { open, close } = period,
			{ open: openB, close: closeB } = b[0],
			{ day } = openB,
			{ openTime, closeTime } = timeOfDay(open, close, day),
			{ openTime: start, closeTime: end } = timeOfDay(openB, closeB, day)

		if (closeTime < start || openTime > end) {		// no overlap
			difference.push(period)
			continue
		}

		const intersectB = periodIntersects(b, open, close)

		let empty = false

		for (const intersect of intersectB) {
			const { openTime: openTimeB, closeTime: closeTimeB } = timeOfDay(intersect.open, intersect.close, day)

			if (day <= open.day && openTime >= openTimeB) {
				if (closeTime <= closeTimeB) {
					empty = true
					break	// 'zero', B enclose A
				}

				open.time = intersect.close.time
			}
			else {
				if (closeTime > closeTimeB) {		// A enclose B, fragment
					const fragment = {
						open: { ...period.open },
						close: { ...period.close },
					}

					if (fragment.open.time !== intersect.open.time) {
						fragment.close.time = intersect.open.time
						difference.push(fragment)
					}

					open.time = intersect.close.time
				}
				else if (open.time === intersect.open.time) {
					empty = true
					break	// 'zero', A enclose B
				}
				else {
					close.time = intersect.open.time
				}
			}
		}

		if (!empty) {
			if (period.close.time === HOUR_24) period.close.time = HOUR_0		// handle 24 hour period
			difference.push(period)
		}
	}

	return sortHours(difference)
}

/**
 * Merge a specific date into an array of specific dates
 * - If the date already exists, merge the periods using periodsUnion
 * - If the date doesn't exist, add it (with a deep clone to avoid reference issues)
 */
export function mergeSpecificDate(specificDates: Hours['specific'] = [], newDate: NonNullable<Hours['specific']>[0]): NonNullable<Hours['specific']> {
	if (!specificDates) return [ cloneDeep(newDate) ]

	const result = cloneDeep(specificDates)
	const { date } = newDate

	// Find if the date already exists
	const existingIndex = result.findIndex((item: { date: YearMonthDay }) =>
		(item.date.year === date.year || item.date.year === 0 || date.year === 0)
		&& (item.date.month === date.month || item.date.month === 0 || date.month === 0)
		&& (item.date.day === date.day || item.date.day === 0 || date.day === 0)
	)

	if (existingIndex >= 0) {
		// If date exists, merge periods using periodsUnion
		const existing = result[existingIndex]
		const mergedPeriods = periodsUnion(
			existing.periods.map((p: TimePeriod) => ({
				open: { day: 1, time: p.open.time },
				close: { day: 1, time: p.close.time },
				...(p.busy ? { busy: { day: 1, time: p.busy.time } } : {})
			})),
			newDate.periods.map((p: TimePeriod) => ({
				open: { day: 1, time: p.open.time },
				close: { day: 1, time: p.close.time },
				...(p.busy ? { busy: { day: 1, time: p.busy.time } } : {})
			}))
		)

		// Convert back to TimePeriod[]
		existing.periods = mergedPeriods.map((p: Period) => {
			const timePeriod: TimePeriod = {
				open: { time: p.open.time },
				close: { time: p.close.time }
			}

			if (p.busy) {
				timePeriod.busy = { time: p.busy.time }
			}

			return timePeriod
		})
	}
	else {
		// If date doesn't exist, add it (with a deep clone)
		result.push(cloneDeep(newDate))
	}

	return result
}

/**
 * Aggregate multiple hours objects into a combined hours object
 * - Combines regular periods using periodsUnion
 * - Handles specific date overrides using mergeSpecificDate
 *
 * This implementation optimizes performance by:
 * - Validating inputs to handle malformed data gracefully
 * - Batching period merges to reduce deep cloning operations
 * - Processing specific dates more efficiently
 * - Using early returns for common edge cases
 */
export function aggregateHours(hoursArray: { hours: Hours }[]): Hours {
	// Early return for empty or invalid input
	if (!hoursArray || !Array.isArray(hoursArray) || !hoursArray.length) {
		return { periods: [], specific: [] }
	}

	// Create a new result object with empty arrays
	const result: Hours = {
		periods: [],
		specific: []
	}

	// Collect all valid periods first
	const allValidPeriods: Period[][] = []
	const allValidSpecificDates: NonNullable<Hours['specific']> = []

	// First pass: collect all valid data without merging
	for (const item of hoursArray) {
		// Skip invalid items
		if (!item || typeof item !== 'object' || !item.hours) {
			continue
		}

		const { hours } = item

		// Collect periods if they exist and are valid
		if (hours.periods && Array.isArray(hours.periods) && hours.periods.length > 0) {
			allValidPeriods.push(hours.periods)
		}

		// Collect specific dates if they exist and are valid
		if (hours.specific && Array.isArray(hours.specific) && hours.specific.length > 0) {
			for (const specificDate of hours.specific) {
				// Validate the specific date structure
				if (specificDate && specificDate.date && Array.isArray(specificDate.periods)) {
					allValidSpecificDates.push(specificDate)
				}
			}
		}
	}

	// Process periods: if we have any valid periods, merge them all at once
	if (allValidPeriods.length > 0) {
		if (allValidPeriods.length === 1) {
			// If only one set of periods, just clone it
			result.periods = cloneDeep(allValidPeriods[0])
		} else {
			// Merge all periods in batches to reduce the number of union operations
			let mergedPeriods: Period[] = cloneDeep(allValidPeriods[0])

			for (let i = 1; i < allValidPeriods.length; i++) {
				mergedPeriods = periodsUnion(mergedPeriods, allValidPeriods[i])
			}

			result.periods = mergedPeriods
		}
	}

	// Process specific dates: merge them all
	if (allValidSpecificDates.length > 0) {
		let specificDates: NonNullable<Hours['specific']> = []

		for (const specificDate of allValidSpecificDates) {
			specificDates = mergeSpecificDate(specificDates, specificDate)
		}

		result.specific = specificDates
	}

	return result
}

export default {
	hoursFor,
	isLateNight,
	within,
	isOpen,
	nextOpen,
	startEndOf,
	date2TimeString,
	sortHours,
	scheduleToHours,
	mergeSpecificDate,
	aggregateHours,

	periodsMerge,
	periodsUnion,
	periodsDifference,
}

// ----  Private functions  ----

/**
 * Get value of time (for comparison)
 */
function value(date: dayjs.Dayjs): number {
	return date.hour() * 100 + date.minute()
}

function after(open: DayTime, close: DayTime, start: number, end: number, dow: number): boolean {
	const time = timeOfDay(open, close, dow),
		{ openTime, closeTime } = time

	if (!openTime && !closeTime) return false

	return closeTime >= end && openTime >= start
}

/**
 * Get value of open & close time (for comparison), considering late night hours & day
 * day 1, 8pm - 8am
 * 	 > dow 7: no intersection
 * 	 > dow 1: 2000 - 3200    (today)
 * 	 > dow 2: 0000 - 0800    (yesterday)
 * 	 > dow 3: no intersection
 */
function timeOfDay(open: DayTime, close: DayTime, dow: number): { openTime: number, closeTime: number } {
	const day = (open.day === 7 && dow === 1) ? 0 : open.day

	if (Math.abs(day - dow) > 1) return { openTime: 0, closeTime: 0 }		// no intersection

	const today = open.day === dow,
		yesterday = day === (dow - 1),
		offset = Math.max(0, 2400 * (day - dow)),		// offset for coming days
		openVal = Time.value(open.time),
		closeVal = Time.value(close.time),
		lateNight = today && (isLateNight(open, close) || (!!openVal && !closeVal))
			? 2400
			: 0,

		openTime = yesterday ? 0 : (openVal + offset),
		closeTime = Time.value(close.time) + offset + lateNight

	return { openTime, closeTime }
}

function periodIntersects(periods: Period[], open: DayTime, close: DayTime) {
	return periods.filter(period => {
		const { day } = period.open,
			{ openTime, closeTime } = timeOfDay(open, close, day),
			{ openTime: periodOpenTime, closeTime: periodCloseTime } = timeOfDay(period.open, period.close, day)

		return periodOpenTime <= closeTime && periodCloseTime >= openTime
	})
}

function periodEnclose(periods: Period[], open: DayTime, close: DayTime) {
	return periods.filter(period => {
		const { day } = period.open,
			{ openTime, closeTime } = timeOfDay(open, close, day),
			{ openTime: periodOpenTime, closeTime: periodCloseTime } = timeOfDay(period.open, period.close, day)

		return periodOpenTime <= openTime && periodCloseTime >= closeTime
	})
}

function earliestOpenLatestClose(periods: Period[]): Period {
	const [ first ] = periods
	const result: Period = {
		open: { ...first.open },
		close: { ...first.close }
	}

	// Preserve busy property from the first period
	if (first.busy) {
		result.busy = { ...first.busy }
	}

	for (let i = 0; i < periods.length; i++) {
		const p = periods[i],
			{ openTime: pOpenTime, closeTime: pCloseTime } = timeOfDay(p.open, p.close, p.open.day),
			{ openTime, closeTime } = timeOfDay(result.open, result.close, p.open.day)

		if (pOpenTime <= openTime) result.open.time = p.open.time
		if (pCloseTime >= closeTime) result.close.time = p.close.time
	}

	return result
}
