import { countries as Countries } from 'country-data'
import {
	parsePhoneNumber as parsePhone,
	PhoneNumber,
	CountryCode,
	CountryCallingCode,
	NationalNumber,
	E164Number
} from 'libphonenumber-js/max'

export enum LineType {
	FIXED_LINE = 'fixedLine',
	MOBILE = 'mobile',
	FIXED_LINE_OR_MOBILE = 'fixedLine_OR_mobile',
	TOLL_FREE = 'tollFree',
	PREMIUM_RATE = 'premiumRate',
	SHARED_COST = 'sharedCost',
	VOIP = 'voip',
	PERSONAL_NUMBER = 'personalNumber',
	PAGER = 'pager',
	UAN = 'UAN',
	VOICEMAIL = 'voicemail',
	UNKNOWN = 'unknown',
}

type LineKeys = keyof typeof LineType

const { MOBILE, UNKNOWN } = LineType,
	HK_CLASS2_PREFIX = [ '58' ]  // HK IP Telephony Service - Class 2 service: https://www.ofca.gov.hk/en/consumer_focus/galley/information_leaflets/index_id_15.html

export type ParsedPhone = {
	lineType?: typeof LineType[LineKeys],
	valid: boolean,
	possible: boolean,
	regionCode?: CountryCode,
	countryCode?: CountryCallingCode | string,
	nationalNumber?: NationalNumber,
	formattedNational?: string,
	number?: E164Number,
	fullNumber: string,
	isMobile?: boolean,
	isValidMobile?: boolean,
	isPossibleMobile?: boolean,
	international?: string
}

// TODO Functions from LB3
// parsePhone
// parseFullNumber

/**
 * @param phoneNumber
 * @param country - either country calling code (65) OR ISO 3166-1 alpha-2 (SG)
 * @param clean
 */
export function parsePhoneNumber(phoneNumber: number | string, country: string | number = '', clean: boolean = true): ParsedPhone {
	const ctryCode = isNaN(Number(country))
			? countryCode(String(country))
			: String(country),
		phone = clean
			? cleanup(phoneNumber)
			: String(phoneNumber).trim().replace(/^\+/, ''), // or remove prefix '+', parse will auto-add
		countryCodes = !ctryCode
			? [ '' ]
			: phone.startsWith(ctryCode)
				? [ '', ctryCode ] // parse without country code first if phone is a possible full number
				: [ ctryCode, '' ]

	let parsed
	for (let i = 0; i < countryCodes.length; i++) {
		try {
			parsed = parse(parseInt(`${countryCodes[i]}${phone}`))
			if (parsed.isMobile) break
		}
		catch (err) {
			continue
		}
	}

	if (parsed) {
		// Handle duplicated country code case (usually due to user key in country code during registration)
		const { valid, nationalNumber = '', countryCode = '' } = parsed
		if (valid === false && countryCode && nationalNumber.startsWith(countryCode)) {
			const reParsed = parse(nationalNumber)
			if (reParsed.valid) {
				parsed = reParsed
			}
		}
	}
	else {
		parsed = {
			valid: false,
			possible: false,
			isMobile: false,
			isPossibleMobile: false,
			fullNumber: `${ctryCode}${phone}`,
			countryCode: ctryCode,
		}
	}

	return parsed
}

export function countryCode(country: string = ''): string {
	if (!country) return ''

	const alpha2 = country.toUpperCase(),
		[ code = '' ] = Countries[alpha2]?.countryCallingCodes || []	// [ +65 ]

	return code.replace(/^\+/, '')
}

exports.parseFullNumber = parse

function cleanup(phoneNumber: number | string): string {
	let phone = String(phoneNumber)
	phone = phone.replace(/\s+/g, '')		// remove space from phoneNumber
	phone = phone.replace(/[^0-9]/g, '')	// remove non-numeric characters from phoneNumber
	return phone
}

function parse(cleanedNumber: number | string) {
	const parsed = parsePhone('+' + cleanedNumber)
	return buildResult(parsed)
}

function buildResult(parsed?: PhoneNumber): ParsedPhone {
	if (!parsed) return { valid: false, possible: false } as ParsedPhone

	const parsedType = parsed.getType(),
		R: ParsedPhone = {
			lineType: parsedType ? LineType[parsedType] : UNKNOWN,
			valid: parsed.isValid(),
			possible: parsed.isPossible(),
			regionCode: parsed.country,
			countryCode: parsed.countryCallingCode,
			nationalNumber: parsed.nationalNumber,
			formattedNational: parsed.formatNational(),
			number: parsed.number,
			fullNumber: parsed.number.replace('+', ''),
		}
	R.isMobile = R.lineType?.includes('mobile')
	R.isValidMobile = R.valid && R.isMobile || isHKClass2Number(R)
	R.isPossibleMobile = R.possible && (R.isMobile || R.lineType === UNKNOWN)
	R.international = R.isPossibleMobile ? parsed.formatInternational() : R.number
	return R
}

function isHKClass2Number(phone: ParsedPhone): boolean {
	const { nationalNumber } = phone
	if (phone.countryCode === '852' && nationalNumber?.length === 8 && HK_CLASS2_PREFIX.find(prefix => nationalNumber.startsWith(prefix))) {
		phone.lineType = MOBILE
		phone.isMobile = true
		phone.valid = true
		return true
	}
	return !!phone.isValidMobile
}

export default {
	parsePhoneNumber,
	countryCode,
	parseFullNumber: parse
}
