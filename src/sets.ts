
export function intersection<T>(sets: Set<T>[]): Set<any> {
	if (sets.length <= 1) return sets[0]

	const set = new Set()
	sets.sort((a: Set<T>, b: Set<T>) => a.size - b.size)

	sets[0].forEach(elem => {
		let allMatch = true

		for (let i = 1; i < sets.length; i++) {
			if (!sets[`${i}`].has(elem)) {
				allMatch = false
				break
			}
		}
		if (allMatch) set.add(elem)
	})
	return set
}

export function union<T>(sets: Set<T>[]): Set<any> {
	const set = new Set()

	sets.forEach(aSet => {
		aSet.forEach(elem => set.add(elem))
	})
	return set
}

export function difference<T>(a: Set<T>, sets: Set<T>[]): Set<T> {
	const set = new Set(a)

	for (let i = 0; i < sets.length; i++) {
		sets[`${i}`].forEach(elem => set.delete(elem))
	}
	return set
}

export function symmetricDifference<T>(a: Set<T>, b: Set<T>): Set<T> {
	const set = new Set(a)

	b.forEach(elem => {
		set.has(elem) ? set.delete(elem) : set.add(elem)
	})
	return set
}

export default {
	intersection,
	union,
	difference,
	symmetricDifference,
}
