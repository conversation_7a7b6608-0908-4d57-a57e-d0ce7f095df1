import { parseOneAddress } from 'email-addresses'

export { default as isEmail } from 'validator/lib/isEmail'

export enum EmailType {
	HOME = 'home',
	WORK = 'work',
	OTHERS = 'others'
}

const { HOME, WORK, OTHERS } = EmailType,
	PERSONAL_DOMAINS = [
		'gmail', 'hotmail', 'live', 'outlook', 'msn', 'icloud', 'mac', 'yahoo', 'aol', 'mail', 'singnet', 'qq',
	]

export function emailAddressType(email: string) {
	const parts = parseOneAddress(email) || {},
		{ domain = '' } = parts as any || {}

	return domain ? (isPersonalDomain(domain) ? HOME : WORK) : OTHERS
}

export function isPersonalEmail(email: string): boolean {
	const parts = parseOneAddress(email),
		{ domain = '' } = parts as any || {}

	return domain ? isPersonalDomain(domain) : false
}

// ---- Private Functions

function isPersonalDomain(domain: string): boolean {
	const [ secondLevel ] = domain.split('.')
	return PERSONAL_DOMAINS.indexOf(secondLevel) >= 0
}

export default {
	isPersonalEmail,
	emailAddressType,
	parseEmailAddress: parseOneAddress
}
